-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local _design = TS.import(script, game:GetService("ReplicatedStorage"), "core", "design")
local COLORS = _design.COLORS
local BORDER_RADIUS = _design.BORDER_RADIUS
local useZIndex = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "layout", "useZIndex").useZIndex
local function AutoDockFrame(props)
	local dockPosition = props.dockPosition or "TopRight"
	local _condition = props.margin
	if _condition == nil then
		_condition = 20
	end
	local margin = _condition
	local _condition_1 = props.padding
	if _condition_1 == nil then
		_condition_1 = 12
	end
	local padding = _condition_1
	local _condition_2 = props.backgroundColor
	if _condition_2 == nil then
		_condition_2 = COLORS.bg.surface
	end
	local backgroundColor = _condition_2
	local _condition_3 = props.backgroundTransparency
	if _condition_3 == nil then
		_condition_3 = 0.1
	end
	local backgroundTransparency = _condition_3
	local size = props.size or UDim2.new(0, 300, 0, 400)
	-- Use automatic Z-Index management
	local _exp = props.elementId
	local _condition_4 = props.bringToFrontOnMount
	if _condition_4 == nil then
		_condition_4 = true
	end
	local _binding = useZIndex(_exp, _condition_4)
	local zIndex = _binding.zIndex
	local _condition_5 = props.zIndex
	if _condition_5 == nil then
		_condition_5 = zIndex
	end
	local finalZIndex = _condition_5
	-- Calculate position and anchor based on dock position
	local getPositionAndAnchor = function()
		repeat
			if dockPosition == "TopLeft" then
				return {
					position = UDim2.new(0, margin, 0, margin),
					anchorPoint = Vector2.new(0, 0),
				}
			end
			if dockPosition == "TopRight" then
				return {
					position = UDim2.new(1, -margin, 0, margin),
					anchorPoint = Vector2.new(1, 0),
				}
			end
			if dockPosition == "BottomLeft" then
				return {
					position = UDim2.new(0, margin, 1, -margin),
					anchorPoint = Vector2.new(0, 1),
				}
			end
			if dockPosition == "BottomRight" then
				return {
					position = UDim2.new(1, -margin, 1, -margin),
					anchorPoint = Vector2.new(1, 1),
				}
			end
			if dockPosition == "Center" then
				return {
					position = UDim2.new(0.5, 0, 0.5, 0),
					anchorPoint = Vector2.new(0.5, 0.5),
				}
			end
			return {
				position = UDim2.new(1, -margin, 0, margin),
				anchorPoint = Vector2.new(1, 0),
			}
		until true
	end
	local _binding_1 = getPositionAndAnchor()
	local position = _binding_1.position
	local anchorPoint = _binding_1.anchorPoint
	return React.createElement("frame", {
		BackgroundColor3 = Color3.fromHex(backgroundColor),
		BackgroundTransparency = backgroundTransparency,
		Size = size,
		Position = position,
		AnchorPoint = anchorPoint,
		BorderSizePixel = 0,
		ZIndex = finalZIndex,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, BORDER_RADIUS.md),
	}), React.createElement("uistroke", {
		Color = Color3.fromHex(COLORS.border.l2),
		Thickness = 1,
		Transparency = 0.3,
	}), React.createElement("uipadding", {
		PaddingTop = UDim.new(0, padding),
		PaddingBottom = UDim.new(0, padding),
		PaddingLeft = UDim.new(0, padding),
		PaddingRight = UDim.new(0, padding),
	}), props.children)
end
return {
	AutoDockFrame = AutoDockFrame,
}
