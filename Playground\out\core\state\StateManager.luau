-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local BaseService = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "BaseService").BaseService
local Result = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "types", "Result").Result
local StateError = TS.import(script, game:GetService("ReplicatedStorage"), "core", "state", "errors", "StateError").StateError
local StateManager
do
	local super = BaseService
	StateManager = setmetatable({}, {
		__tostring = function()
			return "StateManager"
		end,
		__index = super,
	})
	StateManager.__index = StateManager
	function StateManager.new(...)
		local self = setmetatable({}, StateManager)
		return self:constructor(...) or self
	end
	function StateManager:constructor(initialState, name)
		if name == nil then
			name = "StateManager"
		end
		super.constructor(self, name)
		self.subscriptions = {}
		self.middlewares = {}
		self.actionHistory = {}
		self.maxHistorySize = 100
		self.currentState = initialState
	end
	StateManager.onInitialize = TS.async(function(self)
		self:logInfo(`State manager initialized with initial state`)
		return Result:ok(nil)
	end)
	StateManager.onShutdown = TS.async(function(self)
		table.clear(self.subscriptions)
		self.middlewares = {}
		self.actionHistory = {}
		return Result:ok(nil)
	end)
	function StateManager:getState()
		return self:deepClone(self.currentState)
	end
	function StateManager:dispatch(action)
		local initResult = self:ensureInitialized()
		if initResult:isError() then
			return Result:err(StateError.new(initResult:getError().message))
		end
		local _exitType, _returns = TS.try(function()
			-- Apply middlewares
			local processedAction = action
			for _, middleware in self.middlewares do
				local middlewareResult = middleware:process(processedAction, self.currentState)
				if middlewareResult:isError() then
					return TS.TRY_RETURN, { Result:err(StateError.new(`Middleware error: {middlewareResult:getError().message}`)) }
				end
				processedAction = middlewareResult:getValue()
			end
			-- Apply the action
			local previousState = self:deepClone(self.currentState)
			local newState = processedAction.reducer(self.currentState, processedAction.payload)
			-- Validate new state
			if processedAction.validator then
				local validationResult = processedAction.validator(newState)
				if validationResult:isError() then
					return TS.TRY_RETURN, { Result:err(StateError.new(`State validation failed: {validationResult:getError().message}`)) }
				end
			end
			-- Update state
			self.currentState = newState
			-- Add to history
			self:addToHistory(processedAction)
			-- Notify subscribers
			self:notifySubscribers(previousState, newState)
			self:logDebug(`Action dispatched: {processedAction.type}`)
			return TS.TRY_RETURN, { Result:ok(self:deepClone(newState)) }
		end, function(error)
			return TS.TRY_RETURN, { Result:err(StateError.new(`Failed to dispatch action: {error}`)) }
		end)
		if _exitType then
			return unpack(_returns)
		end
	end
	function StateManager:subscribe(id, callback, selector)
		local initResult = self:ensureInitialized()
		if initResult:isError() then
			return Result:err(StateError.new(initResult:getError().message))
		end
		local _subscriptions = self.subscriptions
		local _id = id
		if _subscriptions[_id] ~= nil then
			return Result:err(StateError.new(`Subscription with ID '{id}' already exists`))
		end
		local subscription = {
			id = id,
			callback = callback,
			selector = selector,
			lastSelectedValue = if selector then selector(self.currentState) else nil,
		}
		local _subscriptions_1 = self.subscriptions
		local _id_1 = id
		_subscriptions_1[_id_1] = subscription
		-- Return unsubscribe function
		local unsubscribe = function()
			local _subscriptions_2 = self.subscriptions
			local _id_2 = id
			_subscriptions_2[_id_2] = nil
			self:logDebug(`Unsubscribed: {id}`)
		end
		self:logDebug(`Subscribed: {id}`)
		return Result:ok(unsubscribe)
	end
	function StateManager:addMiddleware(middleware)
		local initResult = self:ensureInitialized()
		if initResult:isError() then
			return Result:err(StateError.new(initResult:getError().message))
		end
		local _middlewares = self.middlewares
		local _middleware = middleware
		table.insert(_middlewares, _middleware)
		self:logDebug(`Middleware added: {middleware.name}`)
		return Result:ok(nil)
	end
	function StateManager:getActionHistory()
		local _array = {}
		local _length = #_array
		local _array_1 = self.actionHistory
		table.move(_array_1, 1, #_array_1, _length + 1, _array)
		return _array
	end
	function StateManager:canUndo()
		return #self.actionHistory > 0
	end
	function StateManager:undo()
		if not self:canUndo() then
			return Result:err(StateError.new("No actions to undo"))
		end
		local _exp = self.actionHistory
		-- ▼ Array.pop ▼
		local _length = #_exp
		local _result = _exp[_length]
		_exp[_length] = nil
		-- ▲ Array.pop ▲
		local lastAction = _result
		if lastAction.undoReducer then
			local _exitType, _returns = TS.try(function()
				local newState = lastAction.undoReducer(self.currentState)
				local previousState = self:deepClone(self.currentState)
				self.currentState = newState
				self:notifySubscribers(previousState, newState)
				self:logDebug(`Undid action: {lastAction.type}`)
				return TS.TRY_RETURN, { Result:ok(self:deepClone(newState)) }
			end, function(error)
				return TS.TRY_RETURN, { Result:err(StateError.new(`Failed to undo action: {error}`)) }
			end)
			if _exitType then
				return unpack(_returns)
			end
		else
			return Result:err(StateError.new(`Action '{lastAction.type}' is not undoable`))
		end
	end
	function StateManager:notifySubscribers(previousState, newState)
		for _, subscription in self.subscriptions do
			TS.try(function()
				if subscription.selector then
					local newSelectedValue = subscription.selector(newState)
					if newSelectedValue ~= subscription.lastSelectedValue then
						subscription.lastSelectedValue = newSelectedValue
						subscription.callback(previousState, newState)
					end
				else
					subscription.callback(previousState, newState)
				end
			end, function(error)
				self:logError(`Subscription callback error for {subscription.id}: {error}`)
			end)
		end
	end
	function StateManager:addToHistory(action)
		if action.undoReducer then
			local _actionHistory = self.actionHistory
			local _action = action
			table.insert(_actionHistory, _action)
			-- Limit history size
			if #self.actionHistory > self.maxHistorySize then
				table.remove(self.actionHistory, 1)
			end
		end
	end
	function StateManager:deepClone(obj)
		local _condition = obj == nil
		if not _condition then
			local _obj = obj
			_condition = typeof(_obj) ~= "table"
		end
		if _condition then
			return obj
		end
		local cloned = {}
		for key, value in pairs(obj) do
			cloned[key] = self:deepClone(value)
		end
		return cloned
	end
end
return {
	StateManager = StateManager,
}
