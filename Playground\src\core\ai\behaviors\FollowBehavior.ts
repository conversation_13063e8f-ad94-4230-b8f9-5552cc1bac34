import { AIBehavior } from "../interfaces/AIBehavior";
import { AIContext } from "../interfaces/AIContext";
import { AIBehaviorResult } from "../interfaces/AIBehaviorResult";
import { PositionHelper } from "../../helper/PositionHelper";

export class Follow<PERSON>ehavior implements AIBehavior {
	name = "Follow";
	priority = 5;

	canExecute(context: AIContext): boolean {
		if (!context.target || !context.targetPosition) return false;
		
		const distance = context.position.sub(context.targetPosition).Magnitude;
		return distance <= 30 && distance > 5;
	}

	execute(context: AIContext): AIBehaviorResult {
		if (!context.target || !context.targetPosition) {
			return { success: false, completed: true };
		}

		const distance = context.position.sub(context.targetPosition).Magnitude;
		
		this.moveTowards(context, context.targetPosition);
		PositionHelper.lookAt(context.entity, context.targetPosition);

		return { success: true, completed: distance <= 5 };
	}

	onEnter(context: AIContext): void {
		print(`🏃 ${context.entityId} is following target`);
	}

	private moveTowards(context: AIContext, targetPosition: Vector3): void {
		const clearTarget = PositionHelper.findClearPath(context.position, targetPosition, [context.entity]);

		if (context.entity.IsA("Model") && context.entity.PrimaryPart) {
			const humanoid = context.entity.FindFirstChild("Humanoid") as Humanoid;
			if (humanoid) {
				humanoid.MoveTo(clearTarget);
			} else {
				this.smoothMoveTo(context.entity.PrimaryPart, clearTarget, context.deltaTime);
			}
		} else if (context.entity.IsA("BasePart")) {
			this.smoothMoveTo(context.entity as BasePart, clearTarget, context.deltaTime);
		}
	}

	private smoothMoveTo(part: BasePart, targetPosition: Vector3, deltaTime: number): void {
		const currentPosition = part.Position;
		const direction = targetPosition.sub(currentPosition);
		const distance = direction.Magnitude;

		if (distance > 0.1) {
			const moveSpeed = 16;
			const maxMove = moveSpeed * deltaTime;
			const moveDistance = math.min(maxMove, distance);
			const newPosition = currentPosition.add(direction.Unit.mul(moveDistance));

			const lookDirection = direction.Unit;
			const newCFrame = CFrame.lookAt(newPosition, newPosition.add(lookDirection));
			part.CFrame = newCFrame;
		}
	}
}
