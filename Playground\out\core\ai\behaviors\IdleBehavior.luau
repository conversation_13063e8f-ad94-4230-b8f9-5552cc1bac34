-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local PositionHelper = TS.import(script, game:GetService("ReplicatedStorage"), "core", "helper", "PositionHelper").PositionHelper
local IdleBehavior
do
	IdleBehavior = setmetatable({}, {
		__tostring = function()
			return "IdleBehavior"
		end,
	})
	IdleBehavior.__index = IdleBehavior
	function IdleBehavior.new(...)
		local self = setmetatable({}, IdleBehavior)
		return self:constructor(...) or self
	end
	function IdleBehavior:constructor()
		self.name = "Idle"
		self.priority = 1
	end
	function IdleBehavior:canExecute(_context)
		return true
	end
	function IdleBehavior:execute(context)
		local _condition = context.blackboard.idleTime
		if not (_condition ~= 0 and _condition == _condition and _condition) then
			_condition = 0
		end
		local idleTime = _condition
		local newIdleTime = idleTime + context.deltaTime
		if newIdleTime > 3 then
			self:lookAround(context)
			context.blackboard.idleTime = 0
		else
			context.blackboard.idleTime = newIdleTime
		end
		return {
			success = true,
			completed = false,
		}
	end
	function IdleBehavior:onEnter(context)
		context.blackboard.idleTime = 0
		print(`😴 {context.entityId} is now idle`)
	end
	function IdleBehavior:lookAround(context)
		local randomAngle = math.random() * math.pi * 2
		local lookDirection = Vector3.new(math.cos(randomAngle), 0, math.sin(randomAngle))
		local _position = context.position
		local _arg0 = lookDirection * 10
		local lookPosition = _position + _arg0
		PositionHelper:lookAt(context.entity, lookPosition)
	end
end
return {
	IdleBehavior = IdleBehavior,
}
