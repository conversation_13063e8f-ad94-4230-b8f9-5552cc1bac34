-- Compiled with roblox-ts v3.0.0
local COLORS = {
	text = {
		main = "#F1F5F9",
		secondary = "#A0AEC0",
	},
	bg = {
		base = "#1F2023",
		secondary = "#18191B",
		surface = "#242529",
		["surface-hover"] = "#2E3033",
		avatar = "#3B3C40",
		badge = "#2F3237",
		hover = "#2E3033",
	},
	label = {
		text = "#BCCCDC",
		focus = "#E5EFF9",
		bg = "#2F3134",
		border = "#393C3F",
		muted = "#A7B0BF",
		hover = "#D4DCE3",
	},
	span = {
		default = "#F1F5F9",
		muted = "#A0AEC0",
		highlight = "#6FC2B2",
		subtle = "#9AA3B5",
		hover = "#FFFFFF",
	},
	border = {
		base = "#404040",
		l1 = "#4A4A4A",
		l2 = "#555555",
		l3 = "#666666",
		strong = "#777777",
		focus = "#5CB2BC",
	},
	primary = "#5CB2BC",
	["primary-dark"] = "#4AA3A3",
	success = "#AADB99",
	warning = "#E5A73D",
	error = "#DF6B75",
	info = "#5CB2BC",
	["progress-bg"] = "#16191C",
	["progress-fill"] = "#5CB2BC",
	["account-active"] = "#2C2D2F",
	["account-active-hover"] = "#2C2D2F",
	badge = {
		bg = "#2F3237",
		text = "#D7E6F4",
		border = "#3B3E45",
	},
	ring = {
		["focus-accent"] = "#5CB2BC",
	},
}
return {
	COLORS = COLORS,
}
