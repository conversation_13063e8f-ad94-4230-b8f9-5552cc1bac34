-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local _design = TS.import(script, game:GetService("ReplicatedStorage"), "core", "design")
local COLORS = _design.COLORS
local SIZES = _design.SIZES
local BORDER_RADIUS = _design.BORDER_RADIUS
local function IconButton(props)
	local hovered, setHovered = React.useState(false)
	-- Get the appropriate color based on state
	local _condition = props.backgroundColor
	if _condition == nil then
		_condition = (if props.disabled then COLORS.bg.secondary elseif hovered then COLORS.bg["surface-hover"] else COLORS.bg.surface)
	end
	local bgColorHSL = _condition
	local size = props.size or UDim2.new(0, SIZES.button.height, 0, SIZES.button.height)
	return React.createElement("textbutton", {
		Text = "",
		BackgroundColor3 = Color3.fromHex(bgColorHSL),
		Size = size,
		Position = props.position,
		AnchorPoint = props.anchorPoint,
		LayoutOrder = props.layoutOrder,
		AutoButtonColor = false,
		Event = {
			Activated = props.onClick,
			MouseEnter = function()
				return not props.disabled and setHovered(true)
			end,
			MouseLeave = function()
				return setHovered(false)
			end,
		},
		BorderSizePixel = 0,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, BORDER_RADIUS.md),
	}), React.createElement("uistroke", {
		Color = Color3.fromHex(COLORS.border.l2),
		Thickness = 1,
		Transparency = if props.disabled then 0.5 else 0,
	}), if (string.find(props.icon, "rbxassetid://")) ~= nil then (React.createElement("imagelabel", {
		Image = props.icon,
		ImageColor3 = props.iconColor or Color3.fromHex(if props.disabled then COLORS.text.secondary else COLORS.text.main),
		Size = UDim2.new(0.7, 0, 0.7, 0),
		Position = UDim2.new(0.5, 0, 0.5, 0),
		AnchorPoint = Vector2.new(0.5, 0.5),
		BackgroundTransparency = 1,
		ScaleType = Enum.ScaleType.Fit,
	})) else (React.createElement("textlabel", {
		Text = props.icon,
		TextColor3 = props.iconColor or Color3.fromHex(if props.disabled then COLORS.text.secondary else COLORS.text.main),
		Font = Enum.Font.SourceSansBold,
		TextSize = SIZES.fontSize + 4,
		Size = UDim2.new(1, 0, 1, 0),
		BackgroundTransparency = 1,
		TextXAlignment = Enum.TextXAlignment.Center,
		TextYAlignment = Enum.TextYAlignment.Center,
		TextScaled = false,
	})))
end
return {
	IconButton = IconButton,
}
