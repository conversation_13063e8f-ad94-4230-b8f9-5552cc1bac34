import * as React from "@rbxts/react";
import { useState } from "@rbxts/react";
import { Button, VerticalFrame } from "../../core";
import { WorldTestingPanel } from "./WorldTestingPanel";

export function ZIndexDemo() {
    const [showVoiceChat, setShowVoiceChat] = useState(false);
    const [showWorldTesting, setShowWorldTesting] = useState(false);

    return (
        <>
            {/* Control Panel */}
            <VerticalFrame
                size={new UDim2(0, 200, 0, 150)}
                position={new UDim2(0, 20, 0, 20)}
                spacing={8}
                padding={12}
                backgroundTransparency={0.1}
            >
                <Button
                    text="Toggle Voice Chat"
                    onClick={() => setShowVoiceChat(!showVoiceChat)}
                />
                
                <Button
                    text="Toggle World Testing"
                    onClick={() => setShowWorldTesting(!showWorldTesting)}
                />
                
                <Button
                    text="Show Both"
                    onClick={() => {
                        setShowVoiceChat(true);
                        setShowWorldTesting(true);
                    }}
                />
                
                <Button
                    text="Hide Both"
                    onClick={() => {
                        setShowVoiceChat(false);
                        setShowWorldTesting(false);
                    }}
                />
            </VerticalFrame>


            {/* World Testing Panel */}
            <WorldTestingPanel />
        </>
    );
}
