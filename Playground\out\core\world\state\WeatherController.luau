-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local _services = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services")
local Lighting = _services.Lighting
local Workspace = _services.Workspace
local EffectPartBuilder = TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "EffectPartBuilder").EffectPartBuilder
local EffectTweenBuilder = TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "EffectTweenBuilder").EffectTweenBuilder
--[[
	*
	 * WeatherController - Manages weather effects and atmospheric conditions
	 * Provides simple methods for changing weather states with smooth transitions
	 
]]
local WeatherController
do
	WeatherController = setmetatable({}, {
		__tostring = function()
			return "WeatherController"
		end,
	})
	WeatherController.__index = WeatherController
	function WeatherController.new(...)
		local self = setmetatable({}, WeatherController)
		return self:constructor(...) or self
	end
	function WeatherController:constructor()
		self.currentWeather = "clear"
		self.weatherEffects = {}
	end
	function WeatherController:getInstance()
		if not WeatherController.instance then
			WeatherController.instance = WeatherController.new()
		end
		return WeatherController.instance
	end
	function WeatherController:setWeather(options)
		self.currentWeather = options.type
		-- Clear existing weather effects
		self:clearWeatherEffects()
		local _exp = options.type
		repeat
			if _exp == "clear" then
				self:setClearWeather()
				break
			end
			if _exp == "rain" then
				local _self = self
				local _condition = options.intensity
				if not (_condition ~= 0 and _condition == _condition and _condition) then
					_condition = 0.5
				end
				_self:setRainWeather(_condition)
				break
			end
			if _exp == "heavy_rain" then
				local _self = self
				local _condition = options.intensity
				if not (_condition ~= 0 and _condition == _condition and _condition) then
					_condition = 0.8
				end
				_self:setRainWeather(_condition)
				break
			end
			if _exp == "snow" then
				local _self = self
				local _condition = options.intensity
				if not (_condition ~= 0 and _condition == _condition and _condition) then
					_condition = 0.5
				end
				_self:setSnowWeather(_condition)
				break
			end
			if _exp == "blizzard" then
				local _self = self
				local _condition = options.intensity
				if not (_condition ~= 0 and _condition == _condition and _condition) then
					_condition = 0.9
				end
				_self:setSnowWeather(_condition)
				break
			end
			if _exp == "storm" then
				local _self = self
				local _condition = options.intensity
				if not (_condition ~= 0 and _condition == _condition and _condition) then
					_condition = 0.7
				end
				_self:setStormWeather(_condition)
				break
			end
			if _exp == "thunderstorm" then
				local _self = self
				local _condition = options.intensity
				if not (_condition ~= 0 and _condition == _condition and _condition) then
					_condition = 0.8
				end
				_self:setThunderstormWeather(_condition)
				break
			end
			if _exp == "fog" then
				local _self = self
				local _condition = options.intensity
				if not (_condition ~= 0 and _condition == _condition and _condition) then
					_condition = 0.6
				end
				_self:setFogWeather(_condition)
				break
			end
			if _exp == "sandstorm" then
				local _self = self
				local _condition = options.intensity
				if not (_condition ~= 0 and _condition == _condition and _condition) then
					_condition = 0.7
				end
				_self:setSandstormWeather(_condition)
				break
			end
		until true
		print(`🌦️ Weather changed to: {options.type}`)
	end
	function WeatherController:getCurrentWeather()
		return self.currentWeather
	end
	function WeatherController:setClearWeather()
		-- Clear, bright day
		Lighting.FogEnd = 100000
		Lighting.FogStart = 0
		Lighting.FogColor = Color3.new(0.76, 0.76, 0.76)
		Lighting.Brightness = 1
		Lighting.Ambient = Color3.new(0.5, 0.5, 0.5)
	end
	function WeatherController:setRainWeather(intensity)
		-- Overcast and rainy
		Lighting.FogEnd = math.max(500, 2000 - (intensity * 1500))
		Lighting.FogStart = 0
		Lighting.FogColor = Color3.new(0.6, 0.6, 0.6)
		Lighting.Brightness = math.max(0.3, 0.8 - (intensity * 0.4))
		Lighting.Ambient = Color3.new(0.3, 0.3, 0.4)
		-- Create rain particle effect
		self:createRainEffect(intensity)
	end
	function WeatherController:setSnowWeather(intensity)
		-- Cold and snowy
		Lighting.FogEnd = math.max(300, 1500 - (intensity * 1200))
		Lighting.FogStart = 0
		Lighting.FogColor = Color3.new(0.8, 0.8, 0.9)
		Lighting.Brightness = math.max(0.4, 0.9 - (intensity * 0.3))
		Lighting.Ambient = Color3.new(0.4, 0.4, 0.5)
		-- Create snow particle effect
		self:createSnowEffect(intensity)
	end
	function WeatherController:setStormWeather(intensity)
		-- Dark storm clouds
		Lighting.FogEnd = math.max(200, 1000 - (intensity * 800))
		Lighting.FogStart = 0
		Lighting.FogColor = Color3.new(0.3, 0.3, 0.3)
		Lighting.Brightness = math.max(0.1, 0.5 - (intensity * 0.3))
		Lighting.Ambient = Color3.new(0.2, 0.2, 0.3)
		self:createStormEffect(intensity)
	end
	function WeatherController:setThunderstormWeather(intensity)
		-- Very dark with lightning
		Lighting.FogEnd = math.max(150, 800 - (intensity * 650))
		Lighting.FogStart = 0
		Lighting.FogColor = Color3.new(0.2, 0.2, 0.2)
		Lighting.Brightness = math.max(0.05, 0.3 - (intensity * 0.2))
		Lighting.Ambient = Color3.new(0.1, 0.1, 0.2)
		self:createThunderstormEffect(intensity)
	end
	function WeatherController:setFogWeather(intensity)
		-- Heavy fog
		Lighting.FogEnd = math.max(50, 300 - (intensity * 250))
		Lighting.FogStart = 0
		Lighting.FogColor = Color3.new(0.7, 0.7, 0.7)
		Lighting.Brightness = math.max(0.2, 0.6 - (intensity * 0.3))
		Lighting.Ambient = Color3.new(0.3, 0.3, 0.3)
	end
	function WeatherController:setSandstormWeather(intensity)
		-- Sandy, dusty conditions
		Lighting.FogEnd = math.max(100, 600 - (intensity * 500))
		Lighting.FogStart = 0
		Lighting.FogColor = Color3.new(0.8, 0.7, 0.5)
		Lighting.Brightness = math.max(0.3, 0.7 - (intensity * 0.3))
		Lighting.Ambient = Color3.new(0.4, 0.3, 0.2)
		self:createSandstormEffect(intensity)
	end
	function WeatherController:createRainEffect(intensity)
		-- Create rain particles using Core framework
		local rainEffect = EffectPartBuilder:create():size(Vector3.new(200, 1, 200)):position(Vector3.new(0, 100, 0)):transparency(1):spawn()
		rainEffect.Name = "RainEffect"
		self.weatherEffects.rain = rainEffect
		-- Create rain particle effect using ParticleHelper approach
		local numRainDrops = math.floor(intensity * 100)
		self:createRainParticles(rainEffect, intensity, numRainDrops)
		print(`💧 Rain effect created with intensity: {intensity}`)
	end
	function WeatherController:createRainParticles(parent, intensity, numDrops)
		-- Create continuous rain effect that falls from sky to ground
		local rainLoop
		rainLoop = function()
			if self.currentWeather ~= "rain" and self.currentWeather ~= "heavy_rain" then
				return nil
			end
			-- Create raindrops that start high and fall to ground level
			do
				local i = 0
				local _shouldIncrement = false
				while true do
					if _shouldIncrement then
						i += 1
					else
						_shouldIncrement = true
					end
					if not (i < math.floor(intensity * 20)) then
						break
					end
					local startHeight = 200
					local groundLevel = 0
					local raindrop = EffectPartBuilder:create():shape(Enum.PartType.Cylinder):size(Vector3.new(0.3, 3, 0.3)):color(Color3.new(0.6, 0.8, 1)):material(Enum.Material.Glass):transparency(0.1):position(Vector3.new(math.random(-300, 300), startHeight, math.random(-300, 300))):spawn()
					-- Rotate to look like falling rain
					local _cFrame = raindrop.CFrame
					local _arg0 = CFrame.Angles(math.rad(90), 0, 0)
					raindrop.CFrame = _cFrame * _arg0
					-- Animate falling to the ground using tween for smooth motion
					EffectTweenBuilder["for"](EffectTweenBuilder, raindrop):move(Vector3.new(raindrop.Position.X + math.random(-20, 20) * intensity, groundLevel - 10, raindrop.Position.Z + math.random(-10, 10) * intensity)):duration(2.5):easing(Enum.EasingStyle.Linear, Enum.EasingDirection.InOut):onComplete(function()
						if raindrop.Parent then
							raindrop:Destroy()
						end
					end):play()
					-- Backup cleanup
					task.delay(4, function()
						if raindrop.Parent then
							raindrop:Destroy()
						end
					end)
				end
			end
			-- Continue rain loop
			task.wait(0.3)
			rainLoop()
		end
		-- Start continuous rain
		task.spawn(function()
			return rainLoop()
		end)
	end
	function WeatherController:createSnowEffect(intensity)
		-- Create snow particles using Core framework
		local snowEffect = EffectPartBuilder:create():size(Vector3.new(200, 1, 200)):position(Vector3.new(0, 100, 0)):transparency(1):spawn()
		snowEffect.Name = "SnowEffect"
		self.weatherEffects.snow = snowEffect
		-- Create snow particle effect
		local numSnowflakes = math.floor(intensity * 80)
		self:createSnowParticles(snowEffect, intensity, numSnowflakes)
		print(`❄️ Snow effect created with intensity: {intensity}`)
	end
	function WeatherController:createSnowParticles(parent, intensity, numFlakes)
		-- Create continuous snow effect that falls from sky to ground
		local snowLoop
		snowLoop = function()
			if self.currentWeather ~= "snow" and self.currentWeather ~= "blizzard" then
				return nil
			end
			-- Create snowflakes that start high and fall to ground level
			do
				local i = 0
				local _shouldIncrement = false
				while true do
					if _shouldIncrement then
						i += 1
					else
						_shouldIncrement = true
					end
					if not (i < math.floor(intensity * 15)) then
						break
					end
					local startHeight = 180
					local groundLevel = 0
					local snowflake = EffectPartBuilder:create():shape(Enum.PartType.Ball):size(Vector3.new(0.8, 0.8, 0.8)):color(Color3.new(1, 1, 1)):material(Enum.Material.Neon):transparency(0.05):position(Vector3.new(math.random(-250, 250), startHeight, math.random(-250, 250))):spawn()
					-- Animate gentle falling to the ground with drift
					EffectTweenBuilder["for"](EffectTweenBuilder, snowflake):move(Vector3.new(snowflake.Position.X + math.random(-30, 30) * intensity, groundLevel - 5, snowflake.Position.Z + math.random(-30, 30) * intensity)):duration(5):easing(Enum.EasingStyle.Sine, Enum.EasingDirection.InOut):onComplete(function()
						if snowflake.Parent then
							snowflake:Destroy()
						end
					end):play()
					-- Backup cleanup
					task.delay(7, function()
						if snowflake.Parent then
							snowflake:Destroy()
						end
					end)
				end
			end
			-- Continue snow loop
			task.wait(0.6)
			snowLoop()
		end
		-- Start continuous snow
		task.spawn(function()
			return snowLoop()
		end)
	end
	function WeatherController:createStormEffect(intensity)
		-- Create storm particles using Core framework
		local stormEffect = EffectPartBuilder:create():size(Vector3.new(200, 1, 200)):position(Vector3.new(0, 100, 0)):transparency(1):spawn()
		stormEffect.Name = "StormEffect"
		self.weatherEffects.storm = stormEffect
		-- Create storm particle effect with wind and heavy rain
		local numStormParticles = math.floor(intensity * 120)
		self:createStormParticles(stormEffect, intensity, numStormParticles)
		print(`🌪️ Storm effect created with intensity: {intensity}`)
	end
	function WeatherController:createStormParticles(parent, intensity, numParticles)
		-- Create continuous storm effect
		local stormLoop
		stormLoop = function()
			if self.currentWeather ~= "storm" then
				return nil
			end
			-- Create intense storm particles that fall from sky to ground
			do
				local i = 0
				local _shouldIncrement = false
				while true do
					if _shouldIncrement then
						i += 1
					else
						_shouldIncrement = true
					end
					if not (i < math.floor(intensity * 25)) then
						break
					end
					local startHeight = 220
					local groundLevel = 0
					local stormParticle = EffectPartBuilder:create():shape(Enum.PartType.Cylinder):size(Vector3.new(0.4, 4, 0.4)):color(Color3.new(0.4, 0.5, 0.7)):material(Enum.Material.Glass):transparency(0.1):position(Vector3.new(math.random(-400, 400), startHeight, math.random(-400, 400))):spawn()
					-- Rotate for falling effect
					local _cFrame = stormParticle.CFrame
					local _arg0 = CFrame.Angles(math.rad(90 + math.random(-20, 20)), 0, math.random(-0.5, 0.5))
					stormParticle.CFrame = _cFrame * _arg0
					-- Animate violent falling with strong wind
					EffectTweenBuilder["for"](EffectTweenBuilder, stormParticle):move(Vector3.new(stormParticle.Position.X + math.random(-60, 60) * intensity, groundLevel - 15, stormParticle.Position.Z + math.random(-40, 40) * intensity)):duration(1.8):easing(Enum.EasingStyle.Quad, Enum.EasingDirection.In):onComplete(function()
						if stormParticle.Parent then
							stormParticle:Destroy()
						end
					end):play()
					-- Clean up quickly due to intensity
					task.delay(3, function()
						if stormParticle.Parent then
							stormParticle:Destroy()
						end
					end)
				end
			end
			-- Continue storm loop rapidly
			task.wait(0.3)
			stormLoop()
		end
		-- Start continuous storm
		task.spawn(function()
			return stormLoop()
		end)
	end
	function WeatherController:createThunderstormEffect(intensity)
		-- Create thunderstorm particles using Core framework
		local thunderEffect = EffectPartBuilder:create():size(Vector3.new(200, 1, 200)):position(Vector3.new(0, 100, 0)):transparency(1):spawn()
		thunderEffect.Name = "ThunderstormEffect"
		self.weatherEffects.thunderstorm = thunderEffect
		-- Create intense storm with lightning
		local numThunderParticles = math.floor(intensity * 150)
		self:createThunderstormParticles(thunderEffect, intensity, numThunderParticles)
		-- Start lightning effects
		self:startLightningEffects(intensity)
		print(`⛈️ Thunderstorm effect created with intensity: {intensity}`)
	end
	function WeatherController:createThunderstormParticles(parent, intensity, numParticles)
		-- Create continuous thunderstorm effect
		local thunderstormLoop
		thunderstormLoop = function()
			if self.currentWeather ~= "thunderstorm" then
				return nil
			end
			-- Heavy rain particles that fall from sky to ground
			do
				local i = 0
				local _shouldIncrement = false
				while true do
					if _shouldIncrement then
						i += 1
					else
						_shouldIncrement = true
					end
					if not (i < math.floor(intensity * 30)) then
						break
					end
					local startHeight = 240
					local groundLevel = 0
					local rainParticle = EffectPartBuilder:create():shape(Enum.PartType.Cylinder):size(Vector3.new(0.35, 4.5, 0.35)):color(Color3.new(0.3, 0.4, 0.6)):material(Enum.Material.Glass):transparency(0.05):position(Vector3.new(math.random(-500, 500), startHeight, math.random(-500, 500))):spawn()
					-- Rotate for falling effect
					local _cFrame = rainParticle.CFrame
					local _arg0 = CFrame.Angles(math.rad(90 + math.random(-30, 30)), 0, math.random(-1, 1))
					rainParticle.CFrame = _cFrame * _arg0
					-- Animate extremely fast falling with chaotic wind
					EffectTweenBuilder["for"](EffectTweenBuilder, rainParticle):move(Vector3.new(rainParticle.Position.X + math.random(-80, 80) * intensity, groundLevel - 20, rainParticle.Position.Z + math.random(-60, 60) * intensity)):duration(1.2):easing(Enum.EasingStyle.Quad, Enum.EasingDirection.In):onComplete(function()
						if rainParticle.Parent then
							rainParticle:Destroy()
						end
					end):play()
					-- Clean up
					task.delay(2.5, function()
						if rainParticle.Parent then
							rainParticle:Destroy()
						end
					end)
				end
			end
			-- Random lightning flash effects
			if math.random() < 0.3 then
				self:createLightningFlash(parent)
			end
			-- Continue thunderstorm loop
			task.wait(0.2)
			thunderstormLoop()
		end
		-- Start continuous thunderstorm
		task.spawn(function()
			return thunderstormLoop()
		end)
	end
	function WeatherController:createLightningFlash(parent)
		-- Create bright flash effect
		local _ = EffectPartBuilder:create():shape(Enum.PartType.Ball):size(Vector3.new(500, 500, 500)):color(Color3.new(1, 1, 0.8)):material(Enum.Material.Neon):transparency(0.7)
		local _position = parent.Position
		local _vector3 = Vector3.new(0, 100, 0)
		local flash = _:position(_position + _vector3):spawn()
		-- Flash effect - quick bright flash
		EffectTweenBuilder["for"](EffectTweenBuilder, flash):fade(0.95):duration(0.1):easing(Enum.EasingStyle.Quad, Enum.EasingDirection.Out):onComplete(function()
			if flash.Parent then
				flash:Destroy()
			end
		end):play()
	end
	function WeatherController:createSandstormEffect(intensity)
		-- Create sandstorm particles using Core framework
		local sandEffect = EffectPartBuilder:create():size(Vector3.new(200, 1, 200)):position(Vector3.new(0, 100, 0)):transparency(1):spawn()
		sandEffect.Name = "SandstormEffect"
		self.weatherEffects.sandstorm = sandEffect
		-- Create sand particle effect
		local numSandParticles = math.floor(intensity * 200)
		self:createSandParticles(sandEffect, intensity, numSandParticles)
		print(`🏜️ Sandstorm effect created with intensity: {intensity}`)
	end
	function WeatherController:createSandParticles(parent, intensity, numParticles)
		-- Create continuous sandstorm effect
		local sandstormLoop
		sandstormLoop = function()
			if self.currentWeather ~= "sandstorm" then
				return nil
			end
			-- Create swirling sand particles that move horizontally at ground level
			do
				local i = 0
				local _shouldIncrement = false
				while true do
					if _shouldIncrement then
						i += 1
					else
						_shouldIncrement = true
					end
					if not (i < math.floor(intensity * 20)) then
						break
					end
					local groundHeight = 10
					local sandParticle = EffectPartBuilder:create():shape(Enum.PartType.Ball):size(Vector3.new(1.2, 1.2, 1.2)):color(Color3.new(0.9, 0.8, 0.6)):material(Enum.Material.Sand):transparency(0.2):position(Vector3.new(math.random(-400, 400), groundHeight + math.random(-5, 15), math.random(-400, 400))):spawn()
					-- Horizontal swirling sandstorm motion
					EffectTweenBuilder["for"](EffectTweenBuilder, sandParticle):move(Vector3.new(sandParticle.Position.X + math.random(-100, 100) * intensity, groundHeight + math.random(-5, 25), sandParticle.Position.Z + math.random(-100, 100) * intensity)):duration(3):easing(Enum.EasingStyle.Sine, Enum.EasingDirection.InOut):onComplete(function()
						if sandParticle.Parent then
							sandParticle:Destroy()
						end
					end):play()
					-- Clean up faster for continuous effect
					task.delay(4, function()
						if sandParticle.Parent then
							sandParticle:Destroy()
						end
					end)
				end
			end
			-- Continue sandstorm loop
			task.wait(0.8)
			sandstormLoop()
		end
		-- Start continuous sandstorm
		task.spawn(function()
			return sandstormLoop()
		end)
	end
	function WeatherController:startLightningEffects(intensity)
		-- Lightning flash effect using Core framework
		local lightningInterval = math.max(2, 10 - (intensity * 8))
		local createLightningBolt = function()
			-- Create lightning bolt visual effect
			local lightningBolt = EffectPartBuilder:create():shape(Enum.PartType.Cylinder):size(Vector3.new(0.5, 100, 0.5)):color(Color3.new(1, 1, 0.8)):material(Enum.Material.Neon):transparency(0.1):position(Vector3.new(math.random(-50, 50), 50, math.random(-50, 50))):withLight(20, 10, Color3.new(1, 1, 0.8)):spawn()
			-- Flash effect with tween
			EffectTweenBuilder["for"](EffectTweenBuilder, lightningBolt):fade(1):duration(0.2):onComplete(function()
				if lightningBolt.Parent then
					lightningBolt:Destroy()
				end
			end):play()
			-- Play lightning sound
			local sound = Instance.new("Sound")
			sound.SoundId = "rbxasset://sounds/electronicpingshort.wav"
			sound.Volume = 0.5 * intensity
			sound.PlaybackSpeed = math.random(0.8, 1.2)
			sound.Parent = Workspace
			sound:Play()
			sound.Ended:Connect(function()
				return sound:Destroy()
			end)
		end
		local flashLightning
		flashLightning = function()
			-- Brief bright flash
			local originalBrightness = Lighting.Brightness
			-- Create lightning bolt
			createLightningBolt()
			-- Flash the sky using brightness method
			EffectTweenBuilder["for"](EffectTweenBuilder, Lighting):brightness(2):duration(0.1):onComplete(function()
				EffectTweenBuilder["for"](EffectTweenBuilder, Lighting):brightness(originalBrightness):duration(0.1):play()
			end):play()
			-- Schedule next lightning
			task.wait(lightningInterval + math.random() * 3)
			if self.currentWeather == "thunderstorm" then
				flashLightning()
			end
		end
		-- Start lightning effects
		task.spawn(function()
			return flashLightning()
		end)
	end
	function WeatherController:clearWeatherEffects()
		-- Remove all weather effect instances
		for key, effect in self.weatherEffects do
			if effect and effect.Parent then
				effect:Destroy()
			end
		end
		table.clear(self.weatherEffects)
	end
	function WeatherController:cleanup()
		self:clearWeatherEffects()
		self:setClearWeather()
	end
end
return {
	WeatherController = WeatherController,
}
