import { AIBehavior } from "../interfaces/AIBehavior";
import { AIContext } from "../interfaces/AIContext";
import { AIBehaviorResult } from "../interfaces/AIBehaviorResult";
export declare class FleeBehavior implements AIBehavior {
    name: string;
    priority: number;
    canExecute(context: AIContext): boolean;
    execute(context: AIContext): AIBehaviorResult;
    onEnter(context: AIContext): void;
    private getThreatPosition;
    private moveTowards;
}
