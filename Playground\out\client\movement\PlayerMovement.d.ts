export declare class PlayerMovement {
    private player;
    private character?;
    private humanoid?;
    private humanoidRootPart?;
    private connections;
    constructor();
    private setupCharacter;
    /**
     * Basic movement using Humanoid.WalkSpeed
     */
    setWalkSpeed(speed: number): void;
    /**
     * Make the player jump
     */
    jump(): void;
    /**
     * Move player to a specific position using Humanoid:MoveTo()
     */
    moveTo(position: Vector3): void;
    /**
     * Dash in a direction using BodyVelocity (temporary speed boost)
     */
    dash(direction: Vector3, power?: number, duration?: number): void;
    /**
     * Launch player upward (like a rocket jump)
     */
    launch(upwardForce?: number, forwardForce?: number): void;
    /**
     * Smooth movement to position using TweenService
     */
    smoothMoveTo(targetPosition: Vector3, duration?: number): void;
    /**
     * Teleport player instantly to position
     */
    teleport(position: Vector3): void;
    /**
     * Enable/disable player movement
     */
    setMovementEnabled(enabled: boolean): void;
    /**
     * Get current player velocity
     */
    getVelocity(): Vector3;
    /**
     * Check if player is moving
     */
    isMoving(): boolean;
    /**
     * Setup WASD movement override (custom movement system)
     */
    setupCustomMovement(speed?: number): void;
    /**
     * Restore default Roblox movement
     */
    restoreDefaultMovement(): void;
    /**
     * Cleanup when done
     */
    destroy(): void;
}
