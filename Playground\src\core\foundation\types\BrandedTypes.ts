declare const __brand: unique symbol;

export type Brand<T, TBrand> = T & { [__brand]: TBrand };

// Re-export Error type for convenience
export type { Error } from "./RobloxError";
export { createError } from "./RobloxError";

export type PlayerId = Brand<number, "PlayerId">;
export type EntityId = Brand<string, "EntityId">;
export type ServiceName = Brand<string, "ServiceName">;
export type EventName = Brand<string, "EventName">;
export type ComponentId = Brand<string, "ComponentId">;
export type AssetId = Brand<string, "AssetId">;
export type ConfigKey = Brand<string, "ConfigKey">;

export namespace BrandedTypes {
    export function playerId(id: number): PlayerId {
        return id as PlayerId;
    }

    export function entityId(id: string): EntityId {
        return id as EntityId;
    }

    export function serviceName(name: string): ServiceName {
        return name as ServiceName;
    }

    export function eventName(name: string): EventName {
        return name as EventName;
    }

    export function componentId(id: string): ComponentId {
        return id as ComponentId;
    }

    export function assetId(id: string): AssetId {
        return id as AssetId;
    }

    export function configKey(key: string): ConfigKey {
        return key as ConfigKey;
    }
}
