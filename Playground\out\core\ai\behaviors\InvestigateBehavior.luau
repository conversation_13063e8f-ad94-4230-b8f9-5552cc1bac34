-- Compiled with roblox-ts v3.0.0
local InvestigateBehavior
do
	InvestigateBehavior = setmetatable({}, {
		__tostring = function()
			return "InvestigateBehavior"
		end,
	})
	InvestigateBehavior.__index = InvestigateBehavior
	function InvestigateBehavior.new(...)
		local self = setmetatable({}, InvestigateBehavior)
		return self:constructor(...) or self
	end
	function InvestigateBehavior:constructor()
		self.name = "Investigate"
		self.priority = 4
	end
	function InvestigateBehavior:canExecute(context)
		local investigationPoint = context.blackboard.investigationPoint
		return investigationPoint ~= nil
	end
	function InvestigateBehavior:execute(context)
		local investigationPoint = context.blackboard.investigationPoint
		local distance = (context.position - investigationPoint).Magnitude
		if distance <= 3 then
			context.blackboard.investigationPoint = nil
			context.blackboard.investigationTime = 0
			return {
				success = true,
				completed = true,
			}
		end
		self:moveTowards(context, investigationPoint)
		return {
			success = true,
			completed = false,
		}
	end
	function InvestigateBehavior:onEnter(context)
		print(`🔍 {context.entityId} is investigating`)
	end
	function InvestigateBehavior:moveTowards(context, targetPosition)
		if context.entity:IsA("Model") and context.entity.PrimaryPart then
			local humanoid = context.entity:FindFirstChild("Humanoid")
			if humanoid then
				humanoid:MoveTo(targetPosition)
			end
		end
	end
end
return {
	InvestigateBehavior = InvestigateBehavior,
}
