export declare class EffectPartBuilder {
    private part;
    constructor();
    static create(): EffectPartBuilder;
    shape(shape: Enum.PartType): this;
    size(size: Vector3): this;
    color(color: Color3): this;
    material(material: Enum.Material): this;
    transparency(trans: number): this;
    position(pos: Vector3): this;
    cframe(cf: CFrame): this;
    withLight(range?: number, brightness?: number, color?: Color3): this;
    spawn(): Part;
}
