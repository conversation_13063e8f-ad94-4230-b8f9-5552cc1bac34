import { BaseService } from "../foundation/BaseService";
import { Result } from "../foundation/types/Result";
import { Error } from "../foundation/types/RobloxError";
import { StateError } from "./errors/StateError";
import { StateAction } from "./interfaces/StateAction";
import { StateMiddleware } from "./interfaces/StateMiddleware";
export declare class StateManager<TState> extends BaseService {
    private currentState;
    private subscriptions;
    private middlewares;
    private actionHistory;
    private maxHistorySize;
    constructor(initialState: TState, name?: string);
    protected onInitialize(): Promise<Result<void, Error>>;
    protected onShutdown(): Promise<Result<void, Error>>;
    getState(): TState;
    dispatch<TPayload>(action: StateAction<TState, TPayload>): Result<TState, StateError>;
    subscribe(id: string, callback: (previousState: TState, newState: TState) => void, selector?: (state: TState) => unknown): Result<() => void, StateError>;
    addMiddleware(middleware: StateMiddleware<TState>): Result<void, StateError>;
    getActionHistory(): StateAction<TState>[];
    canUndo(): boolean;
    undo(): Result<TState, StateError>;
    private notifySubscribers;
    private addToHistory;
    private deepClone;
}
