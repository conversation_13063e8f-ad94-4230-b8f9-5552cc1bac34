-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local AbilityBase = TS.import(script, script.Parent, "AbilityBase").AbilityBase
local Players = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services").Players
local RoomAbility
do
	local super = AbilityBase
	RoomAbility = setmetatable({}, {
		__tostring = function()
			return "RoomAbility"
		end,
		__index = super,
	})
	RoomAbility.__index = RoomAbility
	function RoomAbility.new(...)
		local self = setmetatable({}, RoomAbility)
		return self:constructor(...) or self
	end
	function RoomAbility:constructor()
		super.constructor(self, "ROOM", 8)
		self.cooldownEndTime = 0
	end
	function RoomAbility:isOnCooldown()
		return tick() < self.cooldownEndTime
	end
	function RoomAbility:startCooldown()
		self.cooldownEndTime = tick() + self:getCooldownTime()
	end
	function RoomAbility:activate()
		if self:isOnCooldown() then
			return nil
		end
		local player = Players.LocalPlayer
		local character = player.Character
		if not character then
			return nil
		end
		print(`🔵 {player.Name} activated Room ability!`)
		-- Add your Room ability logic here
		-- For now, just a simple effect
		local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
		if humanoidRootPart then
			-- Create a blue sphere effect
			local roomSphere = Instance.new("Part")
			roomSphere.Name = "RoomSphere"
			roomSphere.Shape = Enum.PartType.Ball
			roomSphere.Size = Vector3.new(20, 20, 20)
			roomSphere.Color = Color3.fromRGB(0, 100, 255)
			roomSphere.Material = Enum.Material.ForceField
			roomSphere.Transparency = 0.8
			roomSphere.CanCollide = false
			roomSphere.Anchored = true
			roomSphere.Position = humanoidRootPart.Position
			roomSphere.Parent = game.Workspace
			-- Remove after 3 seconds
			task.delay(3, function()
				if roomSphere.Parent then
					roomSphere:Destroy()
				end
			end)
		end
		self:startCooldown()
	end
end
return {
	RoomAbility = RoomAbility,
}
