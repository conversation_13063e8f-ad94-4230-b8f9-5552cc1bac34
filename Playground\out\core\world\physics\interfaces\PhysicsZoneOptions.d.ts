export interface PhysicsZoneOptions {
    center: Vector3;
    radius: number;
    zoneType: "gravity" | "force" | "barrier" | "teleport" | "slow" | "accelerate";
    intensity: number;
    duration?: number;
    affectedObjects: ObjectType[];
    visualEffect?: boolean;
    soundEffect?: string;
}
export interface GravityZoneOptions extends PhysicsZoneOptions {
    zoneType: "gravity";
    gravityDirection?: Vector3;
    gravityMultiplier: number;
}
export interface ForceZoneOptions extends PhysicsZoneOptions {
    zoneType: "force";
    forceDirection: Vector3;
    forceStrength: number;
    forceType: "constant" | "impulse" | "radial" | "vortex";
}
export interface BarrierZoneOptions extends PhysicsZoneOptions {
    zoneType: "barrier";
    barrierType: "wall" | "dome" | "cylinder";
    allowedObjects?: ObjectType[];
    bounceStrength?: number;
}
export type ObjectType = "players" | "parts" | "debris" | "projectiles" | "npcs";
export interface PhysicsZone {
    id: string;
    options: PhysicsZoneOptions;
    createdAt: number;
    expiresAt?: number;
    affectedObjects: Set<Instance>;
    zoneVisual?: Part;
}
export interface ForceApplication {
    target: Instance;
    forceType: "BodyVelocity" | "BodyPosition" | "BodyAngularVelocity";
    forceObject: Instance;
    originalValues?: Map<string, unknown>;
}
