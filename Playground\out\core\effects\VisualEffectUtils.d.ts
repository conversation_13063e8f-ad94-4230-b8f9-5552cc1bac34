export declare function cameraShake(intensity?: number, duration?: number): void;
export declare class <PERSON>ShakeHelper {
    private static activeShakes;
    static shake(intensity?: number, duration?: number, id?: string): void;
    static stopShake(id: string): void;
    static stopAllShakes(): void;
}
export declare class CrackEffectHelper {
    static createGroundCracks(centerPosition: Vector3, maxRadius?: number, numCracks?: number): void;
    static createAirCracks(position: Vector3, numCracks?: number): void;
    static createGlassCracks(position: Vector3, direction: Vector3, numMainCracks?: number, numWebCracks?: number): void;
    private static createGlassCrackLine;
}
export declare function createImpactFlash(pos: Vector3, size?: number, duration?: number): void;
export declare function createTrail(startPos: Vector3, endPos: Vector3, color: Color3, numSegments?: number): void;
