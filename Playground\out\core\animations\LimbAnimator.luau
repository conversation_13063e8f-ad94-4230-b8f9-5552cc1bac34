-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local TweenService = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services").TweenService
local AnimationBuilder = TS.import(script, game:GetService("ReplicatedStorage"), "core", "animations", "AnimationBuilder").AnimationBuilder
local getJoint = TS.import(script, game:GetService("ReplicatedStorage"), "core", "animations", "CharacterJointManager").getJoint
local LimbAnimator
do
	LimbAnimator = setmetatable({}, {
		__tostring = function()
			return "LimbAnimator"
		end,
	})
	LimbAnimator.__index = LimbAnimator
	function LimbAnimator.new(...)
		local self = setmetatable({}, LimbAnimator)
		return self:constructor(...) or self
	end
	function LimbAnimator:constructor(character)
		self.character = character
	end
	function LimbAnimator:forC<PERSON>cter(character)
		return LimbAnimator.new(character)
	end
	function LimbAnimator:moveLimb(limbName, relativeCFrame, duration, easingStyle, easingDirection)
		if duration == nil then
			duration = 0.3
		end
		if easingStyle == nil then
			easingStyle = Enum.EasingStyle.Quad
		end
		if easingDirection == nil then
			easingDirection = Enum.EasingDirection.Out
		end
		local joint = getJoint(self.character, limbName)
		if joint then
			AnimationBuilder:forJoint(joint):to(relativeCFrame):duration(duration):easing(easingStyle, easingDirection):play()
		end
	end
	function LimbAnimator:moveBodyPart(partName, relativeCFrame, duration, easingStyle, easingDirection)
		if duration == nil then
			duration = 0.3
		end
		if easingStyle == nil then
			easingStyle = Enum.EasingStyle.Quad
		end
		if easingDirection == nil then
			easingDirection = Enum.EasingDirection.Out
		end
		local joint = getJoint(self.character, partName)
		if joint then
			AnimationBuilder:forJoint(joint):to(relativeCFrame):duration(duration):easing(easingStyle, easingDirection):play()
		end
	end
	function LimbAnimator:lunge(directionVector, studs, duration, easingStyle, easingDirection)
		if studs == nil then
			studs = 5
		end
		if duration == nil then
			duration = 0.3
		end
		if easingStyle == nil then
			easingStyle = Enum.EasingStyle.Back
		end
		if easingDirection == nil then
			easingDirection = Enum.EasingDirection.Out
		end
		local humanoidRootPart = self.character:FindFirstChild("HumanoidRootPart")
		if humanoidRootPart then
			local originalCFrame = humanoidRootPart.CFrame
			local _unit = directionVector.Unit
			local _studs = studs
			local lungeVector = _unit * _studs
			TweenService:Create(humanoidRootPart, TweenInfo.new(duration, easingStyle, easingDirection), {
				CFrame = originalCFrame + lungeVector,
			}):Play()
		end
	end
end
return {
	LimbAnimator = LimbAnimator,
}
