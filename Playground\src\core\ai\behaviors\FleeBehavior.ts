import { AIBehavior } from "../interfaces/AIBehavior";
import { AIContext } from "../interfaces/AIContext";
import { AIBehaviorResult } from "../interfaces/AIBehaviorResult";

export class FleeBehavior implements AIBehavior {
	name = "Flee";
	priority = 8;

	canExecute(context: AIContext): boolean {
		const threat = context.blackboard.threat as Instance;
		return threat !== undefined;
	}

	execute(context: AIContext): AIBehaviorResult {
		const threat = context.blackboard.threat as Instance;
		if (!threat || !threat.Parent) {
			context.blackboard.threat = undefined;
			return { success: true, completed: true };
		}

		const threatPosition = this.getThreatPosition(threat);
		if (!threatPosition) {
			context.blackboard.threat = undefined;
			return { success: true, completed: true };
		}

		const fleeDirection = context.position.sub(threatPosition).Unit;
		const fleeTarget = context.position.add(fleeDirection.mul(30));

		this.moveTowards(context, fleeTarget);
		return { success: true, completed: false };
	}

	onEnter(context: AIContext): void {
		print(`😨 ${context.entityId} is fleeing from threat`);
	}

	private getThreatPosition(threat: Instance): Vector3 | undefined {
		if (threat.IsA("Model") && threat.PrimaryPart) {
			return threat.PrimaryPart.Position;
		} else if (threat.IsA("BasePart")) {
			return threat.Position;
		}
		return undefined;
	}

	private moveTowards(context: AIContext, targetPosition: Vector3): void {
		if (context.entity.IsA("Model") && context.entity.PrimaryPart) {
			const humanoid = context.entity.FindFirstChild("Humanoid") as Humanoid;
			if (humanoid) {
				humanoid.MoveTo(targetPosition);
			}
		}
	}
}
