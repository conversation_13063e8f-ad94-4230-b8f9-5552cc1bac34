import { Result } from "../foundation/types/Result";
import { Error } from "../foundation/types/RobloxError";
export interface ClientGameState {
    ui: {
        debugPanel: {
            isOpen: boolean;
            activeTab: string;
        };
        worldTestingPanel: {
            isOpen: boolean;
        };
        actionBar: {
            isVisible: boolean;
            abilities: ClientAbilitySlot[];
        };
        voiceChat: {
            isOpen: boolean;
            participants: VoiceChatParticipant[];
        };
    };
    player: {
        abilities: PlayerAbility[];
        cooldowns: Map<string, number>;
        position: Vector3;
        health: number;
        maxHealth: number;
    };
    world: {
        weather: string;
        timeOfDay: number;
        gravity: number;
    };
    network: {
        ping: number;
        isConnected: boolean;
        lastServerUpdate: number;
    };
}
export interface ClientAbilitySlot {
    id: string;
    name: string;
    icon: string;
    cooldown: number;
    maxCooldown: number;
    isActive: boolean;
}
export interface VoiceChatParticipant {
    userId: number;
    displayName: string;
    isTalking: boolean;
    isMuted: boolean;
}
export interface PlayerAbility {
    id: string;
    name: string;
    type: string;
    cooldown: number;
    damage?: number;
    range?: number;
}
export interface ClientStateAction<T = unknown> {
    type: string;
    payload: T;
}
export type ClientStateUpdater<T = unknown> = (state: ClientGameState, payload: T) => ClientGameState;
declare class ClientStateManager {
    private static instance?;
    private state;
    private listeners;
    private constructor();
    static getInstance(): ClientStateManager;
    private getInitialState;
    getState(): ClientGameState;
    dispatch<T>(action: ClientStateAction<T>, updater: ClientStateUpdater<T>): Result<ClientGameState, Error>;
    subscribe(listener: (state: ClientGameState) => void): () => void;
    updateUI<K extends keyof ClientGameState["ui"]>(section: K, updates: Partial<ClientGameState["ui"][K]>): Result<ClientGameState, Error>;
    updatePlayer<K extends keyof ClientGameState["player"]>(field: K, value: ClientGameState["player"][K]): Result<ClientGameState, Error>;
    updateWorld<K extends keyof ClientGameState["world"]>(field: K, value: ClientGameState["world"][K]): Result<ClientGameState, Error>;
}
export declare const ClientState: ClientStateManager;
export declare function useClientState(): ClientGameState;
export declare function useUIState<K extends keyof ClientGameState["ui"]>(section: K): ClientGameState["ui"][K];
export {};
