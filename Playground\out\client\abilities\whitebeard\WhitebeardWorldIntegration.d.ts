export declare class WhitebeardWorldIntegration {
    /**
     * Enhanced <PERSON>beard punch with full environmental destruction
     */
    static executeQuakePunch(player: Player, punchPosition: Vector3, punchType: "single" | "double"): void;
    /**
     * Room ability with spatial manipulation
     */
    static executeRoomAbility(player: Player, roomCenter: Vector3, roomRadius: number): void;
    /**
     * Ice Age ability with environmental freezing
     */
    static executeIceAge(player: Player, freezeCenter: Vector3, freezeRadius: number): void;
    /**
     * Create expanding shockwave visual effect
     */
    private static createShockwaveEffect;
    /**
     * Apply forces to objects from quake
     */
    private static applyQuakeForces;
    /**
     * Combo ability: Earthquake + Tsunami
     */
    static executeEarthquakeTsunami(player: Player, epicenter: Vector3, magnitude: number): void;
}
