import { AbilityBase } from "../AbilityBase";
import { Players } from "@rbxts/services";
import { onServerEvent, fireServer, Core, waitForEvent, ClientCore_Instance } from "../../../core";

import { createCameraShake } from "./effects/CameraShake";
import { createMapShockwave } from "./effects/ShockwaveEffects";
import { createQuakeSphere, createSequentialQuakeSpheres } from "./effects/SphereVisuals";
import { executeQuakePunch } from "./animations/PunchExecution";

interface WhitebeardEffectData {
    type: string;
    casterUserId: number;
    punchType: "single" | "double";
    position: Vector3;
    timestamp: number;
}

export class QuakeAbility extends AbilityBase {
    public quakeEffect?: Part;
    public leftQuakeEffect?: Part;
    public quakeConnection?: RBXScriptConnection;
    private cooldownEndTime = 0;
    private effectReplicationEvent?: RemoteEvent;
    private networkingInitialized = false;

    constructor() {
        super("QUAKE_PUNCH", 12);
        print("🥊 QuakeAbility: Constructor called");

        // Expose this instance globally for debugging purposes
        (_G as any)._quakeAbilityInstance = this;

        // Try to initialize networking immediately, but don't fail if it's not ready
        this.tryInitializeNetworking();

        // Set up a retry mechanism
        this.setupNetworkingRetry();
    }

    private tryInitializeNetworking(): void {
        if (this.networkingInitialized) return;

        const eventName = Core.eventName("WhitebeardEffectReplicate");

        // Check if the event is available
        if (ClientCore_Instance.isEventAvailable(eventName)) {
            print("✅ QuakeAbility: RemoteEvent available, initializing networking...");
            this.initializeNetworking();
        } else {
            print(`⏳ QuakeAbility: RemoteEvent '${eventName}' not available yet`);
        }
    }

    private setupNetworkingRetry(): void {
        // Retry every 2 seconds for up to 30 seconds
        task.spawn(() => {
            let attempts = 0;
            const maxAttempts = 15;

            while (attempts < maxAttempts && !this.networkingInitialized) {
                task.wait(2); // Wait 2 seconds
                attempts++;

                if (this.networkingInitialized) {
                    return;
                }

                print(`🔄 QuakeAbility: Retry attempt ${attempts}/${maxAttempts}`);
                this.tryInitializeNetworking();
            }

            if (!this.networkingInitialized) {
                warn("⚠️ QuakeAbility: Failed to initialize networking after 30 seconds");
            }
        });
    }

    private initializeNetworking(): void {
        if (this.networkingInitialized) return;

        // Listen for server-side effect replication using Core framework
        const result = onServerEvent<WhitebeardEffectData>(Core.eventName("WhitebeardEffectReplicate"), (data) => {
            print(`🌊 Client received Whitebeard effect: ${data.type} from user ${data.casterUserId}`);
            if (data.type === "whitebeard_quake") {
                this.createVisualEffectsFromServer(data);
            }
        });

        if (result.isError()) {
            warn(`Failed to setup Whitebeard networking: ${result.getError().message}`);
        } else {
            this.networkingInitialized = true;
            print("✅ QuakeAbility networking initialized successfully");
        }
    }

    public getNetworkingStatus(): { initialized: boolean; eventAvailable: boolean; eventName: string } {
        const eventName = Core.eventName("WhitebeardEffectReplicate");
        return {
            initialized: this.networkingInitialized,
            eventAvailable: ClientCore_Instance.isEventAvailable(eventName),
            eventName: eventName
        };
    }

    public forceInitializeNetworking(): boolean {
        print("🔧 QuakeAbility: Force initializing networking...");
        this.initializeNetworking();
        return this.networkingInitialized;
    }

    public isOnCooldown(): boolean {
        return tick() < this.cooldownEndTime;
    }

    private startCooldown(): void {
        this.cooldownEndTime = tick() + this.getCooldownTime();
    }

    private createVisualEffectsFromServer(effectData: WhitebeardEffectData): void {
        // Get the player who cast the ability
        const caster = Players.GetPlayerByUserId(effectData.casterUserId);
        if (!caster?.Character) return;

        print(`🥊 Creating ${effectData.punchType} quake punch effects for ${caster.Name}`);

        const character = caster.Character;
        const rightHand = character.FindFirstChild("RightHand") as Part;
        if (!rightHand) return;

        // Phase 1: Create sphere(s) based on punch type (same as original)
        if (effectData.punchType === "single") {
            createQuakeSphere(this, character);
        } else {
            createSequentialQuakeSpheres(this, character);
        }

        // Phase 2: Execute punch animation and effects (after 2 seconds like original)
        task.delay(2, () => {
            if (character.Parent) {
                executeQuakePunch(this, character, rightHand, effectData.punchType);

                // Create environmental effects
                const humanoidRootPart = character.FindFirstChild("HumanoidRootPart") as Part;
                if (humanoidRootPart) {
                    createCameraShake(5, 0.5);
                    createMapShockwave(humanoidRootPart.Position, 200, 2);
                }
            }
        });
    }

    public activate(punchType: "single" | "double" = "single"): void {
        if (this.isOnCooldown()) return;

        if (!this.networkingInitialized) {
            // Try to initialize networking one more time as a fallback
            print("🔄 QuakeAbility: Attempting to initialize networking on demand...");
            this.forceInitializeNetworking();

            if (!this.networkingInitialized) {
                warn("⚠️ QuakeAbility: Server not ready yet. Please wait a moment and try again.");
                warn("💡 Tip: Use the WorldTestingPanel to check server status and RemoteEvents.");
                warn("💡 Tip: Try the 'Force Reinit Quake Networking' button in WorldTestingPanel.");

                // Show current status for debugging
                const status = this.getNetworkingStatus();
                print(`📊 Debug Info - Event Available: ${status.eventAvailable}, Initialized: ${status.initialized}`);
                return;
            }
        }

        const player = Players.LocalPlayer;
        const character = player.Character;
        if (!character) return;

        const humanoidRootPart = character.FindFirstChild("HumanoidRootPart") as Part;
        if (!humanoidRootPart) return;

        print(`🥊 Requesting ${punchType} punch from server for visual sync`);

        // Send request to server using Core framework
        const result = fireServer(Core.eventName("WhitebeardVisualSync"), {
            punchType: punchType,
            casterUserId: player.UserId,
            position: humanoidRootPart.Position,
            timestamp: tick()
        });

        if (result.isError()) {
            warn(`Failed to send Whitebeard ability to server: ${result.getError().message}`);
            return;
        }

        // Start cooldown immediately to prevent spam while waiting for server response
        this.startCooldown();
    }
}