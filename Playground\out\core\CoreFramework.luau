-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local ServiceContainer = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "ServiceContainer").ServiceContainer
local Result = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "types", "Result").Result
local ServiceLifecycle = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "enums", "ServiceLifecycle").ServiceLifecycle
local NetworkService = TS.import(script, game:GetService("ReplicatedStorage"), "core", "networking", "NetworkService").NetworkService
local NetworkValidationService = TS.import(script, game:GetService("ReplicatedStorage"), "core", "networking", "NetworkValidationService").NetworkValidationService
local StateManager = TS.import(script, game:GetService("ReplicatedStorage"), "core", "state", "StateManager").StateManager
local BrandedTypes = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "types", "BrandedTypes").BrandedTypes
local createError = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "types", "RobloxError").createError
local CoreFramework
do
	CoreFramework = setmetatable({}, {
		__tostring = function()
			return "CoreFramework"
		end,
	})
	CoreFramework.__index = CoreFramework
	function CoreFramework.new(...)
		local self = setmetatable({}, CoreFramework)
		return self:constructor(...) or self
	end
	function CoreFramework:constructor()
		self.isInitialized = false
		self.container = ServiceContainer:getInstance()
	end
	function CoreFramework:getInstance()
		if not CoreFramework.instance then
			CoreFramework.instance = CoreFramework.new()
		end
		return CoreFramework.instance
	end
	CoreFramework.initialize = TS.async(function(self)
		if self.isInitialized then
			return Result:ok(nil)
		end
		local _exitType, _returns = TS.try(function()
			-- Register core services
			local registrationResult = self:registerCoreServices()
			if registrationResult:isError() then
				return TS.TRY_RETURN, { registrationResult }
			end
			-- Initialize all services
			local initResult = TS.await(self.container:initialize())
			if initResult:isError() then
				return TS.TRY_RETURN, { Result:err(createError(`Failed to initialize Core Framework: {initResult:getError().message}`)) }
			end
			self.isInitialized = true
			print("🚀 Core Framework initialized successfully!")
			return TS.TRY_RETURN, { Result:ok(nil) }
		end, function(error)
			return TS.TRY_RETURN, { Result:err(createError(`Core Framework initialization failed: {error}`)) }
		end)
		if _exitType then
			return unpack(_returns)
		end
	end)
	CoreFramework.shutdown = TS.async(function(self)
		if not self.isInitialized then
			return Result:ok(nil)
		end
		local shutdownResult = TS.await(self.container:shutdown())
		if shutdownResult:isError() then
			return Result:err(createError(`Failed to shutdown Core Framework: {shutdownResult:getError().message}`))
		end
		self.isInitialized = false
		print("🛑 Core Framework shutdown complete")
		return Result:ok(nil)
	end)
	function CoreFramework:getService(serviceName)
		if not self.isInitialized then
			return Result:err(createError("Core Framework is not initialized"))
		end
		local serviceResult = self.container:resolve(serviceName)
		if serviceResult:isError() then
			return Result:err(createError(`Failed to resolve service '{serviceName}': {serviceResult:getError().message}`))
		end
		return Result:ok(serviceResult:getValue())
	end
	function CoreFramework:getNetworkService()
		return self:getService("NetworkService")
	end
	function CoreFramework:getValidationService()
		return self:getService("NetworkValidationService")
	end
	function CoreFramework:createStateManager(initialState, name)
		return StateManager.new(initialState, name)
	end
	function CoreFramework:registerCoreServices()
		-- Register Network Validation Service
		local validationResult = self.container:register("NetworkValidationService", function()
			return NetworkValidationService.new()
		end, ServiceLifecycle.Singleton)
		if validationResult:isError() then
			return validationResult:mapError(function(err)
				return createError(err.message)
			end)
		end
		-- Register Network Service with validation dependency
		local networkResult = self.container:register("NetworkService", function()
			local networkService = NetworkService.new()
			local validationService = self.container:resolve("NetworkValidationService")
			if validationService:isOk() then
				networkService:setValidationService(validationService:getValue())
			end
			return networkService
		end, ServiceLifecycle.Singleton, { "NetworkValidationService" })
		if networkResult:isError() then
			return networkResult:mapError(function(err)
				return createError(err.message)
			end)
		end
		return Result:ok(nil)
	end
end
-- Global initialization function for easy setup
local initializeCoreFramework = TS.async(function()
	local core = CoreFramework:getInstance()
	return TS.await(core:initialize())
end)
-- Global shutdown function
local shutdownCoreFramework = TS.async(function()
	local core = CoreFramework:getInstance()
	return TS.await(core:shutdown())
end)
-- Utility functions for common operations
local Core = {
	getInstance = function()
		return CoreFramework:getInstance()
	end,
	getNetworkService = function()
		return CoreFramework:getInstance():getNetworkService()
	end,
	getValidationService = function()
		return CoreFramework:getInstance():getValidationService()
	end,
	createStateManager = function(initialState, name)
		return CoreFramework:getInstance():createStateManager(initialState, name)
	end,
	playerId = BrandedTypes.playerId,
	entityId = BrandedTypes.entityId,
	serviceName = BrandedTypes.serviceName,
	eventName = BrandedTypes.eventName,
	componentId = BrandedTypes.componentId,
	assetId = BrandedTypes.assetId,
	configKey = BrandedTypes.configKey,
}
return {
	initializeCoreFramework = initializeCoreFramework,
	shutdownCoreFramework = shutdownCoreFramework,
	CoreFramework = CoreFramework,
	Core = Core,
}
