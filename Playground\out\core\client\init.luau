-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitFor<PERSON>hild("RuntimeLib"))
local exports = {}
-- Client-side Core framework exports
local _ClientCore = TS.import(script, game:GetService("ReplicatedStorage"), "core", "client", "ClientCore")
exports.ClientCore = _ClientCore.ClientCore
exports.ClientCore_Instance = _ClientCore.ClientCore_Instance
exports.initializeClientCore = _ClientCore.initializeClientCore
exports.onServerEvent = _ClientCore.onServerEvent
exports.fireServer = _ClientCore.fireServer
exports.waitForEvent = _ClientCore.waitForEvent
local _ClientStateManager = TS.import(script, game:GetService("ReplicatedStorage"), "core", "client", "ClientStateManager")
exports.ClientState = _ClientStateManager.ClientState
exports.useClientState = _ClientStateManager.useClientState
exports.useUIState = _ClientStateManager.useUIState
return exports
