declare const __brand: unique symbol;
export type Brand<T, TBrand> = T & {
    [__brand]: TBrand;
};
export type { Error } from "./RobloxError";
export { createError } from "./RobloxError";
export type PlayerId = Brand<number, "PlayerId">;
export type EntityId = Brand<string, "EntityId">;
export type ServiceName = Brand<string, "ServiceName">;
export type EventName = Brand<string, "EventName">;
export type ComponentId = Brand<string, "ComponentId">;
export type AssetId = Brand<string, "AssetId">;
export type ConfigKey = Brand<string, "ConfigKey">;
export declare namespace BrandedTypes {
    function playerId(id: number): PlayerId;
    function entityId(id: string): EntityId;
    function serviceName(name: string): ServiceName;
    function eventName(name: string): EventName;
    function componentId(id: string): ComponentId;
    function assetId(id: string): AssetId;
    function configKey(key: string): ConfigKey;
}
