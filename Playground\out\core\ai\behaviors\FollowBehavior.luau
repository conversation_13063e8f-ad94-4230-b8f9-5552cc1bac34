-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local PositionHelper = TS.import(script, game:GetService("ReplicatedStorage"), "core", "helper", "PositionHelper").PositionHelper
local FollowBehavior
do
	FollowBehavior = setmetatable({}, {
		__tostring = function()
			return "FollowBehavior"
		end,
	})
	FollowBehavior.__index = FollowBehavior
	function FollowBehavior.new(...)
		local self = setmetatable({}, FollowBehavior)
		return self:constructor(...) or self
	end
	function FollowBehavior:constructor()
		self.name = "Follow"
		self.priority = 5
	end
	function FollowBehavior:canExecute(context)
		if not context.target or not context.targetPosition then
			return false
		end
		local _position = context.position
		local _targetPosition = context.targetPosition
		local distance = (_position - _targetPosition).Magnitude
		return distance <= 30 and distance > 5
	end
	function FollowBehavior:execute(context)
		if not context.target or not context.targetPosition then
			return {
				success = false,
				completed = true,
			}
		end
		local _position = context.position
		local _targetPosition = context.targetPosition
		local distance = (_position - _targetPosition).Magnitude
		self:moveTowards(context, context.targetPosition)
		PositionHelper:lookAt(context.entity, context.targetPosition)
		return {
			success = true,
			completed = distance <= 5,
		}
	end
	function FollowBehavior:onEnter(context)
		print(`🏃 {context.entityId} is following target`)
	end
	function FollowBehavior:moveTowards(context, targetPosition)
		local clearTarget = PositionHelper:findClearPath(context.position, targetPosition, { context.entity })
		if context.entity:IsA("Model") and context.entity.PrimaryPart then
			local humanoid = context.entity:FindFirstChild("Humanoid")
			if humanoid then
				humanoid:MoveTo(clearTarget)
			else
				self:smoothMoveTo(context.entity.PrimaryPart, clearTarget, context.deltaTime)
			end
		elseif context.entity:IsA("BasePart") then
			self:smoothMoveTo(context.entity, clearTarget, context.deltaTime)
		end
	end
	function FollowBehavior:smoothMoveTo(part, targetPosition, deltaTime)
		local currentPosition = part.Position
		local direction = targetPosition - currentPosition
		local distance = direction.Magnitude
		if distance > 0.1 then
			local moveSpeed = 16
			local maxMove = moveSpeed * deltaTime
			local moveDistance = math.min(maxMove, distance)
			local _arg0 = direction.Unit * moveDistance
			local newPosition = currentPosition + _arg0
			local lookDirection = direction.Unit
			local newCFrame = CFrame.lookAt(newPosition, newPosition + lookDirection)
			part.CFrame = newCFrame
		end
	end
end
return {
	FollowBehavior = FollowBehavior,
}
