-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local Workspace = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services").Workspace
local function playSound(soundId, volume, pitch, parent, delay)
	if volume == nil then
		volume = 0.8
	end
	if pitch == nil then
		pitch = 1
	end
	if parent == nil then
		parent = Workspace
	end
	if delay == nil then
		delay = 0
	end
	local sound = Instance.new("Sound")
	sound.SoundId = soundId
	sound.Volume = volume
	sound.PlaybackSpeed = pitch
	sound.Parent = parent
	if delay > 0 then
		task.delay(delay, function()
			return sound:Play()
		end)
	else
		sound:Play()
	end
	sound.Ended:Connect(function()
		return sound:Destroy()
	end)
	return sound
end
return {
	playSound = playSound,
}
