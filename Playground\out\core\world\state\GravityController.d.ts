import { GravityOptions, GravityLevel } from "./interfaces/GravityOptions";
/**
 * GravityController - Manages gravity levels for world and players
 * Provides simple methods for changing gravity with different presets
 */
export declare class GravityController {
    private static instance;
    private currentGravityLevel;
    private originalGravity;
    private constructor();
    static getInstance(): GravityController;
    /**
     * Set gravity level with options
     */
    setGravity(options: GravityOptions): void;
    /**
     * Get current gravity level
     */
    getCurrentGravityLevel(): GravityLevel;
    /**
     * Get current gravity value
     */
    getCurrentGravityValue(): number;
    /**
     * Get gravity multiplier compared to normal
     */
    getCurrentGravityMultiplier(): number;
    private getGravityValueForLevel;
    private applyGravityToPlayers;
    private applyGravityToObjects;
    /**
     * Create gravity zones (areas with different gravity)
     */
    createGravityZone(position: Vector3, size: Vector3, gravityLevel: GravityLevel): Part;
    private createFloatingParticles;
    private createGravityZoneEffect;
    private getGravityZoneColorAsColor3;
    private getGravityZoneColor;
    /**
     * Reset gravity to normal
     */
    resetGravity(): void;
    /**
     * Cleanup all gravity effects
     */
    cleanup(): void;
}
