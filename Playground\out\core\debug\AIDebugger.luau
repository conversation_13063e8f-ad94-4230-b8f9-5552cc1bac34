-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local PositionHelper = TS.import(script, game:GetService("ReplicatedStorage"), "core", "helper", "PositionHelper").PositionHelper
local AIDebugger
do
	AIDebugger = setmetatable({}, {
		__tostring = function()
			return "AIDebugger"
		end,
	})
	AIDebugger.__index = AIDebugger
	function AIDebugger.new(...)
		local self = setmetatable({}, AIDebugger)
		return self:constructor(...) or self
	end
	function AIDebugger:constructor(renderer)
		self.renderer = renderer
		self:setupGUI()
	end
	function AIDebugger:update(_config)
		-- For now, show placeholder info until we can safely access AIController
		self:updateAIInfo(0)
		-- TODO: Find a way to access AI agents without circular dependency
		-- This could be done by having the AIController register itself with the debug system
	end
	function AIDebugger:setupGUI()
		-- No need to calculate position since this will be part of the unified panel
		self.aiInfoLabel = self.renderer:createGUIElement("AIInfo", UDim2.new(0, 0, 0, 0), UDim2.new(0, 0, 0, 0), "AI Debug Info")
	end
	function AIDebugger:updateAIInfo(aiCount)
		if not self.aiInfoLabel then
			return nil
		end
		local info = { "=== AI DEBUG INFO ===", `Active AI Agents: {aiCount}` }
		self.aiInfoLabel.Text = table.concat(info, "\n")
	end
	function AIDebugger:debugAIAgent(aiAgent, config)
		-- Get AI agent data (we need to access private members for debugging)
		local entityId = self:getPrivateField(aiAgent, "entityId")
		local entity = self:getPrivateField(aiAgent, "entity")
		local state = aiAgent:getState()
		local currentBehavior = self:getPrivateField(aiAgent, "currentBehavior")
		local aiConfig = self:getPrivateField(aiAgent, "config")
		if not entity or not entity.Parent then
			return nil
		end
		local position = PositionHelper:getPosition(entity)
		local _result = currentBehavior
		if _result ~= nil then
			_result = _result.name
		end
		local _condition = _result
		if not (_condition ~= "" and _condition) then
			_condition = "None"
		end
		local behaviorName = _condition
		-- Show AI position and state
		if config.showPositions then
			self.renderer:drawSphere(position, 1, self:getStateColor(state), 0.1)
			local infoText = table.concat({ `ID: {entityId}`, `State: {state}`, `Behavior: {behaviorName}`, `Pos: {math.floor(position.X)}, {math.floor(position.Y)}, {math.floor(position.Z)}` }, "\n")
			local _renderer = self.renderer
			local _vector3 = Vector3.new(0, 3, 0)
			_renderer:drawText(position + _vector3, infoText, Color3.fromRGB(255, 255, 255), 0.1)
		end
		-- Show detection range
		local _value = config.showAI and aiConfig and aiConfig.detectionRange
		if _value ~= 0 and _value == _value and _value then
			local detectionColor = Color3.fromRGB(255, 165, 0)
			self.renderer:drawSphere(position, aiConfig.detectionRange, detectionColor, 0.1)
		end
		-- Show vision cone
		if config.showVision then
			local lookDirection = self:getEntityLookDirection(entity)
			local _value_1 = lookDirection and aiConfig and aiConfig.detectionRange
			if _value_1 ~= 0 and _value_1 == _value_1 and _value_1 then
				self.renderer:drawVisionCone(position, lookDirection, aiConfig.detectionRange, 90, Color3.fromRGB(0, 255, 0), 0.1)
			end
		end
		-- Show AI paths
		if config.showPaths then
			self:debugAIPaths(aiAgent, position)
		end
		-- Show target information
		self:debugAITarget(aiAgent, position)
		-- Show blackboard data
		if config.showAI then
			self:debugBlackboard(aiAgent, position)
		end
	end
	function AIDebugger:debugAIPaths(aiAgent, position)
		local blackboard = self:getPrivateField(aiAgent, "blackboard")
		-- Show patrol path
		local patrolPoints = blackboard.patrolPoints
		if patrolPoints and #patrolPoints > 0 then
			self.renderer:drawPath(patrolPoints, Color3.fromRGB(255, 255, 0), 0.1)
			-- Highlight current patrol target
			local _condition = blackboard.currentPatrolIndex
			if not (_condition ~= 0 and _condition == _condition and _condition) then
				_condition = 0
			end
			local currentIndex = _condition
			if currentIndex < #patrolPoints then
				self.renderer:drawLine(position, patrolPoints[currentIndex + 1], Color3.fromRGB(255, 0, 255), 0.1)
			end
		end
		-- Show wander target
		local wanderTarget = blackboard.wanderTarget
		if wanderTarget then
			self.renderer:drawLine(position, wanderTarget, Color3.fromRGB(0, 255, 255), 0.1)
			self.renderer:drawSphere(wanderTarget, 0.5, Color3.fromRGB(0, 255, 255), 0.1)
		end
		-- Show investigate position
		local investigatePos = blackboard.investigatePosition
		if investigatePos then
			self.renderer:drawLine(position, investigatePos, Color3.fromRGB(255, 128, 0), 0.1)
			self.renderer:drawSphere(investigatePos, 1, Color3.fromRGB(255, 128, 0), 0.1)
		end
	end
	function AIDebugger:debugAITarget(aiAgent, position)
		-- Find the target using the same method as AIAgent
		local target = self:callPrivateMethod(aiAgent, "findNearestPlayer")
		if target and target:IsA("Model") and target.PrimaryPart then
			local targetModel = target
			local targetPosition = targetModel.PrimaryPart.Position
			local distance = (position - targetPosition).Magnitude
			-- Draw line to target
			local aiEntity = self:getPrivateField(aiAgent, "entity")
			local hasLineOfSight = PositionHelper:hasLineOfSight(position, targetPosition, { aiEntity })
			local lineColor = if hasLineOfSight then Color3.fromRGB(0, 255, 0) else Color3.fromRGB(255, 0, 0)
			self.renderer:drawLine(position, targetPosition, lineColor, 0.1)
			-- Show target info
			local targetInfo = table.concat({ `Target: {targetModel.Name}`, `Distance: {math.floor(distance)}`, `LOS: {if hasLineOfSight then "YES" else "NO"}` }, "\n")
			local midpoint = (position + targetPosition) / 2
			self.renderer:drawText(midpoint, targetInfo, Color3.fromRGB(255, 255, 0), 0.1)
		end
	end
	function AIDebugger:debugBlackboard(aiAgent, position)
		local blackboard = self:getPrivateField(aiAgent, "blackboard")
		local blackboardData = {}
		-- ▼ ReadonlyMap.forEach ▼
		local _callback = function(value, key)
			local _value = value
			if typeof(_value) == "number" then
				local _arg0 = `{key}: {math.floor(value * 100) / 100}`
				table.insert(blackboardData, _arg0)
			else
				local _value_1 = value
				if typeof(_value_1) == "Vector3" then
					local vec = value
					local _arg0 = `{key}: ({math.floor(vec.X)}, {math.floor(vec.Y)}, {math.floor(vec.Z)})`
					table.insert(blackboardData, _arg0)
				else
					local _value_2 = value
					if typeof(_value_2) == "boolean" then
						local _arg0 = `{key}: {if value ~= 0 and value == value and value ~= "" and value then "true" else "false"}`
						table.insert(blackboardData, _arg0)
					else
						local _arg0 = `{key}: {tostring(value)}`
						table.insert(blackboardData, _arg0)
					end
				end
			end
		end
		for _k, _v in blackboard do
			_callback(_v, _k, blackboard)
		end
		-- ▲ ReadonlyMap.forEach ▲
		if #blackboardData > 0 then
			local blackboardText = "Blackboard:\n" .. table.concat(blackboardData, "\n")
			local _renderer = self.renderer
			local _position = position
			local _vector3 = Vector3.new(3, 0, 0)
			_renderer:drawText(_position + _vector3, blackboardText, Color3.fromRGB(200, 200, 200), 0.1)
		end
	end
	function AIDebugger:getStateColor(state)
		repeat
			if state == "Idle" then
				return Color3.fromRGB(128, 128, 128)
			end
			if state == "Moving" then
				return Color3.fromRGB(0, 255, 0)
			end
			if state == "Following" then
				return Color3.fromRGB(0, 0, 255)
			end
			if state == "Patrolling" then
				return Color3.fromRGB(255, 255, 0)
			end
			if state == "Attacking" then
				return Color3.fromRGB(255, 0, 0)
			end
			if state == "Fleeing" then
				return Color3.fromRGB(255, 0, 255)
			end
			if state == "Investigating" then
				return Color3.fromRGB(255, 165, 0)
			end
			return Color3.fromRGB(255, 255, 255)
		until true
	end
	function AIDebugger:getEntityLookDirection(entity)
		if entity:IsA("Model") and entity.PrimaryPart then
			return entity.PrimaryPart.CFrame.LookVector
		elseif entity:IsA("BasePart") then
			return entity.CFrame.LookVector
		end
		return nil
	end
	function AIDebugger:getPrivateField(obj, fieldName)
		return obj[fieldName]
	end
	function AIDebugger:callPrivateMethod(obj, methodName, ...)
		local args = { ... }
		return obj[methodName](unpack(args))
	end
	function AIDebugger:cleanup()
		-- Cleanup any resources
	end
end
return {
	AIDebugger = AIDebugger,
}
