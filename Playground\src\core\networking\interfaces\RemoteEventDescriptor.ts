import { Result } from "../../foundation/types/Result";
import { EventName } from "../../foundation/types/BrandedTypes";
import { Error } from "../../foundation/types/RobloxError";

export interface RemoteEventDescriptor<TRequest, TResponse> {
    eventName: EventName;
    handler: (player: Player, request: TRequest) => Promise<Result<TResponse, Error>>;
    options: {
        validateRequest?: (request: unknown) => Result<TRequest, Error>;
        rateLimit?: { maxCalls: number; windowMs: number };
        requiresAuth?: boolean;
    };
    remoteEvent: RemoteEvent;
}
