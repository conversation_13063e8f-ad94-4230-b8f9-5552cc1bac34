import { Players, ReplicatedStorage, RunService } from "@rbxts/services";
import { Result } from "../foundation/types/Result";
import { Error, createError } from "../foundation/types/RobloxError";
import { EventName } from "../foundation/types/BrandedTypes";

interface ClientEventHandler {
    eventName: string;
    handler: (data: unknown) => void;
}

interface ServerEventData {
    success: boolean;
    data?: unknown;
    error?: string;
    timestamp: number;
}

export class ClientCore {
    private static instance?: ClientCore;
    private eventHandlers = new Map<string, ClientEventHandler>();
    private remoteEvents = new Map<string, RemoteEvent>();
    private isInitialized = false;

    private constructor() {}

    public static getInstance(): ClientCore {
        if (!ClientCore.instance) {
            ClientCore.instance = new ClientCore();
        }
        return ClientCore.instance;
    }

    public async initialize(): Promise<Result<void, Error>> {
        if (this.isInitialized) {
            return Result.ok(undefined);
        }

        try {
            // Wait for server to create RemoteEvents
            await this.waitForServerEvents();
            
            this.isInitialized = true;
            print("🚀 Client Core initialized successfully!");
            return Result.ok(undefined);
        } catch (error) {
            return Result.err(createError(`Failed to initialize Client Core: ${error}`));
        }
    }

    public onServerEvent<T>(eventName: EventName, handler: (data: T) => void): Result<void, Error> {
        if (!this.isInitialized) {
            return Result.err(createError("Client Core is not initialized"));
        }

        const remoteEvent = this.remoteEvents.get(eventName);
        if (!remoteEvent) {
            return Result.err(createError(`RemoteEvent '${eventName}' not found`));
        }

        const clientHandler: ClientEventHandler = {
            eventName,
            handler: (data: unknown) => handler(data as T)
        };

        this.eventHandlers.set(eventName, clientHandler);

        // Connect to server event
        remoteEvent.OnClientEvent.Connect((data: ServerEventData) => {
            if (data.success && data.data !== undefined) {
                handler(data.data as T);
            } else if (data.error) {
                warn(`Server event error for '${eventName}': ${data.error}`);
            }
        });

        return Result.ok(undefined);
    }

    public fireServer<T>(eventName: EventName, data: T): Result<void, Error> {
        if (!this.isInitialized) {
            return Result.err(createError("Client Core is not initialized"));
        }

        const remoteEvent = this.remoteEvents.get(eventName);
        if (!remoteEvent) {
            return Result.err(createError(`RemoteEvent '${eventName}' not found`));
        }

        try {
            remoteEvent.FireServer(data);
            return Result.ok(undefined);
        } catch (error) {
            return Result.err(createError(`Failed to fire server event: ${error}`));
        }
    }

    private async waitForServerEvents(): Promise<void> {
        const maxWaitTime = 30; // 30 seconds
        const startTime = tick();

        while (tick() - startTime < maxWaitTime) {
            const remoteEventsFolder = ReplicatedStorage.FindFirstChild("RemoteEvents");
            if (remoteEventsFolder) {
                // Cache all RemoteEvents
                for (const child of remoteEventsFolder.GetChildren()) {
                    if (child.IsA("RemoteEvent")) {
                        this.remoteEvents.set(child.Name, child);
                    }
                }
                return;
            }
            
            // Wait a frame before checking again
            RunService.Heartbeat.Wait();
        }

        throw `Timeout waiting for server RemoteEvents`;
    }

    public getEventNames(): string[] {
        const names: string[] = [];
        for (const [name] of this.remoteEvents) {
            names.push(name);
        }
        return names;
    }

    public isEventAvailable(eventName: EventName): boolean {
        return this.remoteEvents.has(eventName);
    }

    public async waitForEvent(eventName: EventName, maxWaitTime = 30): Promise<Result<void, Error>> {
        print(`🔍 ClientCore: Waiting for RemoteEvent '${eventName}'...`);
        const startTime = tick();
        let attempts = 0;

        while (tick() - startTime < maxWaitTime) {
            if (this.isEventAvailable(eventName)) {
                print(`✅ ClientCore: RemoteEvent '${eventName}' found after ${attempts} attempts`);
                return Result.ok(undefined);
            }

            attempts++;
            if (attempts % 10 === 0) { // Log every 10 attempts
                print(`⏳ ClientCore: Still waiting for '${eventName}' (${attempts} attempts, ${string.format("%.1f", tick() - startTime)}s)`);
                print(`📊 Available events: ${this.getEventNames().join(", ")}`);
            }

            // Wait a frame before checking again
            await new Promise(resolve => RunService.Heartbeat.Wait());
        }

        print(`❌ ClientCore: Timeout waiting for '${eventName}' after ${attempts} attempts`);
        print(`📊 Final available events: ${this.getEventNames().join(", ")}`);
        return Result.err(createError(`Timeout waiting for RemoteEvent '${eventName}'`));
    }
}

// Global client core instance
export const ClientCore_Instance = ClientCore.getInstance();

// Convenience functions
export function initializeClientCore(): Promise<Result<void, Error>> {
    return ClientCore_Instance.initialize();
}

export function onServerEvent<T>(eventName: EventName, handler: (data: T) => void): Result<void, Error> {
    return ClientCore_Instance.onServerEvent(eventName, handler);
}

export function fireServer<T>(eventName: EventName, data: T): Result<void, Error> {
    return ClientCore_Instance.fireServer(eventName, data);
}

export function waitForEvent(eventName: EventName, maxWaitTime = 30): Promise<Result<void, Error>> {
    return ClientCore_Instance.waitForEvent(eventName, maxWaitTime);
}
