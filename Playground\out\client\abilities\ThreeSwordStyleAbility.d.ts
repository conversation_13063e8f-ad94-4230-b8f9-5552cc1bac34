import { AbilityBase } from "./AbilityBase";
export declare class ThreeSwordStyleAbility extends AbilityBase {
    private swordEffects;
    private swordConnection?;
    private cooldownEndTime;
    private isActive;
    constructor();
    isOnCooldown(): boolean;
    private startCooldown;
    activate(): void;
    private createThreeSwordStyleAnimation;
    private createC<PERSON>cterAnimation;
    private animateR6Character;
    private animateR15<PERSON><PERSON>cter;
    private createSwordAura;
    private createThreeSwords;
    private createSwordTrail;
    private executeTripleSlash;
    private createSlashEffect;
    private createSlashImpact;
    private createAirBlades;
    private launchAirBlade;
    private createAirBladeTrail;
    private checkAirBladeCollision;
    private createAirBladeImpact;
    private createEnvironmentalSwordEffects;
    private createSwordAtmosphere;
    private createFloatingSwordEnergy;
    private createGroundSwordMarks;
    private cleanupEffects;
}
