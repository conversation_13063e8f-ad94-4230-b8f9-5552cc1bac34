export declare class LimbAnimator {
    private character;
    constructor(character: Model);
    static forC<PERSON>cter(character: Model): LimbAnimator;
    moveLimb(limbName: string, relativeCFrame: CFrame, duration?: number, easingStyle?: Enum.EasingStyle.Quad, easingDirection?: Enum.EasingDirection.Out): void;
    moveBodyPart(partName: string, relativeCFrame: CFrame, duration?: number, easingStyle?: Enum.EasingStyle.Quad, easingDirection?: Enum.EasingDirection.Out): void;
    lunge(directionVector: Vector3, studs?: number, duration?: number, easingStyle?: Enum.EasingStyle.Back, easingDirection?: Enum.EasingDirection.Out): void;
}
