-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local _services = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services")
local RunService = _services.RunService
local Workspace = _services.Workspace
local EffectPartBuilder = TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "EffectPartBuilder").EffectPartBuilder
local EffectTweenBuilder = TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "EffectTweenBuilder").EffectTweenBuilder
local function cameraShake(intensity, duration)
	if intensity == nil then
		intensity = 5
	end
	if duration == nil then
		duration = 0.5
	end
	local camera = Workspace.CurrentCamera
	if not camera then
		return nil
	end
	local originalCFrame = camera.CFrame
	local startTime = tick()
	local connection
	connection = RunService.RenderStepped:Connect(function()
		local elapsed = tick() - startTime
		if elapsed >= duration then
			camera.CFrame = originalCFrame
			connection:Disconnect()
			return nil
		end
		local progress = elapsed / duration
		local currentIntensity = intensity * (1 - progress)
		local offset = Vector3.new((math.random() - 0.5) * currentIntensity, (math.random() - 0.5) * currentIntensity, (math.random() - 0.5) * currentIntensity)
		camera.CFrame = originalCFrame + offset
	end)
end
local CameraShakeHelper
do
	CameraShakeHelper = setmetatable({}, {
		__tostring = function()
			return "CameraShakeHelper"
		end,
	})
	CameraShakeHelper.__index = CameraShakeHelper
	function CameraShakeHelper.new(...)
		local self = setmetatable({}, CameraShakeHelper)
		return self:constructor(...) or self
	end
	function CameraShakeHelper:constructor()
	end
	function CameraShakeHelper:shake(intensity, duration, id)
		if intensity == nil then
			intensity = 5
		end
		if duration == nil then
			duration = 0.5
		end
		if id == nil then
			id = "default"
		end
		-- Stop any existing shake with the same ID
		self:stopShake(id)
		local camera = Workspace.CurrentCamera
		if not camera then
			return nil
		end
		local originalCFrame = camera.CFrame
		local startTime = tick()
		local connection
		connection = RunService.RenderStepped:Connect(function()
			local elapsed = tick() - startTime
			if elapsed >= duration then
				-- Restore original camera position and disconnect
				camera.CFrame = originalCFrame
				connection:Disconnect()
				local _activeShakes = self.activeShakes
				local _id = id
				_activeShakes[_id] = nil
				return nil
			end
			-- Calculate shake intensity that decreases over time
			local progress = elapsed / duration
			local currentIntensity = intensity * (1 - progress)
			-- Generate random shake offset
			local shakeX = (math.random() - 0.5) * currentIntensity
			local shakeY = (math.random() - 0.5) * currentIntensity
			local shakeZ = (math.random() - 0.5) * currentIntensity
			-- Apply shake to camera
			local shakeOffset = Vector3.new(shakeX, shakeY, shakeZ)
			camera.CFrame = originalCFrame + shakeOffset
		end)
		local _activeShakes = self.activeShakes
		local _id = id
		_activeShakes[_id] = connection
	end
	function CameraShakeHelper:stopShake(id)
		local _activeShakes = self.activeShakes
		local _id = id
		local connection = _activeShakes[_id]
		if connection then
			connection:Disconnect()
			local _activeShakes_1 = self.activeShakes
			local _id_1 = id
			_activeShakes_1[_id_1] = nil
		end
	end
	function CameraShakeHelper:stopAllShakes()
		for _, connection in self.activeShakes do
			connection:Disconnect()
		end
		table.clear(self.activeShakes)
	end
	CameraShakeHelper.activeShakes = {}
end
local CrackEffectHelper
do
	CrackEffectHelper = setmetatable({}, {
		__tostring = function()
			return "CrackEffectHelper"
		end,
	})
	CrackEffectHelper.__index = CrackEffectHelper
	function CrackEffectHelper.new(...)
		local self = setmetatable({}, CrackEffectHelper)
		return self:constructor(...) or self
	end
	function CrackEffectHelper:constructor()
	end
	function CrackEffectHelper:createGroundCracks(centerPosition, maxRadius, numCracks)
		if maxRadius == nil then
			maxRadius = 140
		end
		if numCracks == nil then
			numCracks = 12
		end
		do
			local i = 0
			local _shouldIncrement = false
			while true do
				if _shouldIncrement then
					i += 1
				else
					_shouldIncrement = true
				end
				if not (i < numCracks) then
					break
				end
				local angle = (i / numCracks) * math.pi * 2
				local crackLength = math.random(maxRadius * 0.6, maxRadius)
				-- Create crack line segments
				local segments = 8
				local segmentLength = crackLength / segments
				for seg = 0, segments - 1 do
					local segmentDelay = (i * 0.03) + (seg * 0.02)
					task.delay(segmentDelay, function()
						local crack = EffectPartBuilder:create():shape(Enum.PartType.Block):size(Vector3.new(0.2, 0.1, segmentLength)):color(Color3.fromRGB(80, 70, 60)):material(Enum.Material.Concrete):transparency(1):position(Vector3.new(centerPosition.X + math.cos(angle + math.random(-0.1, 0.1)) * (seg * segmentLength + segmentLength * 0.5), centerPosition.Y - 4.5, centerPosition.Z + math.sin(angle + math.random(-0.1, 0.1)) * (seg * segmentLength + segmentLength * 0.5))):cframe(CFrame.new(Vector3.new(centerPosition.X + math.cos(angle) * (seg * segmentLength + segmentLength * 0.5), centerPosition.Y - 4.5, centerPosition.Z + math.sin(angle) * (seg * segmentLength + segmentLength * 0.5)), Vector3.new(centerPosition.X + math.cos(angle) * (seg * segmentLength + segmentLength * 0.5) + math.cos(angle), centerPosition.Y - 4.5, centerPosition.Z + math.sin(angle) * (seg * segmentLength + segmentLength * 0.5) + math.sin(angle)))):spawn()
						-- Start invisible and appear
						EffectTweenBuilder["for"](EffectTweenBuilder, crack):fade(0.2):duration(0.1):easing(Enum.EasingStyle.Quad, Enum.EasingDirection.Out):play()
						-- Fade out after some time
						task.delay(8 + math.random(2, 5), function()
							EffectTweenBuilder["for"](EffectTweenBuilder, crack):fade(1):duration(3):onComplete(function()
								return crack:Destroy()
							end):play()
						end)
					end)
				end
			end
		end
	end
	function CrackEffectHelper:createAirCracks(position, numCracks)
		if numCracks == nil then
			numCracks = 8
		end
		do
			local i = 0
			local _shouldIncrement = false
			while true do
				if _shouldIncrement then
					i += 1
				else
					_shouldIncrement = true
				end
				if not (i < numCracks) then
					break
				end
				local crack = EffectPartBuilder:create():shape(Enum.PartType.Block):size(Vector3.new(0.2, math.random(3, 8), 0.2)):color(Color3.fromRGB(255, 255, 255)):material(Enum.Material.Neon):transparency(0.3):spawn()
				-- Random crack positioning
				local angle = (i / numCracks) * math.pi * 2 + math.random(-0.5, 0.5)
				local distance = math.random(2, 6)
				local _position = position
				local _vector3 = Vector3.new(math.cos(angle) * distance, math.random(-3, 3), math.sin(angle) * distance)
				local crackPos = _position + _vector3
				local _cFrame = CFrame.new(crackPos)
				local _arg0 = CFrame.Angles(math.random(-0.5, 0.5), angle, math.random(-0.3, 0.3))
				crack.CFrame = _cFrame * _arg0
				-- Crack appearance animation
				EffectTweenBuilder["for"](EffectTweenBuilder, crack):fade(0.1):duration(0.1):easing(Enum.EasingStyle.Quad, Enum.EasingDirection.Out):play()
				-- Crack disappear animation
				task.delay(2, function()
					EffectTweenBuilder["for"](EffectTweenBuilder, crack):fade(1):duration(1):onComplete(function()
						return crack:Destroy()
					end):play()
				end)
			end
		end
		-- Create shockwave effect
		local _ = EffectPartBuilder:create():shape(Enum.PartType.Cylinder):size(Vector3.new(0.1, 1, 1)):color(Color3.fromRGB(255, 255, 255)):material(Enum.Material.Neon):transparency(0.5):position(position)
		local _cFrame = CFrame.new(position)
		local _arg0 = CFrame.Angles(0, 0, math.rad(90))
		local shockwave = _:cframe(_cFrame * _arg0):spawn()
		-- Expand shockwave
		EffectTweenBuilder["for"](EffectTweenBuilder, shockwave):expand(Vector3.new(0.1, 30, 30)):fade(1):duration(0.8):easing(Enum.EasingStyle.Quad, Enum.EasingDirection.Out):onComplete(function()
			return shockwave:Destroy()
		end):play()
	end
	function CrackEffectHelper:createGlassCracks(position, direction, numMainCracks, numWebCracks)
		if numMainCracks == nil then
			numMainCracks = 8
		end
		if numWebCracks == nil then
			numWebCracks = 12
		end
		-- Create central impact point with bright flash
		local impact = EffectPartBuilder:create():shape(Enum.PartType.Ball):size(Vector3.new(2, 2, 2)):color(Color3.fromRGB(255, 255, 255)):material(Enum.Material.Neon):transparency(0):position(position):withLight(30, 20):spawn()
		EffectTweenBuilder["for"](EffectTweenBuilder, impact):expand(Vector3.new(8, 8, 8)):fade(1):duration(0.3):onComplete(function()
			return impact:Destroy()
		end):play()
		-- Create main radiating cracks
		do
			local i = 0
			local _shouldIncrement = false
			while true do
				if _shouldIncrement then
					i += 1
				else
					_shouldIncrement = true
				end
				if not (i < numMainCracks) then
					break
				end
				local angle = (i / numMainCracks) * math.pi * 2
				local delay = i * 0.02
				task.delay(delay, function()
					self:createGlassCrackLine(position, angle, 15, true)
				end)
			end
		end
		-- Create secondary web pattern
		task.delay(0.2, function()
			do
				local i = 0
				local _shouldIncrement = false
				while true do
					if _shouldIncrement then
						i += 1
					else
						_shouldIncrement = true
					end
					if not (i < numWebCracks) then
						break
					end
					local delay = i * 0.03
					task.delay(delay, function()
						local angle = math.random(0, math.pi * 2)
						local startRadius = math.random(3, 8)
						local _position = position
						local _vector3 = Vector3.new(math.cos(angle) * startRadius, math.sin(angle) * startRadius, math.random(-1, 1))
						local startPoint = _position + _vector3
						self:createGlassCrackLine(startPoint, angle + math.random(-0.5, 0.5), 8, false)
					end)
				end
			end
		end)
	end
	function CrackEffectHelper:createGlassCrackLine(startPoint, angle, maxLength, isMainCrack)
		local segments = if isMainCrack then 12 else 6
		local segmentLength = maxLength / segments
		for seg = 0, segments - 1 do
			local segmentDelay = seg * 0.01
			task.delay(segmentDelay, function()
				local crack = EffectPartBuilder:create():shape(Enum.PartType.Block):size(Vector3.new(if isMainCrack then 0.05 else 0.03, if isMainCrack then 0.08 else 0.05, segmentLength + math.random(-0.2, 0.2))):color(Color3.fromRGB(240, 250, 255)):material(Enum.Material.Neon):transparency(1):spawn()
				-- Position along crack line with natural variation
				local deviation = math.random(-0.2, 0.2)
				local heightVariation = math.random(-1, 1)
				local distance = seg * segmentLength + segmentLength * 0.5
				local _startPoint = startPoint
				local _vector3 = Vector3.new(math.cos(angle + deviation) * distance, math.sin(angle + deviation) * distance + heightVariation, math.random(-0.3, 0.3))
				local crackPos = _startPoint + _vector3
				crack.Position = crackPos
				local _vector3_1 = Vector3.new(math.cos(angle), math.sin(angle), 0)
				crack.CFrame = CFrame.lookAt(crackPos, crackPos + _vector3_1)
				-- Bright glow for main cracks
				if isMainCrack then
					local light = Instance.new("PointLight")
					light.Color = Color3.fromRGB(200, 230, 255)
					light.Range = 2
					light.Brightness = 3
					light.Parent = crack
				end
				-- Flash into existence
				EffectTweenBuilder["for"](EffectTweenBuilder, crack):fade(0.1):duration(0.02):play()
				-- Fade out after time
				task.delay(5 + math.random(2, 4), function()
					EffectTweenBuilder["for"](EffectTweenBuilder, crack):fade(1):duration(2):onComplete(function()
						return crack:Destroy()
					end):play()
				end)
			end)
		end
	end
end
local function createImpactFlash(pos, size, duration)
	if size == nil then
		size = 4
	end
	if duration == nil then
		duration = 0.3
	end
	local flash = EffectPartBuilder:create():shape(Enum.PartType.Ball):size(Vector3.new(0.5, 0.5, 0.5)):color(Color3.fromRGB(255, 255, 255)):material(Enum.Material.Neon):transparency(0):position(pos):withLight(20, 15):spawn()
	EffectTweenBuilder["for"](EffectTweenBuilder, flash):expand(Vector3.new(size, size, size)):fade(1):duration(duration):onComplete(function()
		return flash:Destroy()
	end):play()
end
local function createTrail(startPos, endPos, color, numSegments)
	if numSegments == nil then
		numSegments = 8
	end
	do
		local i = 0
		local _shouldIncrement = false
		while true do
			if _shouldIncrement then
				i += 1
			else
				_shouldIncrement = true
			end
			if not (i < numSegments) then
				break
			end
			local t = i / numSegments
			local basePos = startPos:Lerp(endPos, t)
			local offset = Vector3.new(math.random(-2, 2), math.random(-1, 1), math.random(-2, 2))
			local _ = EffectPartBuilder:create():shape(Enum.PartType.Block):size(Vector3.new(0.3, 0.3, 2)):color(color):material(Enum.Material.Neon):transparency(0.3):position(basePos + offset)
			local _exp = basePos + offset
			local _exp_1 = basePos + offset
			local _endPos = endPos
			local _startPos = startPos
			local _unit = (_endPos - _startPos).Unit
			local trail = _:cframe(CFrame.new(_exp, _exp_1 + _unit)):spawn()
			task.delay(i * 0.05, function()
				EffectTweenBuilder["for"](EffectTweenBuilder, trail):fade(1):duration(1.5):onComplete(function()
					return trail:Destroy()
				end):play()
			end)
		end
	end
end
return {
	cameraShake = cameraShake,
	createImpactFlash = createImpactFlash,
	createTrail = createTrail,
	CameraShakeHelper = CameraShakeHelper,
	CrackEffectHelper = CrackEffectHelper,
}
