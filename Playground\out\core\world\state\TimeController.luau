-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local Lighting = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services").Lighting
local EffectTweenBuilder = TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "EffectTweenBuilder").EffectTweenBuilder
--[[
	*
	 * TimeController - Manages day/night cycle and time of day
	 * Provides simple methods for changing time with smooth transitions
	 
]]
local TimeController
do
	TimeController = setmetatable({}, {
		__tostring = function()
			return "TimeController"
		end,
	})
	TimeController.__index = TimeController
	function TimeController.new(...)
		local self = setmetatable({}, TimeController)
		return self:constructor(...) or self
	end
	function TimeController:constructor()
		self.currentTimeOfDay = "noon"
		self.activeTweens = {}
	end
	function TimeController:getInstance()
		if not TimeController.instance then
			TimeController.instance = TimeController.new()
		end
		return TimeController.instance
	end
	function TimeController:setTime(options)
		self.currentTimeOfDay = options.timeOfDay
		local _condition = options.transitionDuration
		if not (_condition ~= 0 and _condition == _condition and _condition) then
			_condition = 2
		end
		local transitionDuration = _condition
		-- Clear existing tweens
		self:clearActiveTweens()
		-- Get target clock time
		local _condition_1 = options.clockTime
		if not (_condition_1 ~= 0 and _condition_1 == _condition_1 and _condition_1) then
			_condition_1 = self:getClockTimeForTimeOfDay(options.timeOfDay)
		end
		local targetClockTime = _condition_1
		-- Create smooth transition
		self:tweenToTime(targetClockTime, transitionDuration)
		self:tweenLightingForTimeOfDay(options.timeOfDay, transitionDuration)
		print(`🌅 Time changed to: {options.timeOfDay} ({targetClockTime}:00)`)
	end
	function TimeController:getCurrentTimeOfDay()
		return self.currentTimeOfDay
	end
	function TimeController:getCurrentClockTime()
		return Lighting.ClockTime
	end
	function TimeController:getClockTimeForTimeOfDay(timeOfDay)
		repeat
			if timeOfDay == "dawn" then
				return 6
			end
			if timeOfDay == "noon" then
				return 12
			end
			if timeOfDay == "dusk" then
				return 18
			end
			if timeOfDay == "night" then
				return 0
			end
			return 12
		until true
	end
	function TimeController:tweenToTime(targetTime, duration)
		-- Use Core framework EffectTweenBuilder instead of raw TweenService
		local timeTween = EffectTweenBuilder["for"](EffectTweenBuilder, Lighting):duration(duration):easing(Enum.EasingStyle.Sine, Enum.EasingDirection.InOut):play()
		-- Manually animate clock time since EffectTweenBuilder doesn't have clockTime method
		local startTime = Lighting.ClockTime
		local startTick = tick()
		local animateClockTime
		animateClockTime = function()
			local elapsed = tick() - startTick
			local progress = math.min(elapsed / duration, 1)
			-- Smooth interpolation
			local currentTime = startTime + (targetTime - startTime) * progress
			Lighting.ClockTime = currentTime
			if progress < 1 then
				task.wait()
				animateClockTime()
			end
		end
		task.spawn(function()
			return animateClockTime()
		end)
		self.activeTweens.clockTime = timeTween
	end
	function TimeController:tweenLightingForTimeOfDay(timeOfDay, duration)
		-- Get target lighting values
		local targetLighting
		repeat
			if timeOfDay == "dawn" then
				targetLighting = {
					brightness = 0.8,
					ambient = Color3.new(0.8, 0.5, 0.3),
					colorShiftBottom = Color3.new(1, 0.7, 0.4),
					colorShiftTop = Color3.new(1, 0.8, 0.6),
					fogColor = Color3.new(1, 0.8, 0.6),
				}
				break
			end
			if timeOfDay == "noon" then
				targetLighting = {
					brightness = 1,
					ambient = Color3.new(0.5, 0.5, 0.5),
					colorShiftBottom = Color3.new(0, 0, 0),
					colorShiftTop = Color3.new(0, 0, 0),
					fogColor = Color3.new(0.76, 0.76, 0.76),
				}
				break
			end
			if timeOfDay == "dusk" then
				targetLighting = {
					brightness = 0.6,
					ambient = Color3.new(0.6, 0.4, 0.3),
					colorShiftBottom = Color3.new(1, 0.5, 0.2),
					colorShiftTop = Color3.new(0.8, 0.6, 0.4),
					fogColor = Color3.new(0.9, 0.6, 0.4),
				}
				break
			end
			if timeOfDay == "night" then
				targetLighting = {
					brightness = 0.2,
					ambient = Color3.new(0.1, 0.1, 0.3),
					colorShiftBottom = Color3.new(0, 0, 0.2),
					colorShiftTop = Color3.new(0, 0, 0.1),
					fogColor = Color3.new(0.2, 0.2, 0.4),
				}
				break
			end
		until true
		-- Use Core framework EffectTweenBuilder for brightness
		local lightingTween = EffectTweenBuilder["for"](EffectTweenBuilder, Lighting):brightness(targetLighting.brightness):duration(duration):easing(Enum.EasingStyle.Sine, Enum.EasingDirection.InOut):play()
		-- Manually animate other lighting properties since EffectTweenBuilder doesn't have them
		local startValues = {
			ambient = Lighting.Ambient,
			colorShiftBottom = Lighting.ColorShift_Bottom,
			colorShiftTop = Lighting.ColorShift_Top,
			fogColor = Lighting.FogColor,
		}
		local startTick = tick()
		local animateLighting
		animateLighting = function()
			local elapsed = tick() - startTick
			local progress = math.min(elapsed / duration, 1)
			-- Smooth interpolation for colors
			Lighting.Ambient = startValues.ambient:Lerp(targetLighting.ambient, progress)
			Lighting.ColorShift_Bottom = startValues.colorShiftBottom:Lerp(targetLighting.colorShiftBottom, progress)
			Lighting.ColorShift_Top = startValues.colorShiftTop:Lerp(targetLighting.colorShiftTop, progress)
			Lighting.FogColor = startValues.fogColor:Lerp(targetLighting.fogColor, progress)
			if progress < 1 then
				task.wait()
				animateLighting()
			end
		end
		task.spawn(function()
			return animateLighting()
		end)
		self.activeTweens.lighting = lightingTween
	end
	function TimeController:startDayNightCycle(cycleDuration)
		if cycleDuration == nil then
			cycleDuration = 240
		end
		-- 4 minute full cycle by default (60 seconds per time period)
		local timePerPhase = cycleDuration / 4
		local cycleSequence = { "dawn", "noon", "dusk", "night" }
		local currentIndex = 0
		local nextPhase
		nextPhase = function()
			self:setTime({
				timeOfDay = cycleSequence[currentIndex + 1],
				transitionDuration = timePerPhase * 0.1,
			})
			currentIndex = (currentIndex + 1) % #cycleSequence
			task.wait(timePerPhase)
			nextPhase()
		end
		task.spawn(function()
			return nextPhase()
		end)
		print(`🔄 Day/night cycle started ({cycleDuration}s full cycle)`)
	end
	function TimeController:stopDayNightCycle()
		self:clearActiveTweens()
		print("⏹️ Day/night cycle stopped")
	end
	function TimeController:clearActiveTweens()
		for key, tween in self.activeTweens do
			if tween then
				tween:Cancel()
			end
		end
		table.clear(self.activeTweens)
	end
	function TimeController:cleanup()
		self:clearActiveTweens()
		-- Reset to default noon lighting
		self:setTime({
			timeOfDay = "noon",
			transitionDuration = 0,
		})
	end
end
return {
	TimeController = TimeController,
}
