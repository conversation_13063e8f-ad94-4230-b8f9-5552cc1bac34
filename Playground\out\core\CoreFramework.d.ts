import { Result } from "./foundation/types/Result";
import { IService } from "./foundation/interfaces/IService";
import { NetworkService } from "./networking/NetworkService";
import { NetworkValidationService } from "./networking/NetworkValidationService";
import { StateManager } from "./state/StateManager";
import { BrandedTypes } from "./foundation/types/BrandedTypes";
import { Error } from "./foundation/types/RobloxError";
export declare class CoreFramework {
    private static instance;
    private container;
    private isInitialized;
    private constructor();
    static getInstance(): CoreFramework;
    initialize(): Promise<Result<void, Error>>;
    shutdown(): Promise<Result<void, Error>>;
    getService<T extends IService>(serviceName: string): Result<T, Error>;
    getNetworkService(): Result<NetworkService, Error>;
    getValidationService(): Result<NetworkValidationService, Error>;
    createStateManager<TState>(initialState: TState, name?: string): StateManager<TState>;
    private registerCoreServices;
}
export declare function initializeCoreFramework(): Promise<Result<void, Error>>;
export declare function shutdownCoreFramework(): Promise<Result<void, Error>>;
export declare const Core: {
    getInstance: () => CoreFramework;
    getNetworkService: () => Result<NetworkService, Error>;
    getValidationService: () => Result<NetworkValidationService, Error>;
    createStateManager: <TState>(initialState: TState, name?: string) => StateManager<TState>;
    playerId: typeof BrandedTypes.playerId;
    entityId: typeof BrandedTypes.entityId;
    serviceName: typeof BrandedTypes.serviceName;
    eventName: typeof BrandedTypes.eventName;
    componentId: typeof BrandedTypes.componentId;
    assetId: typeof BrandedTypes.assetId;
    configKey: typeof BrandedTypes.configKey;
};
