-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local UserInputService = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services").UserInputService
local PlayerMovement = TS.import(script, script.Parent, "PlayerMovement").PlayerMovement
local MovementExample
do
	MovementExample = setmetatable({}, {
		__tostring = function()
			return "MovementExample"
		end,
	})
	MovementExample.__index = MovementExample
	function MovementExample.new(...)
		local self = setmetatable({}, MovementExample)
		return self:constructor(...) or self
	end
	function MovementExample:constructor()
		self.movement = PlayerMovement.new()
		self:setupControls()
	end
	function MovementExample:setupControls()
		-- Example controls for testing movement
		UserInputService.InputBegan:Connect(function(input, gameProcessed)
			if gameProcessed then
				return nil
			end
			local _exp = input.KeyCode
			repeat
				if _exp == (Enum.KeyCode.Q) then
					-- Dash forward
					self.movement:dash(Vector3.new(0, 0, -1), 80, 0.5)
					break
				end
				if _exp == (Enum.KeyCode.E) then
					-- Launch upward
					self.movement:launch(60, 20)
					break
				end
				if _exp == (Enum.KeyCode.R) then
					-- Super speed
					self.movement:setWalkSpeed(100)
					break
				end
				if _exp == (Enum.KeyCode.T) then
					-- Normal speed
					self.movement:setWalkSpeed(16)
					break
				end
				if _exp == (Enum.KeyCode.Y) then
					-- Jump
					self.movement:jump()
					break
				end
				if _exp == (Enum.KeyCode.U) then
					-- Teleport to spawn
					self.movement:teleport(Vector3.new(0, 10, 0))
					break
				end
				if _exp == (Enum.KeyCode.I) then
					-- Enable custom movement
					self.movement:setupCustomMovement(25)
					break
				end
				if _exp == (Enum.KeyCode.O) then
					-- Restore default movement
					self.movement:restoreDefaultMovement()
					break
				end
				if _exp == (Enum.KeyCode.P) then
					-- Check if moving
					local isMoving = self.movement:isMoving()
					local velocity = self.movement:getVelocity()
					print(`Moving: {isMoving}, Velocity: {velocity}`)
					break
				end
			until true
		end)
		print("🎮 Movement controls setup:")
		print("Q - Dash forward")
		print("E - Launch upward")
		print("R - Super speed")
		print("T - Normal speed")
		print("Y - Jump")
		print("U - Teleport to spawn")
		print("I - Enable custom WASD movement")
		print("O - Restore default movement")
		print("P - Check movement status")
	end
end
-- Initialize the movement example
MovementExample.new()
return {
	MovementExample = MovementExample,
}
