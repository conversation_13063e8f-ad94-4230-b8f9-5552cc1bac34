-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitFor<PERSON>hild("RuntimeLib"))
-- Add immediate debug output to verify script is running
print("🚀 [SERVER] main.server.ts is loading...")
print("📦 [SERVER] Importing modules...")
local initializeCoreFramework = TS.import(script, game:GetService("ReplicatedStorage"), "core").initializeCoreFramework
local WhitebeardAbilityServer = TS.import(script, game:GetService("ServerScriptService"), "TS", "abilities", "WhitebeardAbilityServer").WhitebeardAbilityServer
local WorldTestingServer = TS.import(script, game:GetService("ServerScriptService"), "TS", "world", "WorldTestingServer").WorldTestingServer
local ServerDataStoreService = TS.import(script, game:GetService("ServerScriptService"), "TS", "data", "DataStoreService").ServerDataStoreService
local ReplicatedStorage = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services").ReplicatedStorage
print("✅ [SERVER] All modules imported successfully")
-- Initialize the new enterprise-grade Core Framework
local initializeServer = TS.async(function()
	print("🚀 [SERVER] Starting server initialization...")
	print("🚀 [SERVER] Initializing server with enterprise Core Framework...")
	local _exitType, _returns = TS.try(function()
		local initResult = TS.await(initializeCoreFramework())
		if initResult:isError() then
			error(`[SERVER] Failed to initialize Core Framework: {initResult:getError().message}`)
			return TS.TRY_RETURN, {}
		end
		print("✅ [SERVER] Core Framework initialized successfully!")
		-- Initialize game-specific systems
		print("🔧 [SERVER] Initializing WhitebeardAbilityServer...")
		local whitebeardServer = WhitebeardAbilityServer.new()
		print("🔧 [SERVER] Initializing WorldTestingServer...")
		local worldTestingServer = WorldTestingServer.new()
		print("🔧 [SERVER] Initializing DataStoreService...")
		local dataStoreService = ServerDataStoreService:getInstance()
		print("🎮 [SERVER] Game server ready!")
		print("📡 [SERVER] RemoteEvents should now be available to clients")
		-- Create a server initialization indicator
		local serverIndicator = Instance.new("BoolValue")
		serverIndicator.Name = "CoreServerInitialized"
		serverIndicator.Value = true
		serverIndicator.Parent = ReplicatedStorage
		print("🚩 [SERVER] Created server initialization indicator in ReplicatedStorage")
	end, function(err)
		error(`[SERVER] Server initialization error: {err}`)
	end)
	if _exitType then
		return unpack(_returns)
	end
end)
-- Start server initialization
print("🚀 [SERVER] About to call initializeServer()...")
initializeServer():andThen(function()
	print("✅ [SERVER] Server initialization completed successfully!")
end, function(err)
	error(`[SERVER] Server initialization failed: {err}`)
end)
print("🔥 [SERVER] Playground server script executed! Starting initialization...")
