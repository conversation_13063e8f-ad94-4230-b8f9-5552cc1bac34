-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local createRoot = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react-roblox").createRoot
local Players = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services").Players
local BottomLeftGrid = TS.import(script, script.Parent, "gui", "BottomLeftGrid").BottomLeftGrid
local ActionBarDemo = TS.import(script, script.Parent, "gui", "ActionBarDemo").ActionBarDemo
local MovementExample = TS.import(script, script.Parent, "movement", "MovementExample").MovementExample
local _core = TS.import(script, game:GetService("ReplicatedStorage"), "core")
local initializeDebugSystem = _core.initializeDebugSystem
local initializeClientCore = _core.initializeClientCore
local version = "v1.3.5"
local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")
-- Create a ScreenGui with properties to make it visible
local screenGui = Instance.new("ScreenGui", playerGui)
screenGui.ResetOnSpawn = false
screenGui.IgnoreGuiInset = true
screenGui.ZIndexBehavior = Enum.ZIndexBehavior.Sibling
screenGui.DisplayOrder = 100
screenGui.Name = "MainReactGUI"
print(`🎮 [{tick()}] Main React ScreenGui created with DisplayOrder: {screenGui.DisplayOrder}`)
local root = createRoot(screenGui)
-- Render the GUI components
root:render(React.createElement(React.Fragment, nil, React.createElement(BottomLeftGrid, {
	onTestClick = function()
		return print("Test button clicked!")
	end,
	onHelloClick = function()
		return print("Hello button clicked!")
	end,
}), React.createElement(ActionBarDemo)))
-- Initialize movement example for testing
MovementExample.new()
-- Initialize systems
local initializeClient = TS.async(function()
	-- Initialize debug system for development
	initializeDebugSystem()
	-- Initialize client core
	local clientCoreResult = TS.await(initializeClientCore())
	if clientCoreResult:isError() then
		warn(`Failed to initialize client core: {clientCoreResult:getError().message}`)
	else
		print("✅ Client Core initialized successfully!")
	end
	print(`🔥 Playground client loaded! [{version}]`)
	print(`🎙️ Voice system available! Use voiceDemo methods in console`)
end)
-- Start client initialization
initializeClient():catch(function(err)
	error(`Client initialization failed: {err}`)
end)
