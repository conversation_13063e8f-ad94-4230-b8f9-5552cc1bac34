import { GravityZoneOptions, ForceZoneOptions, BarrierZoneOptions, PhysicsZone } from "./interfaces/PhysicsZoneOptions";
export declare class PhysicsImpactHelper {
    private static activeZones;
    private static zoneCounter;
    private static activeForces;
    private static updateConnection?;
    /**
     * Initialize the physics system
     */
    static initialize(): void;
    /**
     * Create a gravity manipulation zone (perfect for Room ability)
     */
    static createGravityZone(options: GravityZoneOptions): string;
    /**
     * Create a force zone (for pushing/pulling effects)
     */
    static createForceZone(options: ForceZoneOptions): string;
    /**
     * Create a barrier zone (invisible walls, domes)
     */
    static createBarrierZone(options: BarrierZoneOptions): string;
    /**
     * Update all active physics zones
     */
    private static updatePhysicsZones;
    /**
     * Update a specific physics zone
     */
    private static updateZone;
    /**
     * Find all objects within a physics zone
     */
    private static findObjectsInZone;
    /**
     * Get objects by type
     */
    private static getObjectsByType;
    /**
     * Check if an object is within a zone
     */
    private static isObjectInZone;
    /**
     * Add an object to a physics zone
     */
    private static addObjectToZone;
    /**
     * Remove an object from a physics zone
     */
    private static removeObjectFromZone;
    /**
     * Apply zone effects to an object
     */
    private static applyZoneEffect;
    /**
     * Initialize gravity effect on an object
     */
    private static initializeGravityEffect;
    /**
     * Apply gravity effect continuously
     */
    private static applyGravityEffect;
    /**
     * Initialize force effect on an object
     */
    private static initializeForceEffect;
    /**
     * Apply force effect continuously
     */
    private static applyForceEffect;
    /**
     * Get the main Part from an object (Model or Part)
     */
    private static getPartFromObject;
    /**
     * Clean up all effects on an object
     */
    private static cleanupObjectEffects;
    /**
     * Create visual representation of a zone
     */
    private static createZoneVisual;
    /**
     * Create barrier visual
     */
    private static createBarrierVisual;
    /**
     * Remove a physics zone
     */
    static removePhysicsZone(zoneId: string): void;
    /**
     * Clean up expired zones
     */
    private static cleanupExpiredZones;
    /**
     * Get all active physics zones
     */
    static getActiveZones(): Map<string, PhysicsZone>;
    /**
     * Shutdown the physics system
     */
    static shutdown(): void;
}
