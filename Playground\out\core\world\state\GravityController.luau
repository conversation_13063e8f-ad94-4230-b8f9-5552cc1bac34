-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local _services = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services")
local Workspace = _services.Workspace
local Players = _services.Players
local EffectPartBuilder = TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "EffectPartBuilder").EffectPartBuilder
local EffectTweenBuilder = TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "EffectTweenBuilder").EffectTweenBuilder
--[[
	*
	 * GravityController - Manages gravity levels for world and players
	 * Provides simple methods for changing gravity with different presets
	 
]]
local GravityController
do
	GravityController = setmetatable({}, {
		__tostring = function()
			return "GravityController"
		end,
	})
	GravityController.__index = GravityController
	function GravityController.new(...)
		local self = setmetatable({}, GravityController)
		return self:constructor(...) or self
	end
	function GravityController:constructor()
		self.currentGravityLevel = "normal"
		self.originalGravity = 196.2
		self.originalGravity = Workspace.Gravity
	end
	function GravityController:getInstance()
		if not GravityController.instance then
			GravityController.instance = GravityController.new()
		end
		return GravityController.instance
	end
	function GravityController:setGravity(options)
		self.currentGravityLevel = options.level
		local gravityValue
		if options.customValue ~= nil then
			gravityValue = options.customValue * self.originalGravity
		else
			gravityValue = self:getGravityValueForLevel(options.level)
		end
		-- Apply gravity to workspace
		Workspace.Gravity = gravityValue
		-- Apply gravity effects to players if specified
		if options.affectsPlayers ~= false then
			self:applyGravityToPlayers(gravityValue)
		end
		-- Apply gravity effects to objects if specified
		if options.affectsObjects ~= false then
			self:applyGravityToObjects(gravityValue)
		end
		print(`🌍 Gravity changed to: {options.level} ({gravityValue} units, {string.format("%.2f", gravityValue / self.originalGravity)}x)`)
	end
	function GravityController:getCurrentGravityLevel()
		return self.currentGravityLevel
	end
	function GravityController:getCurrentGravityValue()
		return Workspace.Gravity
	end
	function GravityController:getCurrentGravityMultiplier()
		return Workspace.Gravity / self.originalGravity
	end
	function GravityController:getGravityValueForLevel(level)
		repeat
			if level == "zero" then
				return self.originalGravity * 0.05
			end
			if level == "low" then
				return self.originalGravity * 0.3
			end
			if level == "normal" then
				return self.originalGravity * 1.0
			end
			if level == "high" then
				return self.originalGravity * 2.5
			end
			return self.originalGravity
		until true
	end
	function GravityController:applyGravityToPlayers(gravityValue)
		local gravityMultiplier = gravityValue / self.originalGravity
		for _, player in Players:GetPlayers() do
			local character = player.Character
			if not character then
				continue
			end
			local humanoid = character:FindFirstChild("Humanoid")
			if not humanoid then
				continue
			end
			-- Adjust jump power based on gravity
			-- Lower gravity = higher jump power
			local baseJumpPower = 50
			local adjustedJumpPower = baseJumpPower / math.sqrt(gravityMultiplier)
			humanoid.JumpPower = math.max(5, math.min(200, adjustedJumpPower))
			-- Adjust walk speed slightly based on gravity
			local baseWalkSpeed = 16
			local adjustedWalkSpeed = baseWalkSpeed * math.sqrt(1 / gravityMultiplier)
			humanoid.WalkSpeed = math.max(5, math.min(50, adjustedWalkSpeed))
		end
	end
	function GravityController:applyGravityToObjects(gravityValue)
		-- Find all unanchored parts and apply gravity effects
		local _exp = Workspace:GetDescendants()
		-- ▼ ReadonlyArray.filter ▼
		local _newValue = {}
		local _callback = function(obj)
			return obj:IsA("Part") and not obj.Anchored and obj.Parent ~= Players
		end
		local _length = 0
		for _k, _v in _exp do
			if _callback(_v, _k - 1, _exp) == true then
				_length += 1
				_newValue[_length] = _v
			end
		end
		-- ▲ ReadonlyArray.filter ▲
		local parts = _newValue
		for _, part in parts do
			-- Gravity affects mass and physics behavior
			-- Parts will naturally respond to the workspace gravity change
			-- Optional: Add special effects for extreme gravity changes
			local gravityMultiplier = gravityValue / self.originalGravity
			if gravityMultiplier < 0.2 then
				-- Very low gravity - parts float more
				local bodyVelocity = part:FindFirstChild("FloatEffect")
				if not bodyVelocity then
					-- Create floating particles for visual effect
					self:createFloatingParticles(part.Position, gravityMultiplier)
					-- Add float effect using raw BodyVelocity (needed for physics)
					local floatEffect = Instance.new("BodyVelocity")
					floatEffect.Name = "FloatEffect"
					floatEffect.MaxForce = Vector3.new(0, math.huge, 0)
					floatEffect.Velocity = Vector3.new(0, 2, 0)
					floatEffect.Parent = part
				end
			else
				-- Remove float effects for normal/high gravity
				local floatEffect = part:FindFirstChild("FloatEffect")
				if floatEffect then
					floatEffect:Destroy()
				end
			end
		end
	end
	function GravityController:createGravityZone(position, size, gravityLevel)
		-- Create zone using Core framework
		local zone = EffectPartBuilder:create():size(size):position(position):transparency(0.8):material(Enum.Material.ForceField):color(self:getGravityZoneColorAsColor3(gravityLevel)):spawn()
		zone.Name = `GravityZone_{gravityLevel}`
		zone.CanCollide = false
		-- Add zone detection
		local gravityValue = self:getGravityValueForLevel(gravityLevel)
		zone.Touched:Connect(function(hit)
			local character = hit.Parent
			local _result = character
			if _result ~= nil then
				_result = _result:FindFirstChild("Humanoid")
			end
			local humanoid = _result
			if humanoid then
				-- Apply zone-specific gravity effects to player
				local baseJumpPower = 50
				local gravityMultiplier = gravityValue / self.originalGravity
				local adjustedJumpPower = baseJumpPower / math.sqrt(gravityMultiplier)
				humanoid.JumpPower = math.max(5, math.min(200, adjustedJumpPower))
				-- Create visual effect when entering zone
				self:createGravityZoneEffect(zone.Position, gravityLevel)
				print(`Player entered {gravityLevel} gravity zone`)
			end
		end)
		print(`🌀 Created {gravityLevel} gravity zone at {position}`)
		return zone
	end
	function GravityController:createFloatingParticles(position, gravityMultiplier)
		-- Create floating particles for low gravity areas
		local numParticles = math.floor((1 - gravityMultiplier) * 10)
		do
			local i = 0
			local _shouldIncrement = false
			while true do
				if _shouldIncrement then
					i += 1
				else
					_shouldIncrement = true
				end
				if not (i < numParticles) then
					break
				end
				local _ = EffectPartBuilder:create():shape(Enum.PartType.Ball):size(Vector3.new(0.3, 0.3, 0.3)):color(Color3.new(0.8, 0.9, 1)):material(Enum.Material.Neon):transparency(0.6)
				local _position = position
				local _vector3 = Vector3.new(math.random(-5, 5), math.random(-2, 5), math.random(-5, 5))
				local particle = _:position(_position + _vector3):spawn()
				-- Make particles float upward slowly
				local _1 = EffectTweenBuilder["for"](EffectTweenBuilder, particle)
				local _position_1 = particle.Position
				local _vector3_1 = Vector3.new(0, 20, 0)
				_1:move(_position_1 + _vector3_1):duration(8):easing(Enum.EasingStyle.Sine, Enum.EasingDirection.InOut):onComplete(function()
					if particle.Parent then
						particle:Destroy()
					end
				end):play()
				-- Fade out over time
				EffectTweenBuilder["for"](EffectTweenBuilder, particle):fade(1):duration(6):delay(2):play()
			end
		end
	end
	function GravityController:createGravityZoneEffect(position, gravityLevel)
		-- Create visual effect when entering gravity zone
		local effectColor = self:getGravityZoneColorAsColor3(gravityLevel)
		for i = 0, 4 do
			local _ = EffectPartBuilder:create():shape(Enum.PartType.Ball):size(Vector3.new(0.5, 0.5, 0.5)):color(effectColor):material(Enum.Material.Neon):transparency(0.3)
			local _position = position
			local _vector3 = Vector3.new(math.random(-3, 3), math.random(-1, 3), math.random(-3, 3))
			local effect = _:position(_position + _vector3):spawn()
			-- Expand and fade effect
			EffectTweenBuilder["for"](EffectTweenBuilder, effect):expand(Vector3.new(2, 2, 2)):fade(1):duration(1.5):onComplete(function()
				if effect.Parent then
					effect:Destroy()
				end
			end):play()
		end
	end
	function GravityController:getGravityZoneColorAsColor3(level)
		repeat
			if level == "zero" then
				return Color3.new(0, 0.7, 1)
			end
			if level == "low" then
				return Color3.new(0, 1, 1)
			end
			if level == "normal" then
				return Color3.new(0, 1, 0)
			end
			if level == "high" then
				return Color3.new(1, 0, 0)
			end
			return Color3.new(0.5, 0.5, 0.5)
		until true
	end
	function GravityController:getGravityZoneColor(level)
		repeat
			if level == "zero" then
				return BrickColor.new("Bright blue")
			end
			if level == "low" then
				return BrickColor.new("Cyan")
			end
			if level == "normal" then
				return BrickColor.new("Bright green")
			end
			if level == "high" then
				return BrickColor.new("Bright red")
			end
			return BrickColor.new("Medium stone grey")
		until true
	end
	function GravityController:resetGravity()
		self:setGravity({
			level = "normal",
		})
	end
	function GravityController:cleanup()
		self:resetGravity()
		-- Remove all gravity zones
		local _exp = Workspace:GetChildren()
		-- ▼ ReadonlyArray.filter ▼
		local _newValue = {}
		local _callback = function(obj)
			return (string.find(obj.Name, "GravityZone_")) ~= nil
		end
		local _length = 0
		for _k, _v in _exp do
			if _callback(_v, _k - 1, _exp) == true then
				_length += 1
				_newValue[_length] = _v
			end
		end
		-- ▲ ReadonlyArray.filter ▲
		local gravityZones = _newValue
		for _, zone in gravityZones do
			zone:Destroy()
		end
		-- Remove float effects from all parts
		local _exp_1 = Workspace:GetDescendants()
		-- ▼ ReadonlyArray.filter ▼
		local _newValue_1 = {}
		local _callback_1 = function(obj)
			return obj:IsA("Part")
		end
		local _length_1 = 0
		for _k, _v in _exp_1 do
			if _callback_1(_v, _k - 1, _exp_1) == true then
				_length_1 += 1
				_newValue_1[_length_1] = _v
			end
		end
		-- ▲ ReadonlyArray.filter ▲
		local parts = _newValue_1
		for _, part in parts do
			local floatEffect = part:FindFirstChild("FloatEffect")
			if floatEffect then
				floatEffect:Destroy()
			end
		end
		print("🌍 Gravity effects cleaned up")
	end
end
return {
	GravityController = GravityController,
}
