import { AIBehavior } from "../interfaces/AIBehavior";
import { AIContext } from "../interfaces/AIContext";
import { AIBehaviorResult } from "../interfaces/AIBehaviorResult";

export class InvestigateBehavior implements AIBehavior {
	name = "Investigate";
	priority = 4;

	canExecute(context: AIContext): boolean {
		const investigationPoint = context.blackboard.investigationPoint as Vector3;
		return investigationPoint !== undefined;
	}

	execute(context: AIContext): AIBehaviorResult {
		const investigationPoint = context.blackboard.investigationPoint as Vector3;
		const distance = context.position.sub(investigationPoint).Magnitude;

		if (distance <= 3) {
			context.blackboard.investigationPoint = undefined;
			context.blackboard.investigationTime = 0;
			return { success: true, completed: true };
		}

		this.moveTowards(context, investigationPoint);
		return { success: true, completed: false };
	}

	onEnter(context: AIContext): void {
		print(`🔍 ${context.entityId} is investigating`);
	}

	private moveTowards(context: AIContext, targetPosition: Vector3): void {
		if (context.entity.IsA("Model") && context.entity.PrimaryPart) {
			const humanoid = context.entity.FindFirstChild("Humanoid") as Humanoid;
			if (humanoid) {
				humanoid.MoveTo(targetPosition);
			}
		}
	}
}
