interface InputProps {
    placeholder?: string;
    onChange: (text: string) => void;
    value?: string;
    size?: UDim2;
    position?: UDim2;
    anchorPoint?: Vector2;
    layoutOrder?: number;
    disabled?: boolean;
    multiline?: boolean;
    clearTextOnFocus?: boolean;
    textColor?: string;
    backgroundColor?: string;
    borderColor?: string;
    placeholderColor?: string;
}
export declare function Input(props: InputProps): JSX.Element;
export {};
