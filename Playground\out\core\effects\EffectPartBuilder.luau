-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local Workspace = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services").Workspace
local EffectPartBuilder
do
	EffectPartBuilder = setmetatable({}, {
		__tostring = function()
			return "EffectPartBuilder"
		end,
	})
	EffectPartBuilder.__index = EffectPartBuilder
	function EffectPartBuilder.new(...)
		local self = setmetatable({}, EffectPartBuilder)
		return self:constructor(...) or self
	end
	function EffectPartBuilder:constructor()
		self.part = Instance.new("Part")
		self.part.Anchored = true
		self.part.CanCollide = false
		self.part.Parent = Workspace
	end
	function EffectPartBuilder:create()
		return EffectPartBuilder.new()
	end
	function EffectPartBuilder:shape(shape)
		self.part.Shape = shape
		return self
	end
	function EffectPartBuilder:size(size)
		self.part.Size = size
		return self
	end
	function EffectPartBuilder:color(color)
		self.part.Color = color
		return self
	end
	function EffectPartBuilder:material(material)
		self.part.Material = material
		return self
	end
	function EffectPartBuilder:transparency(trans)
		self.part.Transparency = trans
		return self
	end
	function EffectPartBuilder:position(pos)
		self.part.Position = pos
		return self
	end
	function EffectPartBuilder:cframe(cf)
		self.part.CFrame = cf
		return self
	end
	function EffectPartBuilder:withLight(range, brightness, color)
		if range == nil then
			range = 10
		end
		if brightness == nil then
			brightness = 5
		end
		if color == nil then
			color = Color3.fromRGB(255, 255, 255)
		end
		local light = Instance.new("PointLight")
		light.Range = range
		light.Brightness = brightness
		light.Color = color
		light.Parent = self.part
		return self
	end
	function EffectPartBuilder:spawn()
		return self.part
	end
end
return {
	EffectPartBuilder = EffectPartBuilder,
}
