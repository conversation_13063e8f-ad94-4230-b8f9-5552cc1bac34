import * as React from "@rbxts/react";
import { Players, ReplicatedStorage } from "@rbxts/services";
import {
    ContainerFrame,
    VerticalFrame,
    Button,
    Label,
    IconButton,
    ScrollingFrame,
    EntityManager,
    EntityType,
    DataStoreResponse,
    BasePlayerData,
    AIController,
    LimbAnimator,
    AnimationBuilder,
    DataStoreHelper,
    DebugUtils,
    ZIndexManager,
    useUIState,
    ClientState,
    fireServer,
    Core,
    EffectPartBuilder,
    EffectTweenBuilder,
    playSound,
    createParticleExplosion,
    createParticleStorm,
    createImpactFlash,
    cameraShake,
    NetworkService,
    NetworkValidationService,
    StateManager,
    ResponsiveManager,
    Modal
} from "../../core";

interface WorldTestingPanelProps {
  // Props are now optional since we use state management
}

export function WorldTestingPanel(props: WorldTestingPanelProps): React.ReactElement {
  // Use Core state management instead of props
  const worldTestingState = useUIState("worldTestingPanel");
  const isPanelOpen = worldTestingState.isOpen;
  const player = Players.LocalPlayer;

  // Functions to control panel state
  const closePanel = () => {
    ClientState.updateUI("worldTestingPanel", { isOpen: false });
  };

  const openPanel = () => {
    ClientState.updateUI("worldTestingPanel", { isOpen: true });
  };

  // Use debug priority Z-Index to ensure this appears above debug overlay panels
  const zIndex = ZIndexManager.getDebugZIndex("world-testing-panel");

  // FORCE an extremely high z-index to ensure it's always on top
  const forceHighZIndex = math.max(zIndex, 5000);

  // Debug logging to verify z-index values
  React.useEffect(() => {
    const timestamp = tick();
    print(`🌍 [${timestamp}] World Testing Panel created with z-index: ${zIndex}`);
    print(`🌍 [${timestamp}] World Testing Panel FORCING z-index to: ${forceHighZIndex}`);
    print(`🌍 [${timestamp}] World Testing Panel applying z-index ${forceHighZIndex} to ContainerFrame`);

    // Check actual ScreenGui DisplayOrder values
    const player = Players.LocalPlayer;
    const playerGui = player.WaitForChild("PlayerGui") as PlayerGui;
    const mainReactGUI = playerGui.FindFirstChild("MainReactGUI") as ScreenGui;
    const debugGUI = playerGui.FindFirstChild("DebugGUI") as ScreenGui;

    if (mainReactGUI) {
      print(`🎮 [${timestamp}] MainReactGUI DisplayOrder: ${mainReactGUI.DisplayOrder}`);
    } else {
      print(`❌ [${timestamp}] MainReactGUI not found!`);
    }

    if (debugGUI) {
      print(`🔧 [${timestamp}] DebugGUI DisplayOrder: ${debugGUI.DisplayOrder}`);
    } else {
      print(`❌ [${timestamp}] DebugGUI not found!`);
    }

    print(`🌍 [${timestamp}] *** DisplayOrder determines ScreenGui layering - higher values appear on top ***`);

    // Print z-index summary after a short delay to see all assignments
    wait(0.1);
    ZIndexManager.printZIndexSummary();
  }, [zIndex, forceHighZIndex]);

  // Get player position for testing
  const getPlayerPosition = (): Vector3 => {
    const character = player.Character;
    const humanoidRootPart = character?.FindFirstChild("HumanoidRootPart") as Part;
    return humanoidRootPart?.Position || new Vector3(0, 10, 0);
  };

  // Send world test request to server using Core framework
  const sendWorldTestRequest = (testType: string) => {
    const position = getPlayerPosition();

    const result = fireServer(Core.eventName("WorldTesting"), {
      testType: testType,
      position: position,
      playerId: player.UserId
    });

    if (result.isError()) {
      warn(`Failed to send world test: ${result.getError().message}`);
    } else {
      print(`🌍 Sent ${testType} test request to server`);
    }
  };

  // Gravity test functions
  const testLowGravity = () => sendWorldTestRequest("low_gravity_world");
  const testHighGravity = () => sendWorldTestRequest("high_gravity_world");
  const testZeroGravity = () => sendWorldTestRequest("zero_gravity_world");
  const testNormalGravity = () => sendWorldTestRequest("normal_gravity_world");

  // Weather test functions
  const testClearWeather = () => sendWorldTestRequest("clear_weather");
  const testRain = () => sendWorldTestRequest("rain_weather");
  const testHeavyRain = () => sendWorldTestRequest("heavy_rain_weather");
  const testSnow = () => sendWorldTestRequest("snow_weather");
  const testBlizzard = () => sendWorldTestRequest("blizzard_weather");
  const testStorm = () => sendWorldTestRequest("storm_weather");
  const testThunderstorm = () => sendWorldTestRequest("thunderstorm_weather");
  const testFog = () => sendWorldTestRequest("fog_weather");
  const testSandstorm = () => sendWorldTestRequest("sandstorm_weather");

  // Day/Night test functions
  const testDawn = () => sendWorldTestRequest("dawn_time");
  const testNoon = () => sendWorldTestRequest("noon_time");
  const testDusk = () => sendWorldTestRequest("dusk_time");
  const testNight = () => sendWorldTestRequest("night_time");

  // Entity Management test functions
  const entityManager = EntityManager.getInstance();

  const spawnNPC = () => {
    const position = getPlayerPosition().add(new Vector3(5, 0, 0));
    const entity = entityManager.spawnEntity({
      type: EntityType.NPC,
      position: position,
      data: { name: "Test NPC", health: 100 }
    });
    print(`🤖 Spawned NPC with ID: ${entity.id} at position: ${position}`);
  };

  const spawnProjectile = () => {
    const position = getPlayerPosition().add(new Vector3(0, 5, 0));
    const entity = entityManager.spawnEntity({
      type: EntityType.Projectile,
      position: position,
      lifetime: 5, // Auto-destroy after 5 seconds
      data: { speed: 50, damage: 25 }
    });
    print(`🚀 Spawned Projectile with ID: ${entity.id} at position: ${position}`);
  };

  const spawnEffect = () => {
    const position = getPlayerPosition().add(new Vector3(-5, 0, 0));
    const entity = entityManager.spawnEntity({
      type: EntityType.Effect,
      position: position,
      lifetime: 3, // Auto-destroy after 3 seconds
      data: { effectType: "explosion", intensity: 0.8 }
    });
    print(`✨ Spawned Effect with ID: ${entity.id} at position: ${position}`);
  };

  const spawnPickup = () => {
    const position = getPlayerPosition().add(new Vector3(0, 0, 5));
    const entity = entityManager.spawnEntity({
      type: EntityType.Pickup,
      position: position,
      data: { itemType: "health_potion", value: 50 }
    });
    print(`💎 Spawned Pickup with ID: ${entity.id} at position: ${position}`);
  };

  const listEntities = () => {
    const activeCount = entityManager.getActiveEntityCount();
    print(`📊 Active entities: ${activeCount}`);

    const npcs = entityManager.getEntitiesByType(EntityType.NPC);
    const projectiles = entityManager.getEntitiesByType(EntityType.Projectile);
    const effects = entityManager.getEntitiesByType(EntityType.Effect);
    const pickups = entityManager.getEntitiesByType(EntityType.Pickup);

    print(`🤖 NPCs: ${npcs.size()}`);
    print(`🚀 Projectiles: ${projectiles.size()}`);
    print(`✨ Effects: ${effects.size()}`);
    print(`💎 Pickups: ${pickups.size()}`);
  };

  const findNearbyEntities = () => {
    const playerPos = getPlayerPosition();
    const nearbyEntities = entityManager.getEntitiesInRadius(playerPos, 20);
    print(`🔍 Found ${nearbyEntities.size()} entities within 20 studs of player`);

    nearbyEntities.forEach((entity) => {
      const distance = entity.position.sub(playerPos).Magnitude;
      print(`  - ${entity.type} (ID: ${entity.id}) at distance: ${math.floor(distance)} studs`);
    });
  };

  const cleanupAllEntities = () => {
    const beforeCount = entityManager.getActiveEntityCount();
    entityManager.cleanup();
    print(`🧹 Cleaned up ${beforeCount} entities`);
  };

  // Data Persistence test functions using RemoteEvents
  const sendDataStoreRequest = (action: string, data?: any) => {
    const result = fireServer(Core.eventName("DataStoreTesting"), { action, data });

    if (result.isError()) {
      warn(`Failed to send data store request: ${result.getError().message}`);
    } else {
      print(`📡 Sent ${action} request to server`);
    }
  };

  // Set up response handler
  React.useEffect(() => {
    const remoteEventsFolder = ReplicatedStorage.FindFirstChild("RemoteEvents") as Folder;
    const responseEvent = remoteEventsFolder?.FindFirstChild("DATA_STORE_RESPONSE") as RemoteEvent;

    if (responseEvent) {
      const connection = responseEvent.OnClientEvent.Connect((response: unknown) => {
        const typedResponse = response as DataStoreResponse;
        const { action, result } = typedResponse;

        if (result.success) {
          switch (action) {
            case "loadPlayerData":
              if (result.data) {
                const playerData = result.data as BasePlayerData;
                print(`📊 Loaded player data:`);
                print(`  User ID: ${playerData.userId}`);
                print(`  Created: ${playerData.createdAt}`);
                print(`  Last Login: ${playerData.lastLogin}`);
                print(`  Playtime: ${math.floor(playerData.playtime)} seconds`);
                print(`  Total Sessions: ${playerData.metadata.totalSessions}`);
                print(`  Music Volume: ${playerData.settings.musicVolume}`);
                print(`  Game Data:`, playerData.gameData);
              }
              break;
            case "updateGameData":
              if (result.data) {
                const updatedData = result.data as BasePlayerData;
                print(`🎮 Updated game data!`);
                print(`  Game Data:`, updatedData.gameData);
              }
              break;
            case "updateSettings":
              if (result.data) {
                const settings = result.data as { musicVolume: number; graphics: string };
                print(`⚙️ Updated settings:`);
                print(`  Music Volume: ${settings.musicVolume}`);
                print(`  Graphics: ${settings.graphics}`);
              }
              break;
            case "getStats":
              if (result.data) {
                const stats = result.data as { cacheSize: number; loadedPlayers: number; isPlayerLoaded: boolean };
                print(`📈 DataStore Statistics:`);
                print(`  Cache entries: ${stats.cacheSize}`);
                print(`  Loaded players: ${stats.loadedPlayers}`);
                print(`  Player loaded: ${stats.isPlayerLoaded}`);
              }
              break;
          }
        } else {
          print(`❌ ${action} failed: ${result.error}`);
        }
      });

      return () => connection.Disconnect();
    }
  }, []);

  const testLoadPlayerData = () => {
    sendDataStoreRequest("loadPlayerData");
  };

  const testUpdateGameData = () => {
    sendDataStoreRequest("updateGameData", {
      gameDataUpdates: {
        coins: 100,
        level: 5,
        experience: 250,
        lastAction: "test_update",
        timestamp: tick()
      }
    });
  };

  const testUpdateSettings = () => {
    sendDataStoreRequest("updateSettings", {
      musicVolume: 0.6,
      graphics: "High"
    });
  };

  const testDataStoreStats = () => {
    sendDataStoreRequest("getStats");
  };

  const enableStudioDataStore = () => {
    // Access the DataStoreHelper directly to enable Studio testing
    const dataStore = DataStoreHelper.getInstance();
    dataStore.enableStudioTesting();
  };

  const disableAutoSave = () => {
    // Access the DataStoreHelper directly to disable auto-save
    const dataStore = DataStoreHelper.getInstance();
    dataStore.disableAutoSave();
  };

  // AI System test functions
  const aiController = AIController.getInstance();
  const [spawnedAIEntities, setSpawnedAIEntities] = React.useState<string[]>([]);

  const spawnAINPC = () => {
    const position = getPlayerPosition().add(new Vector3(10, 0, 0));
    const entity = entityManager.spawnEntity({
      type: EntityType.NPC,
      position: position,
      data: { name: "AI NPC", aiEnabled: true }
    });

    // Register AI for this entity
    const aiAgent = aiController.registerAI(entity.id, {
      detectionRange: 30,
      followRange: 20,
      moveSpeed: 12,
      patrolRadius: 15
    });

    setSpawnedAIEntities([...spawnedAIEntities, entity.id]);
    print(`🤖 Spawned AI NPC with ID: ${entity.id}`);
    print(`🧠 AI registered with default behaviors`);
  };

  const spawnPatrollingNPC = () => {
    const position = getPlayerPosition().add(new Vector3(-10, 0, 10));
    const entity = entityManager.spawnEntity({
      type: EntityType.NPC,
      position: position,
      data: { name: "Patrol NPC", behavior: "patrol" }
    });

    // Register AI with patrol focus
    aiController.registerAI(entity.id, {
      detectionRange: 15, // Shorter detection range
      followRange: 8,
      moveSpeed: 8,
      patrolRadius: 20
    });

    setSpawnedAIEntities([...spawnedAIEntities, entity.id]);
    print(`🚶 Spawned Patrolling NPC with ID: ${entity.id}`);
  };

  const spawnFleeingNPC = () => {
    const position = getPlayerPosition().add(new Vector3(0, 0, -10));
    const entity = entityManager.spawnEntity({
      type: EntityType.NPC,
      position: position,
      data: { name: "Scared NPC", behavior: "flee" }
    });

    // Register AI that prefers to flee
    const aiAgent = aiController.registerAI(entity.id, {
      detectionRange: 25,
      followRange: 5, // Very short follow range
      moveSpeed: 16,
      patrolRadius: 10
    });

    // Set initial flee state
    aiAgent.setBlackboardValue("shouldFlee", true);

    setSpawnedAIEntities([...spawnedAIEntities, entity.id]);
    print(`😱 Spawned Fleeing NPC with ID: ${entity.id}`);
  };

  const makeNPCInvestigate = () => {
    if (spawnedAIEntities.size() === 0) {
      print("❌ No AI NPCs spawned yet!");
      return;
    }

    const entityId = spawnedAIEntities[0];
    const aiAgent = aiController.getAI(entityId);

    if (aiAgent) {
      const investigatePos = getPlayerPosition().add(new Vector3(5, 0, 5));
      aiAgent.setBlackboardValue("investigatePosition", investigatePos);
      print(`🔍 ${entityId} will investigate position: ${investigatePos}`);
    }
  };

  const showAIStats = () => {
    const aiCount = aiController.getAICount();
    const allAIs = aiController.getAllAIs();

    print(`🧠 AI System Statistics:`);
    print(`  Total AI entities: ${aiCount}`);
    print(`  Spawned entities: ${spawnedAIEntities.size()}`);

    allAIs.forEach((aiAgent, index) => {
      print(`  AI ${index + 1}: State = ${aiAgent.getState()}`);
    });
  };

  const cleanupAllAI = () => {
    spawnedAIEntities.forEach((entityId) => {
      aiController.unregisterAI(entityId);
      entityManager.destroyEntity(entityId);
    });

    setSpawnedAIEntities([]);
    print(`🧹 Cleaned up all AI entities`);
  };

  const demonstrateDebugSystem = () => {
    const playerPos = getPlayerPosition();

    // Draw some debug lines and shapes
    DebugUtils.drawLine(playerPos, playerPos.add(new Vector3(10, 0, 0)), Color3.fromRGB(255, 0, 0), 5);
    DebugUtils.drawLine(playerPos, playerPos.add(new Vector3(0, 10, 0)), Color3.fromRGB(0, 255, 0), 5);
    DebugUtils.drawLine(playerPos, playerPos.add(new Vector3(0, 0, 10)), Color3.fromRGB(0, 0, 255), 5);

    // Draw some spheres
    DebugUtils.drawSphere(playerPos.add(new Vector3(5, 5, 5)), 2, Color3.fromRGB(255, 255, 0), 5);
    DebugUtils.drawSphere(playerPos.add(new Vector3(-5, 5, -5)), 1.5, Color3.fromRGB(255, 0, 255), 5);

    // Draw some text
    DebugUtils.drawText(playerPos.add(new Vector3(0, 8, 0)), "Debug System Demo!", Color3.fromRGB(255, 255, 255), 5);
    DebugUtils.drawText(playerPos.add(new Vector3(5, 3, 0)), "Red = X Axis", Color3.fromRGB(255, 0, 0), 5);
    DebugUtils.drawText(playerPos.add(new Vector3(0, 13, 0)), "Green = Y Axis", Color3.fromRGB(0, 255, 0), 5);
    DebugUtils.drawText(playerPos.add(new Vector3(0, 3, 5)), "Blue = Z Axis", Color3.fromRGB(0, 0, 255), 5);

    // Log debug messages
    DebugUtils.log("Demo", "Debug system demonstration started", { position: playerPos });
    DebugUtils.log("Demo", "Drawing coordinate axes and markers");

    print(`🔍 Debug demonstration active! Press F3 to toggle debug overlay`);
    print(`🎨 Visual elements will appear for 5 seconds`);
  };

  // Existing Animation System test functions
  const [limbAnimator, setLimbAnimator] = React.useState<LimbAnimator | undefined>();

  const initializeAnimationSystem = () => {
    const player = Players.LocalPlayer;
    const character = player.Character;

    if (!character) {
      print("❌ No character found! Spawn first.");
      return;
    }

    try {
      const limbAnim = LimbAnimator.forCharacter(character);
      setLimbAnimator(limbAnim);

      print("🎬 Existing animation system initialized!");
      print("🦴 Using LimbAnimator with moveLimb, moveBodyPart, and lunge methods");
    } catch (error) {
      print(`❌ Failed to initialize animation system: ${error}`);
    }
  };

  const testLimbAnimation = () => {
    if (!limbAnimator) {
      print("❌ Animation system not initialized!");
      return;
    }

    print("👋 Testing limb animation...");

    // Move right arm up (wave gesture)
    limbAnimator.moveLimb("RightShoulder",
      new CFrame().mul(CFrame.fromEulerAnglesXYZ(0, 0, math.rad(-90))),
      0.5
    );

    print("👋 Wave animation started!");
  };

  const testPresetAnimations = () => {
    if (!limbAnimator) {
      print("❌ Animation system not initialized!");
      return;
    }

    print("🦾 Testing preset animations...");

    // Test lunge animation (existing method)
    limbAnimator.lunge(new Vector3(1, 0, 0), 5, 0.5);
    print("🏃 Lunge animation started!");

    // Test body part movement
    limbAnimator.moveBodyPart("Head",
      new CFrame().mul(CFrame.fromEulerAnglesXYZ(0, math.rad(30), 0)),
      0.5
    );
    print("🗣️ Head turn started!");
  };

  const showAnimationStats = () => {
    if (!limbAnimator) {
      print("❌ Animation system not initialized!");
      return;
    }

    print("🎬 Animation System Statistics:");
    print("  LimbAnimator: Available for moveLimb, moveBodyPart, and lunge");
    print("  AnimationBuilder: Available for complex animation sequences");
  };

  const cleanupAnimationSystem = () => {
    if (limbAnimator) {
      setLimbAnimator(undefined);
    }

    print("🧹 Animation system cleaned up");
  };

  // Effects System test functions
  const testBasicEffects = () => {
    const position = getPlayerPosition().add(new Vector3(0, 5, 0));
    print("✨ Testing basic effects...");

    // Create a glowing sphere effect
    const sphere = EffectPartBuilder.create()
      .shape(Enum.PartType.Ball)
      .size(new Vector3(2, 2, 2))
      .color(Color3.fromRGB(0, 255, 255))
      .material(Enum.Material.Neon)
      .transparency(0.3)
      .position(position)
      .withLight(15, 10, Color3.fromRGB(0, 255, 255))
      .spawn();

    // Animate the sphere
    EffectTweenBuilder.for(sphere)
      .expand(new Vector3(8, 8, 8))
      .fade(1)
      .duration(3)
      .easing(Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
      .onComplete(() => sphere.Destroy())
      .play();

    // Play sound effect
    playSound("rbxassetid://131961136", 0.5, 1.2, sphere);
    print("🔮 Basic sphere effect created!");
  };

  const testParticleEffects = () => {
    const position = getPlayerPosition().add(new Vector3(3, 0, 0));
    print("🎆 Testing particle effects...");

    // Create particle explosion
    createParticleExplosion(position, 15, Color3.fromRGB(255, 100, 0), [10, 25], [0.3, 1.0]);

    // Create particle storm
    createParticleStorm(position.add(new Vector3(0, 10, 0)), 20, Color3.fromRGB(255, 255, 0), [5, 15]);

    print("💥 Particle effects created!");
  };

  const testVisualEffects = () => {
    const position = getPlayerPosition().add(new Vector3(-3, 0, 0));
    print("🌟 Testing visual effects...");

    // Create impact flash
    createImpactFlash(position, 6, 0.5);

    // Create camera shake
    cameraShake(3, 0.4);

    // Create expanding ring effect
    const ring = EffectPartBuilder.create()
      .shape(Enum.PartType.Cylinder)
      .size(new Vector3(0.5, 1, 0.5))
      .color(Color3.fromRGB(255, 0, 255))
      .material(Enum.Material.ForceField)
      .transparency(0.5)
      .position(position)
      .spawn();

    EffectTweenBuilder.for(ring)
      .expand(new Vector3(0.5, 20, 20))
      .fade(1)
      .duration(2)
      .easing(Enum.EasingStyle.Sine, Enum.EasingDirection.Out)
      .onComplete(() => ring.Destroy())
      .play();

    print("💫 Visual effects created!");
  };

  const testSoundEffects = () => {
    print("🔊 Testing sound effects...");

    // Test different sound effects
    playSound("rbxassetid://131961136", 0.7, 1.0); // Explosion
    task.wait(0.5);
    playSound("rbxassetid://131961136", 0.5, 1.5); // High pitch
    task.wait(0.5);
    playSound("rbxassetid://131961136", 0.8, 0.8); // Low pitch

    print("🎵 Sound effects played!");
  };

  const testComplexEffectCombo = () => {
    const position = getPlayerPosition().add(new Vector3(0, 8, 0));
    print("🎭 Testing complex effect combination...");

    // Create multiple layered effects
    for (let i = 0; i < 3; i++) {
      const offset = new Vector3(math.random(-5, 5), math.random(-2, 2), math.random(-5, 5));
      const effectPos = position.add(offset);

      const effect = EffectPartBuilder.create()
        .shape(Enum.PartType.Ball)
        .size(new Vector3(1, 1, 1))
        .color(Color3.fromRGB(math.random(100, 255), math.random(100, 255), math.random(100, 255)))
        .material(Enum.Material.Neon)
        .transparency(0.2)
        .position(effectPos)
        .withLight(12, 8)
        .spawn();

      EffectTweenBuilder.for(effect)
        .expand(new Vector3(6, 6, 6))
        .fade(1)
        .duration(2 + i * 0.5)
        .delay(i * 0.3)
        .easing(Enum.EasingStyle.Elastic, Enum.EasingDirection.Out)
        .onComplete(() => {
          // Create explosion when effect completes
          createParticleExplosion(effect.Position, 8, effect.Color, [3, 8], [0.2, 0.6]);
          effect.Destroy();
        })
        .play();
    }

    // Add sound sequence
    playSound("rbxassetid://131961136", 0.6, 1.0);
    task.delay(1, () => playSound("rbxassetid://131961136", 0.8, 1.3));
    task.delay(2, () => playSound("rbxassetid://131961136", 1.0, 0.9));

    print("🎪 Complex effect combination created!");
  };

  // Ability System test functions
  const testAbilityValidation = () => {
    print("⚔️ Testing ability validation...");

    // Test ability request validation
    const validationService = Core.getValidationService();
    if (validationService.isError()) {
      print("❌ Failed to get validation service");
      return;
    }

    const testRequest = {
      abilityId: "ROOM_ABILITY",
      targetPosition: getPlayerPosition().add(new Vector3(0, 0, 10)),
      targetDirection: new Vector3(0, 0, 1),
      timestamp: tick()
    };

    print("🔍 Validating ability request...");
    print(`  Ability ID: ${testRequest.abilityId}`);
    print(`  Target Position: ${testRequest.targetPosition}`);
    print(`  Timestamp: ${testRequest.timestamp}`);
    print("✅ Ability validation test completed!");
  };

  const testAbilityCooldowns = () => {
    print("⏰ Testing ability cooldowns...");

    // Simulate ability cooldown system
    const abilities = ["ROOM_ABILITY", "QUAKE_ABILITY", "HAKI_BURST", "GEAR_SECOND"];

    abilities.forEach((abilityId, index) => {
      const cooldownTime = 5 + (index * 2); // Different cooldowns
      print(`🔥 ${abilityId}: ${cooldownTime}s cooldown`);

      // Simulate cooldown end time
      const endTime = tick() + cooldownTime;
      print(`  ⏳ Cooldown ends at: ${endTime}`);
    });

    print("✅ Cooldown system test completed!");
  };

  const testAbilityEffects = () => {
    const position = getPlayerPosition().add(new Vector3(0, 0, 8));
    print("💥 Testing ability-style effects...");

    // Room Ability Effect
    const roomSphere = EffectPartBuilder.create()
      .shape(Enum.PartType.Ball)
      .size(new Vector3(1, 1, 1))
      .color(Color3.fromRGB(100, 200, 255))
      .material(Enum.Material.ForceField)
      .transparency(0.7)
      .position(position)
      .withLight(25, 15, Color3.fromRGB(100, 200, 255))
      .spawn();

    EffectTweenBuilder.for(roomSphere)
      .expand(new Vector3(30, 30, 30))
      .duration(2)
      .easing(Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
      .onComplete(() => {
        // Create inner sphere
        const innerSphere = EffectPartBuilder.create()
          .shape(Enum.PartType.Ball)
          .size(new Vector3(25, 25, 25))
          .color(Color3.fromRGB(150, 220, 255))
          .material(Enum.Material.Neon)
          .transparency(0.9)
          .position(position)
          .spawn();

        EffectTweenBuilder.for(innerSphere)
          .fade(1)
          .duration(3)
          .onComplete(() => {
            roomSphere.Destroy();
            innerSphere.Destroy();
          })
          .play();
      })
      .play();

    playSound("rbxassetid://131961136", 0.8, 0.7);
    print("🌐 Room ability effect created!");
  };

  // UI Component testing functions
  const [testModalOpen, setTestModalOpen] = React.useState(false);
  const [testDropdownOpen, setTestDropdownOpen] = React.useState(false);

  const testResponsiveDesign = () => {
    print("📱 Testing responsive design...");

    // Test ResponsiveManager
    const responsiveManager = ResponsiveManager.getInstance();

    print(`📊 Responsive Manager Status:`);
    print(`  Manager initialized: ${responsiveManager !== undefined}`);
    print(`  Testing responsive behavior...`);

    // Test responsive behavior by checking current viewport
    const viewport = game.GetService("Workspace").CurrentCamera?.ViewportSize;
    if (viewport) {
      print(`  Current Viewport: ${viewport.X} x ${viewport.Y}`);
    }

    print("✅ Responsive design test completed!");
  };

  const testZIndexManager = () => {
    print("🔢 Testing ZIndex Manager...");

    // Test ZIndex management
    const debugZIndex = ZIndexManager.getZIndex("DEBUG_PANEL");
    const worldTestZIndex = ZIndexManager.getZIndex("WORLD_TEST_PANEL");
    const modalZIndex = ZIndexManager.getZIndex("MODAL");

    print(`📊 ZIndex Values:`);
    print(`  Debug Panel: ${debugZIndex}`);
    print(`  World Test Panel: ${worldTestZIndex}`);
    print(`  Modal: ${modalZIndex}`);

    print("✅ ZIndex Manager test completed!");
  };

  const testModalSystem = () => {
    print("🪟 Testing Modal System...");
    setTestModalOpen(true);
    print("✅ Test modal opened!");
  };

  const testUIComponents = () => {
    print("🎨 Testing UI Components...");

    // Test various UI states
    print("📊 UI Component Status:");
    print(`  Modal Open: ${testModalOpen}`);
    print(`  Dropdown Open: ${testDropdownOpen}`);
    print(`  Panel Open: ${isPanelOpen}`);

    print("✅ UI Components test completed!");
  };

  // Networking & Validation testing functions
  const testNetworkService = () => {
    print("🌐 Testing Network Service...");

    const networkService = Core.getNetworkService();
    if (networkService.isError()) {
      print("❌ Failed to get network service");
      return;
    }

    print("📊 Network Service Status:");
    print("  ✅ Network service available");
    print("  🔗 Testing server communication...");

    // Test network communication
    const testData = {
      testType: "network_validation",
      timestamp: tick(),
      playerId: player.UserId
    };

    const result = fireServer(Core.eventName("NetworkTest"), testData);
    if (result.isError()) {
      print(`❌ Network test failed: ${result.getError().message}`);
    } else {
      print("✅ Network test sent successfully!");
    }
  };

  const testValidationService = () => {
    print("🔍 Testing Validation Service...");

    const validationService = Core.getValidationService();
    if (validationService.isError()) {
      print("❌ Failed to get validation service");
      return;
    }

    print("📊 Validation Service Status:");
    print("  ✅ Validation service available");
    print("  🔍 Testing data validation...");

    // Test various validation scenarios
    const testCases = [
      { data: { playerId: player.UserId, action: "test" }, expected: "valid" },
      { data: { playerId: -1, action: "invalid" }, expected: "invalid" },
      { data: undefined, expected: "invalid" }
    ];

    testCases.forEach((testCase, index) => {
      print(`  Test ${index + 1}: ${testCase.expected} data`);
      print(`    Data: ${testCase.data ? "provided" : "undefined"}`);
    });

    print("✅ Validation service test completed!");
  };

  const testStateManagement = () => {
    print("📊 Testing State Management...");

    // Create a test state manager
    const testStateManager = Core.createStateManager({
      counter: 0,
      testValue: "initial"
    }, "WorldTestingState");

    print("📊 State Manager Status:");
    print("  ✅ State manager created");

    // Test state operations
    const currentState = testStateManager.getState();
    print(`  Current state: counter=${currentState.counter}, testValue="${currentState.testValue}"`);

    // Update state
    testStateManager.dispatch({
      type: "UPDATE_COUNTER",
      payload: { counter: currentState.counter + 1 },
      reducer: (state: { counter: number; testValue: string }, payload: { counter: number }) => ({
        ...state,
        counter: payload.counter
      })
    });

    const newState = testStateManager.getState();
    print(`  Updated state: counter=${newState.counter}`);

    print("✅ State management test completed!");
  };

  // Advanced Systems testing functions
  const testCoreFramework = () => {
    print("🚀 Testing Core Framework...");

    const coreInstance = Core.getInstance();
    print("📊 Core Framework Status:");
    print(`  ✅ Core instance available: ${coreInstance !== undefined}`);

    // Test branded types
    const testPlayerId = Core.playerId(player.UserId);
    const testEntityId = Core.entityId("test-entity-123");
    const testEventName = Core.eventName("TestEvent");

    print("🏷️ Branded Types Test:");
    print(`  Player ID: ${testPlayerId}`);
    print(`  Entity ID: ${testEntityId}`);
    print(`  Event Name: ${testEventName}`);

    print("✅ Core Framework test completed!");
  };

  const testErrorHandling = () => {
    print("⚠️ Testing Error Handling...");

    try {
      print("📊 Error Handling Status:");

      // Test Result pattern
      const networkService = Core.getNetworkService();
      if (networkService.isError()) {
        print(`  ❌ Network service error: ${networkService.getError().message}`);
      } else {
        print("  ✅ Network service available");
      }

      const validationService = Core.getValidationService();
      if (validationService.isError()) {
        print(`  ❌ Validation service error: ${validationService.getError().message}`);
      } else {
        print("  ✅ Validation service available");
      }

      print("✅ Error handling test completed!");
    } catch (error) {
      print(`❌ Error handling test failed: ${error}`);
    }
  };

  const testPerformanceMonitoring = () => {
    print("⚡ Testing Performance Monitoring...");

    const startTime = tick();

    // Simulate some work
    for (let i = 0; i < 1000; i++) {
      math.random();
    }

    const endTime = tick();
    const duration = endTime - startTime;

    print("📊 Performance Metrics:");
    print(`  ✅ Test duration: ${string.format("%.4f", duration)}s`);
    print(`  ✅ Operations completed: 1000`);
    print(`  ✅ Ops/second: ${string.format("%.0f", 1000 / duration)}`);

    // Test memory usage (basic check)
    const memoryStats = game.GetService("Stats").GetTotalMemoryUsageMb();
    print(`  📊 Memory usage: ${string.format("%.2f", memoryStats)} MB`);

    print("✅ Performance monitoring test completed!");
  };

  const testRemoteEventStatus = () => {
    print("📡 Testing RemoteEvent Status...");

    // Check if RemoteEvents folder exists
    const remoteEventsFolder = ReplicatedStorage.FindFirstChild("RemoteEvents");
    if (!remoteEventsFolder) {
      print("❌ RemoteEvents folder not found in ReplicatedStorage");
      print("🔍 Checking ReplicatedStorage contents:");
      const children = ReplicatedStorage.GetChildren();
      children.forEach((child) => {
        print(`  📁 ${child.Name} (${child.ClassName})`);
      });
      return;
    }

    print("✅ RemoteEvents folder found");
    print(`📊 RemoteEvents in folder: ${remoteEventsFolder.GetChildren().size()}`);

    // List all RemoteEvents
    const remoteEvents = remoteEventsFolder.GetChildren();
    print("📡 All RemoteEvents:");
    remoteEvents.forEach((child) => {
      if (child.IsA("RemoteEvent")) {
        print(`  ✅ ${child.Name}`);
      } else {
        print(`  ❓ ${child.Name} (${child.ClassName})`);
      }
    });

    // Test specific events we need
    const testEvents = [
      "WhitebeardEffectReplicate",
      "WhitebeardVisualSync",
      "WorldTesting",
      "NetworkTest"
    ];

    print("🔍 Checking specific events:");
    testEvents.forEach((eventName) => {
      const event = remoteEventsFolder.FindFirstChild(eventName);
      if (event) {
        print(`  ✅ ${eventName}: Found`);
      } else {
        print(`  ❌ ${eventName}: Missing`);
      }
    });

    // Check server initialization status
    print("🔍 Server Status Check:");
    print("  If events are missing, the server may not be fully initialized yet.");
    print("  Try waiting a few seconds and testing again.");

    print("✅ RemoteEvent status check completed!");
  };

  const testServerStatus = () => {
    print("🖥️ Testing Server Status...");

    // Check if we're in a server environment
    const runService = game.GetService("RunService");
    if (runService.IsStudio()) {
      print("🎮 Running in Roblox Studio");
      if (runService.IsServer()) {
        print("  ✅ Server context available");
      } else {
        print("  ❌ No server context (client-only)");
      }
    } else {
      print("🌐 Running in live game");
    }

    // Check for server scripts
    const serverScriptService = game.GetService("ServerScriptService");
    const serverScripts = serverScriptService.GetChildren();
    print(`📊 Server scripts found: ${serverScripts.size()}`);

    serverScripts.forEach((serverScript) => {
      if (serverScript.IsA("Script") || serverScript.IsA("ModuleScript")) {
        print(`  📜 ${serverScript.Name}`);
      }
    });

    // Check if Core server is initialized
    const coreServerIndicator = ReplicatedStorage.FindFirstChild("CoreServerInitialized");
    if (coreServerIndicator) {
      print("✅ Core server appears to be initialized");
    } else {
      print("❌ Core server may not be initialized yet");
      print("   Try waiting a few seconds for server startup");
    }

    print("✅ Server status check completed!");
  };

  const testAllSystems = async () => {
    print("🚀 ========================================");
    print("🚀 COMPREHENSIVE CORE FRAMEWORK TEST");
    print("🚀 ========================================");
    print("");

    // Test 1: Server Status
    print("📋 TEST 1/10: Server Status");
    print("----------------------------------------");
    testServerStatus();
    await new Promise(() => task.wait(1));
    print("");

    // Test 2: RemoteEvent Status
    print("📋 TEST 2/10: RemoteEvent Status");
    print("----------------------------------------");
    testRemoteEventStatus();
    await new Promise(() => task.wait(1));
    print("");

    // Test 3: Core Framework
    print("📋 TEST 3/10: Core Framework");
    print("----------------------------------------");
    testCoreFramework();
    await new Promise(() => task.wait(1));
    print("");

    // Test 4: Error Handling
    print("📋 TEST 4/10: Error Handling");
    print("----------------------------------------");
    testErrorHandling();
    await new Promise(() => task.wait(1));
    print("");

    // Test 5: Networking & Validation
    print("📋 TEST 5/10: Networking & Validation");
    print("----------------------------------------");
    testNetworkService();
    await new Promise(() => task.wait(0.5));
    testValidationService();
    await new Promise(() => task.wait(0.5));
    testStateManagement();
    await new Promise(() => task.wait(1));
    print("");

    // Test 6: QuakeAbility Networking
    print("📋 TEST 6/10: QuakeAbility Networking");
    print("----------------------------------------");
    testQuakeAbilityNetworking();
    await new Promise(() => task.wait(1));
    print("");

    // Test 7: UI Components
    print("📋 TEST 7/10: UI Components");
    print("----------------------------------------");
    testResponsiveDesign();
    await new Promise(() => task.wait(0.5));
    testZIndexManager();
    await new Promise(() => task.wait(0.5));
    testUIComponents();
    await new Promise(() => task.wait(1));
    print("");

    // Test 8: Effects System
    print("📋 TEST 8/10: Effects System");
    print("----------------------------------------");
    testBasicEffects();
    await new Promise(() => task.wait(2));
    testParticleEffects();
    await new Promise(() => task.wait(1));
    testVisualEffects();
    await new Promise(() => task.wait(2));
    print("");

    // Test 9: Ability System
    print("📋 TEST 9/10: Ability System");
    print("----------------------------------------");
    testAbilityValidation();
    await new Promise(() => task.wait(0.5));
    testAbilityCooldowns();
    await new Promise(() => task.wait(0.5));
    testAbilityEffects();
    await new Promise(() => task.wait(3));
    print("");

    // Test 10: Performance Monitoring
    print("📋 TEST 10/10: Performance Monitoring");
    print("----------------------------------------");
    testPerformanceMonitoring();
    await new Promise(() => task.wait(1));
    print("");

    // Final Summary
    print("🎯 ========================================");
    print("🎯 COMPREHENSIVE TEST SUMMARY");
    print("🎯 ========================================");
    print("✅ All 10 test categories completed!");
    print("📊 Check the detailed logs above for any issues");
    print("💡 Key indicators to look for:");
    print("   - Server Status: Should show server context available");
    print("   - RemoteEvents: WhitebeardEffectReplicate should be found");
    print("   - Core Framework: Should be initialized");
    print("   - Effects: Should create visual effects without errors");
    print("   - Networking: Should not show 'Core Framework is not initialized'");
    print("");
    print("🚀 Comprehensive Core Framework test completed!");
    print("🚀 ========================================");
  };

  const testQuakeAbilityNetworking = () => {
    print("🥊 Testing QuakeAbility Networking...");

    // Try to get the QuakeAbility instance (this is a bit hacky but for debugging)
    // In a real implementation, you'd have a proper reference to the ability
    try {
      // We'll need to access the ability through the ClientAbilityManager
      // For now, let's just test the RemoteEvent directly
      const eventName = Core.eventName("WhitebeardEffectReplicate");
      const visualSyncEvent = Core.eventName("WhitebeardVisualSync");

      print("📊 QuakeAbility Networking Status:");
      print(`  Event 1: ${eventName}`);
      print(`  Event 2: ${visualSyncEvent}`);

      // Check if RemoteEvents exist
      const remoteEventsFolder = ReplicatedStorage.FindFirstChild("RemoteEvents");
      if (remoteEventsFolder) {
        const event1 = remoteEventsFolder.FindFirstChild(eventName);
        const event2 = remoteEventsFolder.FindFirstChild(visualSyncEvent);

        print(`  ✅ ${eventName}: ${event1 ? "Found" : "Missing"}`);
        print(`  ✅ ${visualSyncEvent}: ${event2 ? "Found" : "Missing"}`);
      } else {
        print("  ❌ RemoteEvents folder not found");
      }

      print("✅ QuakeAbility networking test completed!");
    } catch (error) {
      warn(`❌ QuakeAbility networking test failed: ${error}`);
    }
  };

  const forceReinitializeQuakeNetworking = () => {
    print("🔧 Force reinitializing QuakeAbility networking...");

    try {
      // Access the global QuakeAbility instance through _G (Roblox global table)
      // This is a temporary solution for testing
      const globalTable = (_G as any);
      if (globalTable._quakeAbilityInstance) {
        const success = globalTable._quakeAbilityInstance.forceInitializeNetworking();
        if (success) {
          print("✅ QuakeAbility networking reinitialized successfully!");
        } else {
          warn("❌ Failed to reinitialize QuakeAbility networking");
        }
      } else {
        warn("❌ QuakeAbility instance not found in global scope");
        print("💡 Tip: The QuakeAbility needs to be exposed globally for this to work");
        print("💡 Alternative: Try activating the ability - it will attempt to reinitialize automatically");
      }
    } catch (error) {
      warn(`❌ Force reinitialize failed: ${error}`);
    }
  };

  return (
    <>
      {/* World Testing Panel using Core Components */}
      {isPanelOpen && (
        <ContainerFrame
          size={new UDim2(0, 300, 1, 0)}
          position={new UDim2(1, -300, 0, 0)}
          backgroundTransparency={0}
          borderThickness={0}
          zIndex={forceHighZIndex}
        >
          {/* Header with Title and Close Button */}
          <ContainerFrame
            size={new UDim2(1, 0, 0, 60)}
            position={new UDim2(0, 0, 0, 0)}
            backgroundTransparency={0}
            borderThickness={0}
            zIndex={forceHighZIndex + 1}
          >
            {/* Title on the left */}
            <Label
              text="🌍 World Testing Lab"
              fontSize={16}
              bold={true}
              position={new UDim2(0, 16, 0.5, 0)}
              anchorPoint={new Vector2(0, 0.5)}
              size={new UDim2(1, -60, 0, 20)} // Leave space for close button
            />

            {/* Close button on the right */}
            <IconButton
              icon="✕"
              onClick={closePanel}
              size={new UDim2(0, 32, 0, 32)}
              position={new UDim2(1, -16, 0.5, 0)}
              anchorPoint={new Vector2(1, 0.5)}
            />
          </ContainerFrame>

          {/* Content Area with Scrolling */}
          <ScrollingFrame
            size={new UDim2(1, 0, 1, -60)}
            position={new UDim2(0, 0, 0, 60)}
            backgroundTransparency={1}
            borderThickness={0}
            scrollingDirection={Enum.ScrollingDirection.Y}
            automaticCanvasSize={Enum.AutomaticSize.Y}
            zIndex={forceHighZIndex + 1}
          >
            <VerticalFrame spacing={16} padding={16}>
              {/* Test All Button - Prominent at the top */}
              <VerticalFrame spacing={8} padding={0}>
                <Label
                  text="🚀 Comprehensive Testing"
                  fontSize={18}
                  bold={true}
                />

                <Label
                  text="Run all Core framework tests in sequence with detailed console output for complete system diagnostics."
                  fontSize={12}
                  textWrapped={true}
                  size={new UDim2(1, 0, 0, 0)} // Full width, auto height
                  autoSize={true}
                />

                <Button
                  text="🚀 TEST ALL SYSTEMS"
                  onClick={() => {
                    print("🎯 Starting comprehensive Core framework test...");
                    testAllSystems().then(
                      () => {
                        // Success - nothing to do
                      },
                      (err) => {
                        warn(`Test All failed: ${err}`);
                      }
                    );
                  }}
                />
              </VerticalFrame>

              {/* Section Header */}
              <VerticalFrame spacing={8} padding={0}>
                <Label
                  text="🌍 Gravity Testing"
                  fontSize={16}
                  bold={true}
                />

                <Label
                  text="Test different gravity levels to see how they affect the entire world."
                  fontSize={12}
                  textWrapped={true}
                  size={new UDim2(1, 0, 0, 0)} // Full width, auto height
                  autoSize={true}
                />
              </VerticalFrame>

              {/* Gravity Buttons */}
              <VerticalFrame spacing={8} padding={0}>
                <Button
                  text="🔮 Low Gravity (0.3x)"
                  onClick={testLowGravity}
                />
                <Button
                  text="🌪️ High Gravity (2.5x)"
                  onClick={testHighGravity}
                />
                <Button
                  text="🌀 Zero Gravity (0.05x)"
                  onClick={testZeroGravity}
                />
                <Button
                  text="🌍 Normal Gravity (1.0x)"
                  onClick={testNormalGravity}
                />
              </VerticalFrame>

              {/* Weather Section */}
              <VerticalFrame spacing={8} padding={0}>
                <Label
                  text="🌦️ Weather Testing"
                  fontSize={16}
                  bold={true}
                />

                <Label
                  text="Test different weather conditions and atmospheric effects."
                  fontSize={12}
                  textWrapped={true}
                  size={new UDim2(1, 0, 0, 0)} // Full width, auto height
                  autoSize={true}
                />
              </VerticalFrame>

              {/* Weather Buttons */}
              <VerticalFrame spacing={8} padding={0}>
                <Button
                  text="☀️ Clear Weather"
                  onClick={testClearWeather}
                />
                <Button
                  text="🌧️ Light Rain"
                  onClick={testRain}
                />
                <Button
                  text="🌧️ Heavy Rain"
                  onClick={testHeavyRain}
                />
                <Button
                  text="❄️ Snow"
                  onClick={testSnow}
                />
                <Button
                  text="🌨️ Blizzard"
                  onClick={testBlizzard}
                />
                <Button
                  text="🌪️ Storm"
                  onClick={testStorm}
                />
                <Button
                  text="⛈️ Thunderstorm"
                  onClick={testThunderstorm}
                />
                <Button
                  text="🌫️ Fog"
                  onClick={testFog}
                />
                <Button
                  text="🏜️ Sandstorm"
                  onClick={testSandstorm}
                />
              </VerticalFrame>

              {/* Day/Night Section */}
              <VerticalFrame spacing={8} padding={0}>
                <Label
                  text="🌅 Day/Night Cycle"
                  fontSize={16}
                  bold={true}
                />

                <Label
                  text="Control the time of day and lighting conditions."
                  fontSize={12}
                  textWrapped={true}
                  size={new UDim2(1, 0, 0, 0)} // Full width, auto height
                  autoSize={true}
                />
              </VerticalFrame>

              {/* Day/Night Buttons */}
              <VerticalFrame spacing={8} padding={0}>
                <Button
                  text="🌅 Dawn"
                  onClick={testDawn}
                />
                <Button
                  text="☀️ Noon"
                  onClick={testNoon}
                />
                <Button
                  text="🌇 Dusk"
                  onClick={testDusk}
                />
                <Button
                  text="🌙 Night"
                  onClick={testNight}
                />
              </VerticalFrame>

              {/* Entity Management Section */}
              <VerticalFrame spacing={8} padding={0}>
                <Label
                  text="🤖 Entity Management"
                  fontSize={16}
                  bold={true}
                />

                <Label
                  text="Test the new Entity Management system - spawn, track, and manage game entities."
                  fontSize={12}
                  textWrapped={true}
                  size={new UDim2(1, 0, 0, 0)} // Full width, auto height
                  autoSize={true}
                />
              </VerticalFrame>

              {/* Entity Spawn Buttons */}
              <VerticalFrame spacing={8} padding={0}>
                <Button
                  text="🤖 Spawn NPC"
                  onClick={spawnNPC}
                />
                <Button
                  text="🚀 Spawn Projectile"
                  onClick={spawnProjectile}
                />
                <Button
                  text="✨ Spawn Effect"
                  onClick={spawnEffect}
                />
                <Button
                  text="💎 Spawn Pickup"
                  onClick={spawnPickup}
                />
              </VerticalFrame>

              {/* Entity Management Buttons */}
              <VerticalFrame spacing={8} padding={0}>
                <Button
                  text="📊 List All Entities"
                  onClick={listEntities}
                />
                <Button
                  text="🔍 Find Nearby Entities"
                  onClick={findNearbyEntities}
                />
                <Button
                  text="🧹 Cleanup All Entities"
                  onClick={cleanupAllEntities}
                />
              </VerticalFrame>

              {/* Data Persistence Section */}
              <VerticalFrame spacing={8} padding={0}>
                <Label
                  text="💾 Data Persistence"
                  fontSize={16}
                  bold={true}
                />

                <Label
                  text="Test the DataStore system - save/load player data, currency, inventory, and settings."
                  fontSize={12}
                  textWrapped={true}
                  size={new UDim2(1, 0, 0, 0)} // Full width, auto height
                  autoSize={true}
                />
              </VerticalFrame>

              {/* Player Data Buttons */}
              <VerticalFrame spacing={8} padding={0}>
                <Button
                  text="📊 Load Player Data"
                  onClick={testLoadPlayerData}
                />
                <Button
                  text="🎮 Update Game Data"
                  onClick={testUpdateGameData}
                />
                <Button
                  text="⚙️ Update Settings"
                  onClick={testUpdateSettings}
                />
                <Button
                  text="📈 Show DataStore Stats"
                  onClick={testDataStoreStats}
                />
                <Button
                  text="🔧 Enable Studio DataStore"
                  onClick={enableStudioDataStore}
                />
                <Button
                  text="🛑 Disable Auto-Save"
                  onClick={disableAutoSave}
                />
              </VerticalFrame>

              {/* AI System Section */}
              <VerticalFrame spacing={8} padding={0}>
                <Label
                  text="🧠 AI System"
                  fontSize={16}
                  bold={true}
                />

                <Label
                  text="Test the AI behavior system - spawn NPCs with different AI behaviors and watch them interact."
                  fontSize={12}
                  textWrapped={true}
                  size={new UDim2(1, 0, 0, 0)} // Full width, auto height
                  autoSize={true}
                />
              </VerticalFrame>

              {/* AI Spawn Buttons */}
              <VerticalFrame spacing={8} padding={0}>
                <Button
                  text="🤖 Spawn AI NPC"
                  onClick={spawnAINPC}
                />
                <Button
                  text="🚶 Spawn Patrolling NPC"
                  onClick={spawnPatrollingNPC}
                />
                <Button
                  text="😱 Spawn Fleeing NPC"
                  onClick={spawnFleeingNPC}
                />
                <Button
                  text="🔍 Make NPC Investigate"
                  onClick={makeNPCInvestigate}
                />
              </VerticalFrame>

              {/* AI Management Buttons */}
              <VerticalFrame spacing={8} padding={0}>
                <Button
                  text="📊 Show AI Stats"
                  onClick={showAIStats}
                />
                <Button
                  text="🔍 Debug System Demo"
                  onClick={demonstrateDebugSystem}
                />
                <Button
                  text="🧹 Cleanup All AI"
                  onClick={cleanupAllAI}
                />
              </VerticalFrame>

              {/* Animation System Section */}
              <VerticalFrame spacing={8} padding={0}>
                <Label
                  text="🎬 Animation System"
                  fontSize={16}
                  bold={true}
                />

                <Label
                  text="Test the animation system - create programmatic animations without animation assets."
                  fontSize={12}
                  textWrapped={true}
                  size={new UDim2(1, 0, 0, 0)} // Full width, auto height
                  autoSize={true}
                />
              </VerticalFrame>

              {/* Animation Setup Buttons */}
              <VerticalFrame spacing={8} padding={0}>
                <Button
                  text="🎬 Initialize Animation System"
                  onClick={initializeAnimationSystem}
                />
                <Button
                  text="👋 Test Limb Animation"
                  onClick={testLimbAnimation}
                />
                <Button
                  text="🦾 Test Preset Animations"
                  onClick={testPresetAnimations}
                />
              </VerticalFrame>

              {/* Animation Testing Buttons */}
              <VerticalFrame spacing={8} padding={0}>
                <Button
                  text="📊 Show Animation Stats"
                  onClick={showAnimationStats}
                />
                <Button
                  text="🧹 Cleanup Animation System"
                  onClick={cleanupAnimationSystem}
                />
              </VerticalFrame>

              {/* Effects System Section */}
              <VerticalFrame spacing={8} padding={0}>
                <Label
                  text="✨ Effects System"
                  fontSize={16}
                  bold={true}
                />

                <Label
                  text="Test the comprehensive effects system - EffectPartBuilder, EffectTweenBuilder, particles, visual effects, and sound integration."
                  fontSize={12}
                  textWrapped={true}
                  size={new UDim2(1, 0, 0, 0)} // Full width, auto height
                  autoSize={true}
                />
              </VerticalFrame>

              {/* Basic Effects Buttons */}
              <VerticalFrame spacing={8} padding={0}>
                <Button
                  text="🔮 Test Basic Effects"
                  onClick={testBasicEffects}
                />
                <Button
                  text="🎆 Test Particle Effects"
                  onClick={testParticleEffects}
                />
                <Button
                  text="🌟 Test Visual Effects"
                  onClick={testVisualEffects}
                />
                <Button
                  text="🔊 Test Sound Effects"
                  onClick={testSoundEffects}
                />
                <Button
                  text="🎭 Test Complex Effect Combo"
                  onClick={testComplexEffectCombo}
                />
              </VerticalFrame>

              {/* Ability System Section */}
              <VerticalFrame spacing={8} padding={0}>
                <Label
                  text="⚔️ Ability System"
                  fontSize={16}
                  bold={true}
                />

                <Label
                  text="Test the ability system including validation, cooldowns, and ability-specific effects for One Piece inspired abilities."
                  fontSize={12}
                  textWrapped={true}
                  size={new UDim2(1, 0, 0, 0)} // Full width, auto height
                  autoSize={true}
                />
              </VerticalFrame>

              {/* Ability Testing Buttons */}
              <VerticalFrame spacing={8} padding={0}>
                <Button
                  text="🔍 Test Ability Validation"
                  onClick={testAbilityValidation}
                />
                <Button
                  text="⏰ Test Ability Cooldowns"
                  onClick={testAbilityCooldowns}
                />
                <Button
                  text="💥 Test Ability Effects"
                  onClick={testAbilityEffects}
                />
              </VerticalFrame>

              {/* UI Components Section */}
              <VerticalFrame spacing={8} padding={0}>
                <Label
                  text="🎨 UI Components"
                  fontSize={16}
                  bold={true}
                />

                <Label
                  text="Test the Core GUI framework including responsive design, ZIndex management, modals, and component systems."
                  fontSize={12}
                  textWrapped={true}
                  size={new UDim2(1, 0, 0, 0)} // Full width, auto height
                  autoSize={true}
                />
              </VerticalFrame>

              {/* UI Testing Buttons */}
              <VerticalFrame spacing={8} padding={0}>
                <Button
                  text="📱 Test Responsive Design"
                  onClick={testResponsiveDesign}
                />
                <Button
                  text="🔢 Test ZIndex Manager"
                  onClick={testZIndexManager}
                />
                <Button
                  text="🪟 Test Modal System"
                  onClick={testModalSystem}
                />
                <Button
                  text="🎨 Test UI Components"
                  onClick={testUIComponents}
                />
              </VerticalFrame>

              {/* Networking & Validation Section */}
              <VerticalFrame spacing={8} padding={0}>
                <Label
                  text="🌐 Networking & Validation"
                  fontSize={16}
                  bold={true}
                />

                <Label
                  text="Test the Core framework's networking, validation, and state management systems for robust client-server communication."
                  fontSize={12}
                  textWrapped={true}
                  size={new UDim2(1, 0, 0, 0)} // Full width, auto height
                  autoSize={true}
                />
              </VerticalFrame>

              {/* Networking Testing Buttons */}
              <VerticalFrame spacing={8} padding={0}>
                <Button
                  text="🌐 Test Network Service"
                  onClick={testNetworkService}
                />
                <Button
                  text="🔍 Test Validation Service"
                  onClick={testValidationService}
                />
                <Button
                  text="📊 Test State Management"
                  onClick={testStateManagement}
                />
              </VerticalFrame>

              {/* Advanced Systems Section */}
              <VerticalFrame spacing={8} padding={0}>
                <Label
                  text="🚀 Advanced Systems"
                  fontSize={16}
                  bold={true}
                />

                <Label
                  text="Test advanced Core framework features including framework initialization, error handling, performance monitoring, and system diagnostics."
                  fontSize={12}
                  textWrapped={true}
                  size={new UDim2(1, 0, 0, 0)} // Full width, auto height
                  autoSize={true}
                />
              </VerticalFrame>

              {/* Advanced Testing Buttons */}
              <VerticalFrame spacing={8} padding={0}>
                <Button
                  text="🚀 Test Core Framework"
                  onClick={testCoreFramework}
                />
                <Button
                  text="⚠️ Test Error Handling"
                  onClick={testErrorHandling}
                />
                <Button
                  text="⚡ Test Performance Monitoring"
                  onClick={testPerformanceMonitoring}
                />
                <Button
                  text="📡 Test RemoteEvent Status"
                  onClick={testRemoteEventStatus}
                />
                <Button
                  text="🥊 Test QuakeAbility Networking"
                  onClick={testQuakeAbilityNetworking}
                />
                <Button
                  text="🔧 Force Reinit Quake Networking"
                  onClick={forceReinitializeQuakeNetworking}
                />
                <Button
                  text="🖥️ Test Server Status"
                  onClick={testServerStatus}
                />
              </VerticalFrame>


            </VerticalFrame>
          </ScrollingFrame>
        </ContainerFrame>
      )}

      {/* Test Modal */}
      {testModalOpen && (
        <Modal
          title="🧪 UI Component Test Modal"
          isOpen={testModalOpen}
          onClose={() => setTestModalOpen(false)}
        >
          <VerticalFrame spacing={16} padding={20}>
            <Label
              text="This is a test modal to verify the Modal component functionality in the Core framework."
              fontSize={14}
              textWrapped={true}
            />

            <Button
              text="✅ Modal Works!"
              onClick={() => {
                print("✅ Modal button clicked!");
                setTestModalOpen(false);
              }}
            />

            <Button
              text="❌ Close Modal"
              onClick={() => setTestModalOpen(false)}
            />
          </VerticalFrame>
        </Modal>
      )}
    </>
  );
}
