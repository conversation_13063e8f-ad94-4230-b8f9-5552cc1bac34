import { AbilityBase } from "../AbilityBase";
export declare class QuakeAbility extends AbilityBase {
    quakeEffect?: Part;
    leftQuakeEffect?: Part;
    quakeConnection?: RBXScriptConnection;
    private cooldownEndTime;
    private effectReplicationEvent?;
    private networkingInitialized;
    constructor();
    private tryInitializeNetworking;
    private setupNetworkingRetry;
    private initializeNetworking;
    getNetworkingStatus(): {
        initialized: boolean;
        eventAvailable: boolean;
        eventName: string;
    };
    forceInitializeNetworking(): boolean;
    isOnCooldown(): boolean;
    private startCooldown;
    private createVisualEffectsFromServer;
    activate(punchType?: "single" | "double"): void;
}
