-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local exports = {}
-- Core World System - Environmental interactions and world state management
-- Universal utilities for destructible environments, physics zones, world events, and state management
-- Environment Management
exports.DestructibleManager = TS.import(script, game:GetService("ReplicatedStorage"), "core", "world", "environment", "DestructibleManager").DestructibleManager
-- Physics Management
local PhysicsImpactHelper = TS.import(script, game:GetService("ReplicatedStorage"), "core", "world", "physics", "PhysicsImpactHelper").PhysicsImpactHelper
-- World Events
local WorldEventBroadcaster = TS.import(script, game:GetService("ReplicatedStorage"), "core", "world", "events", "WorldEventBroadcaster").WorldEventBroadcaster
-- World State Management
local WorldStateManager = TS.import(script, game:GetService("ReplicatedStorage"), "core", "world", "state", "WorldStateManager").WorldStateManager
exports.WeatherController = TS.import(script, game:GetService("ReplicatedStorage"), "core", "world", "state", "WeatherController").WeatherController
exports.TimeController = TS.import(script, game:GetService("ReplicatedStorage"), "core", "world", "state", "TimeController").TimeController
exports.GravityController = TS.import(script, game:GetService("ReplicatedStorage"), "core", "world", "state", "GravityController").GravityController
--[[
	*
	 * Initialize all Core World systems
	 * Call this once in your main server script
	 
]]
local function initializeCoreWorld()
	PhysicsImpactHelper:initialize()
	WorldEventBroadcaster:initialize()
	WorldStateManager:initialize()
	print("🌍 Core World systems initialized successfully!")
end
--[[
	*
	 * Shutdown all Core World systems
	 * Call this when shutting down the server
	 
]]
local function shutdownCoreWorld()
	PhysicsImpactHelper:shutdown()
	WorldEventBroadcaster:shutdown()
	WorldStateManager:getInstance():cleanup()
	print("🌍 Core World systems shutdown complete!")
end
exports.initializeCoreWorld = initializeCoreWorld
exports.shutdownCoreWorld = shutdownCoreWorld
exports.PhysicsImpactHelper = PhysicsImpactHelper
exports.WorldEventBroadcaster = WorldEventBroadcaster
exports.WorldStateManager = WorldStateManager
return exports
