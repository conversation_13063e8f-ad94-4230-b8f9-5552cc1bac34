export declare class AnimationBuilder {
    private joint;
    private tweenInfo;
    private targetCFrame;
    private delayTime;
    private completionCallback?;
    constructor(joint: Motor6D);
    static forJoint(joint: Motor6D): AnimationBuilder;
    to(relativeCFrame: CFrame): this;
    toAbsolute(absoluteCFrame: CFrame): this;
    duration(time: number): this;
    easing(style: Enum.EasingStyle, direction: Enum.EasingDirection): this;
    delay(delay: number): this;
    onComplete(callback: () => void): this;
    play(): Tween;
}
