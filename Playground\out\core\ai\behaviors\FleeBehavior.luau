-- Compiled with roblox-ts v3.0.0
local FleeBehavior
do
	FleeBehavior = setmetatable({}, {
		__tostring = function()
			return "FleeBehavior"
		end,
	})
	FleeBehavior.__index = FleeBehavior
	function FleeBehavior.new(...)
		local self = setmetatable({}, FleeBehavior)
		return self:constructor(...) or self
	end
	function FleeBehavior:constructor()
		self.name = "Flee"
		self.priority = 8
	end
	function FleeBehavior:canExecute(context)
		local threat = context.blackboard.threat
		return threat ~= nil
	end
	function FleeBehavior:execute(context)
		local threat = context.blackboard.threat
		if not threat or not threat.Parent then
			context.blackboard.threat = nil
			return {
				success = true,
				completed = true,
			}
		end
		local threatPosition = self:getThreatPosition(threat)
		if not threatPosition then
			context.blackboard.threat = nil
			return {
				success = true,
				completed = true,
			}
		end
		local fleeDirection = (context.position - threatPosition).Unit
		local _position = context.position
		local _arg0 = fleeDirection * 30
		local fleeTarget = _position + _arg0
		self:moveTowards(context, fleeTarget)
		return {
			success = true,
			completed = false,
		}
	end
	function FleeBehavior:onEnter(context)
		print(`😨 {context.entityId} is fleeing from threat`)
	end
	function FleeBehavior:getThreatPosition(threat)
		if threat:IsA("Model") and threat.PrimaryPart then
			return threat.PrimaryPart.Position
		elseif threat:IsA("BasePart") then
			return threat.Position
		end
		return nil
	end
	function FleeBehavior:moveTowards(context, targetPosition)
		if context.entity:IsA("Model") and context.entity.PrimaryPart then
			local humanoid = context.entity:FindFirstChild("Humanoid")
			if humanoid then
				humanoid:MoveTo(targetPosition)
			end
		end
	end
end
return {
	FleeBehavior = FleeBehavior,
}
