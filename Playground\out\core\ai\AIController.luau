-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local _services = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services")
local RunService = _services.RunService
local Players = _services.Players
local EntityManager = TS.import(script, game:GetService("ReplicatedStorage"), "core", "entities", "EntityManager").EntityManager
local PositionHelper = TS.import(script, game:GetService("ReplicatedStorage"), "core", "helper", "PositionHelper").PositionHelper
local AIState = TS.import(script, game:GetService("ReplicatedStorage"), "core", "ai", "enums", "AIState").AIState
local _behaviors = TS.import(script, game:GetService("ReplicatedStorage"), "core", "ai", "behaviors")
local IdleBehavior = _behaviors.IdleBehavior
local FollowBehavior = _behaviors.FollowBehavior
local PatrolBehavior = _behaviors.PatrolBehavior
local InvestigateBehavior = _behaviors.InvestigateBehavior
local FleeBehavior = _behaviors.FleeBehavior
local AttackBehavior = _behaviors.AttackBehavior
local WanderBehavior = _behaviors.WanderBehavior
local AIAgent
local AIController
do
	AIController = setmetatable({}, {
		__tostring = function()
			return "AIController"
		end,
	})
	AIController.__index = AIController
	function AIController.new(...)
		local self = setmetatable({}, AIController)
		return self:constructor(...) or self
	end
	function AIController:constructor()
		self.aiEntities = {}
		self.lastUpdateTime = tick()
		self.entityManager = EntityManager:getInstance()
		self:startAILoop()
	end
	function AIController:getInstance()
		if not AIController.instance then
			AIController.instance = AIController.new()
		end
		return AIController.instance
	end
	function AIController:registerAI(entityId, config)
		local entity = self.entityManager:getEntity(entityId)
		if not entity then
			error(`Entity with ID {entityId} not found`)
		end
		local aiAgent = AIAgent.new(entityId, entity.instance, config)
		local _aiEntities = self.aiEntities
		local _entityId = entityId
		_aiEntities[_entityId] = aiAgent
		print(`🤖 Registered AI for entity: {entityId}`)
		return aiAgent
	end
	function AIController:unregisterAI(entityId)
		local _aiEntities = self.aiEntities
		local _entityId = entityId
		local aiAgent = _aiEntities[_entityId]
		if aiAgent then
			aiAgent:cleanup()
			local _aiEntities_1 = self.aiEntities
			local _entityId_1 = entityId
			_aiEntities_1[_entityId_1] = nil
			print(`🗑️ Unregistered AI for entity: {entityId}`)
		end
	end
	function AIController:getAI(entityId)
		local _aiEntities = self.aiEntities
		local _entityId = entityId
		return _aiEntities[_entityId]
	end
	function AIController:getAllAIs()
		local result = {}
		local _exp = self.aiEntities
		-- ▼ ReadonlyMap.forEach ▼
		local _callback = function(ai)
			local _ai = ai
			table.insert(result, _ai)
			return #result
		end
		for _k, _v in _exp do
			_callback(_v, _k, _exp)
		end
		-- ▲ ReadonlyMap.forEach ▲
		return result
	end
	function AIController:getAICount()
		-- ▼ ReadonlyMap.size ▼
		local _size = 0
		for _ in self.aiEntities do
			_size += 1
		end
		-- ▲ ReadonlyMap.size ▲
		return _size
	end
	function AIController:startAILoop()
		self.heartbeatConnection = RunService.Heartbeat:Connect(function()
			local currentTime = tick()
			local deltaTime = currentTime - self.lastUpdateTime
			self.lastUpdateTime = currentTime
			-- Update all AI agents
			local _exp = self.aiEntities
			-- ▼ ReadonlyMap.forEach ▼
			local _callback = function(aiAgent, entityId)
				local entity = self.entityManager:getEntity(entityId)
				if not entity or not entity.isActive then
					-- Entity no longer exists, remove AI
					self:unregisterAI(entityId)
					return nil
				end
				aiAgent:update(deltaTime)
			end
			for _k, _v in _exp do
				_callback(_v, _k, _exp)
			end
			-- ▲ ReadonlyMap.forEach ▲
		end)
	end
	function AIController:cleanup()
		if self.heartbeatConnection then
			self.heartbeatConnection:Disconnect()
		end
		local _exp = self.aiEntities
		-- ▼ ReadonlyMap.forEach ▼
		local _callback = function(aiAgent)
			aiAgent:cleanup()
		end
		for _k, _v in _exp do
			_callback(_v, _k, _exp)
		end
		-- ▲ ReadonlyMap.forEach ▲
		table.clear(self.aiEntities)
	end
end
do
	AIAgent = setmetatable({}, {
		__tostring = function()
			return "AIAgent"
		end,
	})
	AIAgent.__index = AIAgent
	function AIAgent.new(...)
		local self = setmetatable({}, AIAgent)
		return self:constructor(...) or self
	end
	function AIAgent:constructor(entityId, entity, config)
		self.behaviors = {}
		self.state = AIState.Idle
		self.blackboard = {}
		self.lastStateChange = tick()
		self.entityId = entityId
		self.entity = entity
		local _object = {
			detectionRange = 50,
			followRange = 30,
			attackRange = 5,
			moveSpeed = 16,
			patrolRadius = 20,
			reactionTime = 0.5,
			aggroTime = 10,
			memoryDuration = 5,
		}
		if config then
			for _k, _v in config do
				_object[_k] = _v
			end
		end
		self.config = _object
		-- Register default behaviors
		self:registerDefaultBehaviors()
	end
	function AIAgent:addBehavior(behavior)
		local _behaviors_1 = self.behaviors
		local _name = behavior.name
		local _behavior = behavior
		_behaviors_1[_name] = _behavior
	end
	function AIAgent:removeBehavior(behaviorName)
		local _result = self.currentBehavior
		if _result ~= nil then
			_result = _result.name
		end
		if _result == behaviorName then
			self.currentBehavior = nil
		end
		local _behaviors_1 = self.behaviors
		local _behaviorName = behaviorName
		_behaviors_1[_behaviorName] = nil
	end
	function AIAgent:setState(newState)
		if self.state ~= newState then
			print(`🔄 AI {self.entityId} state: {self.state} → {newState}`)
			self.state = newState
			self.lastStateChange = tick()
		end
	end
	function AIAgent:getState()
		return self.state
	end
	function AIAgent:setBlackboardValue(key, value)
		local _blackboard = self.blackboard
		local _key = key
		local _value = value
		_blackboard[_key] = _value
	end
	function AIAgent:getBlackboardValue(key)
		local _blackboard = self.blackboard
		local _key = key
		return _blackboard[_key]
	end
	function AIAgent:update(deltaTime)
		-- Create AI context
		local context = {
			entityId = self.entityId,
			entity = self.entity,
			position = PositionHelper:getPosition(self.entity),
			target = self:findNearestPlayer(),
			blackboard = self:blackboardToRecord(),
			deltaTime = deltaTime,
		}
		-- Update target position if we have a target
		if context.target then
			context.targetPosition = PositionHelper:getPosition(context.target)
		end
		-- Select best behavior
		local bestBehavior = self:selectBehavior(context)
		-- Switch behaviors if needed
		if bestBehavior ~= self.currentBehavior then
			if self.currentBehavior and self.currentBehavior.onExit then
				self.currentBehavior:onExit(context)
			end
			self.currentBehavior = bestBehavior
			if self.currentBehavior and self.currentBehavior.onEnter then
				self.currentBehavior:onEnter(context)
			end
		end
		-- Execute current behavior
		if self.currentBehavior then
			local result = self.currentBehavior:execute(context)
			-- Handle behavior result
			local _value = result.nextBehavior
			if _value ~= "" and _value then
				local _behaviors_1 = self.behaviors
				local _nextBehavior = result.nextBehavior
				local nextBehavior = _behaviors_1[_nextBehavior]
				if nextBehavior then
					self.currentBehavior = nextBehavior
				end
			end
		end
	end
	function AIAgent:selectBehavior(context)
		local bestBehavior
		local highestPriority = -1
		local _exp = self.behaviors
		-- ▼ ReadonlyMap.forEach ▼
		local _callback = function(behavior)
			if behavior:canExecute(context) and behavior.priority > highestPriority then
				bestBehavior = behavior
				highestPriority = behavior.priority
			end
		end
		for _k, _v in _exp do
			_callback(_v, _k, _exp)
		end
		-- ▲ ReadonlyMap.forEach ▲
		return bestBehavior
	end
	function AIAgent:findNearestPlayer()
		local myPosition = PositionHelper:getPosition(self.entity)
		local nearestPlayer
		local nearestDistance = self.config.detectionRange
		for _, player in Players:GetPlayers() do
			if player.Character and player.Character.PrimaryPart then
				local playerPosition = player.Character.PrimaryPart.Position
				local distance = (myPosition - playerPosition).Magnitude
				-- Check if player is within range AND we have line of sight
				if distance < nearestDistance and PositionHelper:hasLineOfSight(myPosition, playerPosition, { self.entity }) then
					nearestPlayer = player
					nearestDistance = distance
				end
			end
		end
		local _result = nearestPlayer
		if _result ~= nil then
			_result = _result.Character
		end
		return _result
	end
	function AIAgent:blackboardToRecord()
		local record = {}
		local _exp = self.blackboard
		-- ▼ ReadonlyMap.forEach ▼
		local _callback = function(value, key)
			record[key] = value
		end
		for _k, _v in _exp do
			_callback(_v, _k, _exp)
		end
		-- ▲ ReadonlyMap.forEach ▲
		return record
	end
	function AIAgent:registerDefaultBehaviors()
		-- Register default behaviors (ordered by priority)
		self:addBehavior(FleeBehavior.new())
		self:addBehavior(AttackBehavior.new())
		self:addBehavior(FollowBehavior.new())
		self:addBehavior(InvestigateBehavior.new())
		self:addBehavior(PatrolBehavior.new())
		self:addBehavior(WanderBehavior.new())
		self:addBehavior(IdleBehavior.new())
	end
	function AIAgent:cleanup()
		table.clear(self.behaviors)
		table.clear(self.blackboard)
		self.currentBehavior = nil
	end
end
return {
	AIController = AIController,
	AIAgent = AIAgent,
}
