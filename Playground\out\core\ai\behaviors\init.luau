-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local exports = {}
exports.IdleBehavior = TS.import(script, game:GetService("ReplicatedStorage"), "core", "ai", "behaviors", "IdleBehavior").IdleBehavior
exports.FollowBehavior = TS.import(script, game:GetService("ReplicatedStorage"), "core", "ai", "behaviors", "FollowBehavior").FollowBehavior
exports.PatrolBehavior = TS.import(script, game:GetService("ReplicatedStorage"), "core", "ai", "behaviors", "PatrolBehavior").PatrolBehavior
exports.InvestigateBehavior = TS.import(script, game:GetService("ReplicatedStorage"), "core", "ai", "behaviors", "InvestigateBehavior").InvestigateBehavior
exports.FleeBehavior = TS.import(script, game:GetService("ReplicatedStorage"), "core", "ai", "behaviors", "FleeBehavior").FleeBehavior
exports.AttackBehavior = TS.import(script, game:GetService("ReplicatedStorage"), "core", "ai", "behaviors", "AttackBehavior").AttackBehavior
exports.WanderBehavior = TS.import(script, game:GetService("ReplicatedStorage"), "core", "ai", "behaviors", "WanderBehavior").WanderBehavior
return exports
