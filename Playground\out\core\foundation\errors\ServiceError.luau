-- Compiled with roblox-ts v3.0.0
local ServiceError
do
	ServiceError = setmetatable({}, {
		__tostring = function()
			return "ServiceError"
		end,
	})
	ServiceError.__index = ServiceError
	function ServiceError.new(...)
		local self = setmetatable({}, ServiceError)
		return self:constructor(...) or self
	end
	function ServiceError:constructor(message, cause)
		self.name = "ServiceError"
		self.message = message
		self.cause = cause
	end
end
return {
	ServiceError = ServiceError,
}
