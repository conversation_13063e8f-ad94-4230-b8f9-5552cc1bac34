import { IService } from "./interfaces/IService";
import { Result } from "./types/Result";
import { Error } from "./types/RobloxError";
export declare abstract class BaseService implements IService {
    readonly name: string;
    protected isInitialized: boolean;
    protected isShutdown: boolean;
    constructor(name: string);
    initialize(): Promise<Result<void, Error>>;
    shutdown(): Promise<Result<void, Error>>;
    protected abstract onInitialize(): Promise<Result<void, Error>>;
    protected abstract onShutdown(): Promise<Result<void, Error>>;
    protected ensureInitialized(): Result<void, Error>;
    protected ensureNotShutdown(): Result<void, Error>;
    protected logInfo(message: string): void;
    protected logWarning(message: string): void;
    protected logError(message: string): void;
    protected logDebug(message: string): void;
}
