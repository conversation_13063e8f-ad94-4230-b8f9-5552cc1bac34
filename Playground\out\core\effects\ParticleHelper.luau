-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local EffectPartBuilder = TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "EffectPartBuilder").EffectPartBuilder
local EffectTweenBuilder = TS.import(script, game:GetService("ReplicatedStorage"), "core", "effects", "EffectTweenBuilder").EffectTweenBuilder
local function createParticleExplosion(pos, numParticles, color, velocityRange, sizeRange)
	do
		local i = 0
		local _shouldIncrement = false
		while true do
			if _shouldIncrement then
				i += 1
			else
				_shouldIncrement = true
			end
			if not (i < numParticles) then
				break
			end
			local particle = EffectPartBuilder:create():shape(Enum.PartType.Ball):size(Vector3.new(math.random(sizeRange[1], sizeRange[2]), math.random(sizeRange[1], sizeRange[2]), math.random(sizeRange[1], sizeRange[2]))):color(color):material(Enum.Material.Neon):transparency(0.1):position(pos):spawn()
			local bodyVelocity = Instance.new("BodyVelocity")
			bodyVelocity.MaxForce = Vector3.new(math.huge, math.huge, math.huge)
			local _unit = Vector3.new(math.random(-1, 1), math.random(-1, 1), math.random(-1, 1)).Unit
			local _arg0 = math.random(velocityRange[1], velocityRange[2])
			bodyVelocity.Velocity = _unit * _arg0
			bodyVelocity.Parent = particle
			local bodyAngularVelocity = Instance.new("BodyAngularVelocity")
			bodyAngularVelocity.MaxTorque = Vector3.new(1000, 1000, 1000)
			bodyAngularVelocity.AngularVelocity = Vector3.new(math.random(-15, 15), math.random(-15, 15), math.random(-15, 15))
			bodyAngularVelocity.Parent = particle
			task.delay(0.5, function()
				EffectTweenBuilder["for"](EffectTweenBuilder, particle):fade(1):expand(Vector3.new(0.1, 0.1, 0.1)):duration(2):onComplete(function()
					return particle:Destroy()
				end):play()
			end)
			task.delay(1, function()
				bodyVelocity:Destroy()
				bodyAngularVelocity:Destroy()
			end)
		end
	end
end
local function createParticleStorm(pos, numParticles, color, floatRange)
	do
		local i = 0
		local _shouldIncrement = false
		while true do
			if _shouldIncrement then
				i += 1
			else
				_shouldIncrement = true
			end
			if not (i < numParticles) then
				break
			end
			local _ = EffectPartBuilder:create():shape(Enum.PartType.Ball):size(Vector3.new(0.5, 0.5, 0.5)):color(color):material(Enum.Material.Neon):transparency(0.3)
			local _pos = pos
			local _vector3 = Vector3.new(math.random(-15, 15), math.random(-5, 10), math.random(-15, 15))
			local particle = _:position(_pos + _vector3):spawn()
			local _1 = EffectTweenBuilder["for"](EffectTweenBuilder, particle)
			local _position = particle.Position
			local _vector3_1 = Vector3.new(0, math.random(floatRange[1], floatRange[2]), 0)
			_1:move(_position + _vector3_1):duration(3):easing(Enum.EasingStyle.Sine, Enum.EasingDirection.InOut):play()
			task.delay(4, function()
				EffectTweenBuilder["for"](EffectTweenBuilder, particle):fade(1):duration(1):onComplete(function()
					return particle:Destroy()
				end):play()
			end)
		end
	end
end
return {
	createParticleExplosion = createParticleExplosion,
	createParticleStorm = createParticleStorm,
}
