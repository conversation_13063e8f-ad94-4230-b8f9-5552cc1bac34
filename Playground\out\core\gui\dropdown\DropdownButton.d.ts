import * as React from "@rbxts/react";
export interface DropdownOption {
    id: string;
    text: string;
    icon?: string;
}
interface DropdownButtonProps {
    options: DropdownOption[];
    selectedOption?: DropdownOption;
    onOptionSelect: (option: DropdownOption) => void;
    placeholder?: string;
    size?: UDim2;
    layoutOrder?: number;
    disabled?: boolean;
}
export declare function DropdownButton(props: DropdownButtonProps): React.ReactElement;
export {};
