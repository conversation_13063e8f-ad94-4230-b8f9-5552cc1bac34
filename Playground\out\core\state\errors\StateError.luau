-- Compiled with roblox-ts v3.0.0
local StateError
do
	StateError = setmetatable({}, {
		__tostring = function()
			return "StateError"
		end,
	})
	StateError.__index = StateError
	function StateError.new(...)
		local self = setmetatable({}, StateError)
		return self:constructor(...) or self
	end
	function StateError:constructor(message, cause)
		self.name = "StateError"
		self.message = message
		self.cause = cause
	end
end
return {
	StateError = StateError,
}
