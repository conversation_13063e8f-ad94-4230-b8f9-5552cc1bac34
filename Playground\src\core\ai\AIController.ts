import { RunService, Players } from "@rbxts/services";
import { EntityManager } from "../entities/EntityManager";
import { EntityType } from "../entities/enums/EntityType";
import { PositionHelper } from "../helper/PositionHelper";
import { AIBehavior } from "./interfaces/AIBehavior";
import { AIContext } from "./interfaces/AIContext";
import { AIBehaviorResult } from "./interfaces/AIBehaviorResult";
import { AIConfig } from "./interfaces/AIConfig";
import { AIState } from "./enums/AIState";
import {
	IdleBeh<PERSON>or,
	FollowBehavior,
	PatrolBehavior,
	InvestigateBehavior,
	FleeBehavior,
	AttackBehavior,
	WanderBehavior
} from "./behaviors";

export class AIController {
	private static instance: AIController;
	private entityManager: EntityManager;
	private aiEntities = new Map<string, AIAgent>();
	private heartbeatConnection?: RBXScriptConnection;
	private lastUpdateTime = tick();

	private constructor() {
		this.entityManager = EntityManager.getInstance();
		this.startAILoop();
	}

	public static getInstance(): AIController {
		if (!AIController.instance) {
			AIController.instance = new AIController();
		}
		return AIController.instance;
	}

	public registerAI(entityId: string, config?: Partial<AIConfig>): AIAgent {
		const entity = this.entityManager.getEntity(entityId);
		if (!entity) {
			throw `Entity with ID ${entityId} not found`;
		}

		const aiAgent = new AIAgent(entityId, entity.instance, config);
		this.aiEntities.set(entityId, aiAgent);
		
		print(`🤖 Registered AI for entity: ${entityId}`);
		return aiAgent;
	}

	public unregisterAI(entityId: string): void {
		const aiAgent = this.aiEntities.get(entityId);
		if (aiAgent) {
			aiAgent.cleanup();
			this.aiEntities.delete(entityId);
			print(`🗑️ Unregistered AI for entity: ${entityId}`);
		}
	}

	public getAI(entityId: string): AIAgent | undefined {
		return this.aiEntities.get(entityId);
	}

	public getAllAIs(): AIAgent[] {
		const result: AIAgent[] = [];
		this.aiEntities.forEach((ai) => result.push(ai));
		return result;
	}

	public getAICount(): number {
		return this.aiEntities.size();
	}

	private startAILoop(): void {
		this.heartbeatConnection = RunService.Heartbeat.Connect(() => {
			const currentTime = tick();
			const deltaTime = currentTime - this.lastUpdateTime;
			this.lastUpdateTime = currentTime;

			// Update all AI agents
			this.aiEntities.forEach((aiAgent, entityId) => {
				const entity = this.entityManager.getEntity(entityId);
				if (!entity || !entity.isActive) {
					// Entity no longer exists, remove AI
					this.unregisterAI(entityId);
					return;
				}

				aiAgent.update(deltaTime);
			});
		});
	}

	public cleanup(): void {
		if (this.heartbeatConnection) {
			this.heartbeatConnection.Disconnect();
		}

		this.aiEntities.forEach((aiAgent) => {
			aiAgent.cleanup();
		});
		this.aiEntities.clear();
	}
}

export class AIAgent {
	private entityId: string;
	private entity: Instance;
	private config: AIConfig;
	private behaviors = new Map<string, AIBehavior>();
	private currentBehavior?: AIBehavior;
	private state: AIState = AIState.Idle;
	private blackboard = new Map<string, unknown>();
	private lastStateChange = tick();

	constructor(entityId: string, entity: Instance, config?: Partial<AIConfig>) {
		this.entityId = entityId;
		this.entity = entity;
		this.config = {
			detectionRange: 50,
			followRange: 30,
			attackRange: 5,
			moveSpeed: 16,
			patrolRadius: 20,
			reactionTime: 0.5,
			aggroTime: 10,
			memoryDuration: 5,
			...config
		};

		// Register default behaviors
		this.registerDefaultBehaviors();
	}

	public addBehavior(behavior: AIBehavior): void {
		this.behaviors.set(behavior.name, behavior);
	}

	public removeBehavior(behaviorName: string): void {
		if (this.currentBehavior?.name === behaviorName) {
			this.currentBehavior = undefined;
		}
		this.behaviors.delete(behaviorName);
	}

	public setState(newState: AIState): void {
		if (this.state !== newState) {
			print(`🔄 AI ${this.entityId} state: ${this.state} → ${newState}`);
			this.state = newState;
			this.lastStateChange = tick();
		}
	}

	public getState(): AIState {
		return this.state;
	}

	public setBlackboardValue(key: string, value: unknown): void {
		this.blackboard.set(key, value);
	}

	public getBlackboardValue(key: string): unknown {
		return this.blackboard.get(key);
	}

	public update(deltaTime: number): void {
		// Create AI context
		const context: AIContext = {
			entityId: this.entityId,
			entity: this.entity,
			position: PositionHelper.getPosition(this.entity),
			target: this.findNearestPlayer(),
			blackboard: this.blackboardToRecord(),
			deltaTime
		};

		// Update target position if we have a target
		if (context.target) {
			context.targetPosition = PositionHelper.getPosition(context.target);
		}

		// Select best behavior
		const bestBehavior = this.selectBehavior(context);
		
		// Switch behaviors if needed
		if (bestBehavior !== this.currentBehavior) {
			if (this.currentBehavior && this.currentBehavior.onExit) {
				this.currentBehavior.onExit(context);
			}

			this.currentBehavior = bestBehavior;

			if (this.currentBehavior && this.currentBehavior.onEnter) {
				this.currentBehavior.onEnter(context);
			}
		}

		// Execute current behavior
		if (this.currentBehavior) {
			const result = this.currentBehavior.execute(context);
			
			// Handle behavior result
			if (result.nextBehavior) {
				const nextBehavior = this.behaviors.get(result.nextBehavior);
				if (nextBehavior) {
					this.currentBehavior = nextBehavior;
				}
			}
		}
	}

	private selectBehavior(context: AIContext): AIBehavior | undefined {
		let bestBehavior: AIBehavior | undefined;
		let highestPriority = -1;

		this.behaviors.forEach((behavior) => {
			if (behavior.canExecute(context) && behavior.priority > highestPriority) {
				bestBehavior = behavior;
				highestPriority = behavior.priority;
			}
		});

		return bestBehavior;
	}

	private findNearestPlayer(): Instance | undefined {
		const myPosition = PositionHelper.getPosition(this.entity);
		let nearestPlayer: Player | undefined;
		let nearestDistance = this.config.detectionRange;

		for (const player of Players.GetPlayers()) {
			if (player.Character && player.Character.PrimaryPart) {
				const playerPosition = player.Character.PrimaryPart.Position;
				const distance = myPosition.sub(playerPosition).Magnitude;

				// Check if player is within range AND we have line of sight
				if (distance < nearestDistance && PositionHelper.hasLineOfSight(myPosition, playerPosition, [this.entity])) {
					nearestPlayer = player;
					nearestDistance = distance;
				}
			}
		}

		return nearestPlayer?.Character;
	}

	private blackboardToRecord(): Record<string, unknown> {
		const record: Record<string, unknown> = {};
		this.blackboard.forEach((value, key) => {
			record[key] = value;
		});
		return record;
	}

	private registerDefaultBehaviors(): void {
		// Register default behaviors (ordered by priority)
		this.addBehavior(new FleeBehavior());        // Priority 8 - Highest
		this.addBehavior(new AttackBehavior());      // Priority 7
		this.addBehavior(new FollowBehavior());      // Priority 5
		this.addBehavior(new InvestigateBehavior()); // Priority 4
		this.addBehavior(new PatrolBehavior());      // Priority 3
		this.addBehavior(new WanderBehavior());      // Priority 2
		this.addBehavior(new IdleBehavior());        // Priority 1 - Lowest
	}

	public cleanup(): void {
		this.behaviors.clear();
		this.blackboard.clear();
		this.currentBehavior = undefined;
	}
}
