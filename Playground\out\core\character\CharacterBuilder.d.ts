export declare class CharacterBuilder {
    private model;
    private humanoid?;
    private parts;
    constructor();
    static create(): CharacterBuilder;
    name(name: string): this;
    position(position: Vector3): this;
    parent(parent: Instance): this;
    addHumanoid(maxHealth?: number): this;
    addPart(partName: string, size: Vector3, color?: Color3, shape?: Enum.PartType): this;
    addHead(color?: Color3): this;
    addTorso(color?: Color3): this;
    addArm(side: "Left" | "Right", color?: Color3): this;
    addLeg(side: "Left" | "Right", color?: Color3): this;
    positionPart(partName: string, relativePosition: Vector3): this;
    addJoint(jointName: string, part0Name: string, part1Name: string, c0: CFrame, c1: CFrame): this;
    addNeckJoint(): this;
    addShoulderJoint(side: "Left" | "Right"): this;
    addHipJoint(side: "Left" | "Right"): this;
    autoPositionParts(): this;
    autoAddJoints(): this;
    spawn(): Model;
    static createBasicNPC(name?: string, position?: Vector3): Model;
    static createBasicPlayer(name?: string, position?: Vector3): Model;
    static createFullCharacter(name?: string, position?: Vector3): Model;
}
