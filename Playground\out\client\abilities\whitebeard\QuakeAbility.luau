-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local AbilityBase = TS.import(script, script.Parent.Parent, "AbilityBase").AbilityBase
local Players = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services").Players
local _core = TS.import(script, game:GetService("ReplicatedStorage"), "core")
local onServerEvent = _core.onServerEvent
local fireServer = _core.fireServer
local Core = _core.Core
local ClientCore_Instance = _core.ClientCore_Instance
local createCameraShake = TS.import(script, script.Parent, "effects", "CameraShake").createCameraShake
local createMapShockwave = TS.import(script, script.Parent, "effects", "ShockwaveEffects").createMapShockwave
local _SphereVisuals = TS.import(script, script.Parent, "effects", "SphereVisuals")
local createQuakeSphere = _SphereVisuals.createQuakeSphere
local createSequentialQuakeSpheres = _SphereVisuals.createSequentialQuakeSpheres
local executeQuakePunch = TS.import(script, script.Parent, "animations", "PunchExecution").executeQuakePunch
local QuakeAbility
do
	local super = AbilityBase
	QuakeAbility = setmetatable({}, {
		__tostring = function()
			return "QuakeAbility"
		end,
		__index = super,
	})
	QuakeAbility.__index = QuakeAbility
	function QuakeAbility.new(...)
		local self = setmetatable({}, QuakeAbility)
		return self:constructor(...) or self
	end
	function QuakeAbility:constructor()
		super.constructor(self, "QUAKE_PUNCH", 12)
		self.cooldownEndTime = 0
		self.networkingInitialized = false
		print("🥊 QuakeAbility: Constructor called")
		-- Try to initialize networking immediately, but don't fail if it's not ready
		self:tryInitializeNetworking()
		-- Set up a retry mechanism
		self:setupNetworkingRetry()
	end
	function QuakeAbility:tryInitializeNetworking()
		if self.networkingInitialized then
			return nil
		end
		local eventName = Core.eventName("WhitebeardEffectReplicate")
		-- Check if the event is available
		if ClientCore_Instance:isEventAvailable(eventName) then
			print("✅ QuakeAbility: RemoteEvent available, initializing networking...")
			self:initializeNetworking()
		else
			print(`⏳ QuakeAbility: RemoteEvent '{eventName}' not available yet`)
		end
	end
	function QuakeAbility:setupNetworkingRetry()
		-- Retry every 2 seconds for up to 30 seconds
		task.spawn(function()
			local attempts = 0
			local maxAttempts = 15
			while attempts < maxAttempts and not self.networkingInitialized do
				task.wait(2)
				attempts += 1
				if self.networkingInitialized then
					return nil
				end
				print(`🔄 QuakeAbility: Retry attempt {attempts}/{maxAttempts}`)
				self:tryInitializeNetworking()
			end
			if not self.networkingInitialized then
				warn("⚠️ QuakeAbility: Failed to initialize networking after 30 seconds")
			end
		end)
	end
	function QuakeAbility:initializeNetworking()
		if self.networkingInitialized then
			return nil
		end
		-- Listen for server-side effect replication using Core framework
		local result = onServerEvent(Core.eventName("WhitebeardEffectReplicate"), function(data)
			print(`🌊 Client received Whitebeard effect: {data.type} from user {data.casterUserId}`)
			if data.type == "whitebeard_quake" then
				self:createVisualEffectsFromServer(data)
			end
		end)
		if result:isError() then
			warn(`Failed to setup Whitebeard networking: {result:getError().message}`)
		else
			self.networkingInitialized = true
			print("✅ QuakeAbility networking initialized successfully")
		end
	end
	function QuakeAbility:getNetworkingStatus()
		local eventName = Core.eventName("WhitebeardEffectReplicate")
		return {
			initialized = self.networkingInitialized,
			eventAvailable = ClientCore_Instance:isEventAvailable(eventName),
			eventName = eventName,
		}
	end
	function QuakeAbility:forceInitializeNetworking()
		print("🔧 QuakeAbility: Force initializing networking...")
		self:initializeNetworking()
		return self.networkingInitialized
	end
	function QuakeAbility:isOnCooldown()
		return tick() < self.cooldownEndTime
	end
	function QuakeAbility:startCooldown()
		self.cooldownEndTime = tick() + self:getCooldownTime()
	end
	function QuakeAbility:createVisualEffectsFromServer(effectData)
		-- Get the player who cast the ability
		local caster = Players:GetPlayerByUserId(effectData.casterUserId)
		local _result = caster
		if _result ~= nil then
			_result = _result.Character
		end
		if not _result then
			return nil
		end
		print(`🥊 Creating {effectData.punchType} quake punch effects for {caster.Name}`)
		local character = caster.Character
		local rightHand = character:FindFirstChild("RightHand")
		if not rightHand then
			return nil
		end
		-- Phase 1: Create sphere(s) based on punch type (same as original)
		if effectData.punchType == "single" then
			createQuakeSphere(self, character)
		else
			createSequentialQuakeSpheres(self, character)
		end
		-- Phase 2: Execute punch animation and effects (after 2 seconds like original)
		task.delay(2, function()
			if character.Parent then
				executeQuakePunch(self, character, rightHand, effectData.punchType)
				-- Create environmental effects
				local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
				if humanoidRootPart then
					createCameraShake(5, 0.5)
					createMapShockwave(humanoidRootPart.Position, 200, 2)
				end
			end
		end)
	end
	function QuakeAbility:activate(punchType)
		if punchType == nil then
			punchType = "single"
		end
		if self:isOnCooldown() then
			return nil
		end
		if not self.networkingInitialized then
			-- Try to initialize networking one more time as a fallback
			print("🔄 QuakeAbility: Attempting to initialize networking on demand...")
			self:tryInitializeNetworking()
			if not self.networkingInitialized then
				warn("⚠️ QuakeAbility: Server not ready yet. Please wait a moment and try again.")
				warn("💡 Tip: Use the WorldTestingPanel to check server status and RemoteEvents.")
				return nil
			end
		end
		local player = Players.LocalPlayer
		local character = player.Character
		if not character then
			return nil
		end
		local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
		if not humanoidRootPart then
			return nil
		end
		print(`🥊 Requesting {punchType} punch from server for visual sync`)
		-- Send request to server using Core framework
		local result = fireServer(Core.eventName("WhitebeardVisualSync"), {
			punchType = punchType,
			casterUserId = player.UserId,
			position = humanoidRootPart.Position,
			timestamp = tick(),
		})
		if result:isError() then
			warn(`Failed to send Whitebeard ability to server: {result:getError().message}`)
			return nil
		end
		-- Start cooldown immediately to prevent spam while waiting for server response
		self:startCooldown()
	end
end
return {
	QuakeAbility = QuakeAbility,
}
