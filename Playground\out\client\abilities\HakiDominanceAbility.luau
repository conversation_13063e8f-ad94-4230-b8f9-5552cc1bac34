-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local AbilityBase = TS.import(script, script.Parent, "AbilityBase").AbilityBase
local _services = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services")
local TweenService = _services.TweenService
local RunService = _services.RunService
local Workspace = _services.Workspace
local Players = _services.Players
local Lighting = _services.Lighting
local HakiDominanceAbility
do
	local super = AbilityBase
	HakiDominanceAbility = setmetatable({}, {
		__tostring = function()
			return "HakiDominanceAbility"
		end,
		__index = super,
	})
	HakiDominanceAbility.__index = HakiDominanceAbility
	function HakiDominanceAbility.new(...)
		local self = setmetatable({}, HakiDominanceAbility)
		return self:constructor(...) or self
	end
	function HakiDominanceAbility:constructor()
		super.constructor(self, "HAKI_DOMINANCE", 25)
		self.lightningEffects = {}
		self.cooldownEndTime = 0
		self.isActive = false
	end
	function HakiDominanceAbility:isOnCooldown()
		return tick() < self.cooldownEndTime
	end
	function HakiDominanceAbility:startCooldown()
		self.cooldownEndTime = tick() + self:getCooldownTime()
	end
	function HakiDominanceAbility:activate()
		if self:isOnCooldown() or self.isActive then
			return nil
		end
		local player = Players.LocalPlayer
		local character = player.Character
		if not character then
			return nil
		end
		local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
		if not humanoidRootPart then
			return nil
		end
		print("🔴 Activating Rayleigh's Haki Dominance")
		self.isActive = true
		-- Phase 1: Character Haki preparation animation
		self:createHakiDominanceAnimation(character)
		-- Phase 2: Create overwhelming red atmosphere
		self:createRedAtmosphere()
		-- Phase 2: Create map-wide red lightning effects
		self:createMapWideLightning()
		-- Phase 3: Create camera shake and pressure effect
		self:createPressureEffect()
		-- Phase 4: Create character aura
		self:createCharacterAura(character)
		-- Phase 5: Create red glass sparkle effects
		self:createRedGlassSparkles()
		-- Phase 6: Create floating glass shards
		self:createFloatingGlassShards()
		-- Duration: 8 seconds
		task.delay(8, function()
			self:cleanupEffects()
			self.isActive = false
		end)
		self:startCooldown()
	end
	function HakiDominanceAbility:createHakiDominanceAnimation(character)
		print("🔴 Creating Rayleigh's Haki Dominance animation")
		local humanoid = character:FindFirstChild("Humanoid")
		if not humanoid then
			return nil
		end
		-- Create proper Motor6D animation like QuakeAbility
		self:createCharacterAnimation(character)
	end
	function HakiDominanceAbility:createCharacterAnimation(character)
		local torso = character:FindFirstChild("Torso")
		local upperTorso = character:FindFirstChild("UpperTorso")
		if torso then
			-- R6 character
			self:animateR6Character(torso)
		elseif upperTorso then
			-- R15 character
			self:animateR15Character(upperTorso)
		end
	end
	function HakiDominanceAbility:animateR6Character(torso)
		local rightShoulder = torso:FindFirstChild("Right Shoulder")
		local leftShoulder = torso:FindFirstChild("Left Shoulder")
		local rightHip = torso:FindFirstChild("Right Hip")
		local leftHip = torso:FindFirstChild("Left Hip")
		if not rightShoulder or not leftShoulder then
			return nil
		end
		-- Store original C0 values
		local originalRightC0 = rightShoulder.C0
		local originalLeftC0 = leftShoulder.C0
		local originalRightHipC0 = if rightHip then rightHip.C0 else nil
		local originalLeftHipC0 = if leftHip then leftHip.C0 else nil
		-- Phase 1: Concentration stance (0-1s) - Arms at sides, focusing
		print("🔴 Haki concentration stance")
		local _exp = TweenInfo.new(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
		local _object = {}
		local _left = "C0"
		local _arg0 = CFrame.Angles(math.pi / 12, 0, -math.pi / 12)
		_object[_left] = originalRightC0 * _arg0
		local rightArmConcentrate = TweenService:Create(rightShoulder, _exp, _object)
		local _exp_1 = TweenInfo.new(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
		local _object_1 = {}
		local _left_1 = "C0"
		local _arg0_1 = CFrame.Angles(math.pi / 12, 0, math.pi / 12)
		_object_1[_left_1] = originalLeftC0 * _arg0_1
		local leftArmConcentrate = TweenService:Create(leftShoulder, _exp_1, _object_1)
		rightArmConcentrate:Play()
		leftArmConcentrate:Play()
		-- Stable, powerful stance
		if rightHip and leftHip and originalRightHipC0 and originalLeftHipC0 then
			local _exp_2 = TweenInfo.new(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
			local _object_2 = {}
			local _left_2 = "C0"
			local _arg0_2 = CFrame.Angles(0, 0, math.pi / 24)
			_object_2[_left_2] = originalRightHipC0 * _arg0_2
			local rightLegStance = TweenService:Create(rightHip, _exp_2, _object_2)
			local _exp_3 = TweenInfo.new(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
			local _object_3 = {}
			local _left_3 = "C0"
			local _arg0_3 = CFrame.Angles(0, 0, -math.pi / 24)
			_object_3[_left_3] = originalLeftHipC0 * _arg0_3
			local leftLegStance = TweenService:Create(leftHip, _exp_3, _object_3)
			rightLegStance:Play()
			leftLegStance:Play()
		end
		-- Phase 2: Haki buildup (1-2s) - Arms slightly raised, power building
		task.delay(1, function()
			print("🔴 Haki power buildup")
			local _exp_2 = TweenInfo.new(1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut)
			local _object_2 = {}
			local _left_2 = "C0"
			local _arg0_2 = CFrame.Angles(-math.pi / 8, 0, -math.pi / 6)
			_object_2[_left_2] = originalRightC0 * _arg0_2
			local rightArmBuildup = TweenService:Create(rightShoulder, _exp_2, _object_2)
			local _exp_3 = TweenInfo.new(1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut)
			local _object_3 = {}
			local _left_3 = "C0"
			local _arg0_3 = CFrame.Angles(-math.pi / 8, 0, math.pi / 6)
			_object_3[_left_3] = originalLeftC0 * _arg0_3
			local leftArmBuildup = TweenService:Create(leftShoulder, _exp_3, _object_3)
			rightArmBuildup:Play()
			leftArmBuildup:Play()
		end)
		-- Phase 3: Haki release (2-3s) - Dramatic power release pose
		task.delay(2, function()
			print("💥 Haki power release!")
			local _exp_2 = TweenInfo.new(1, Enum.EasingStyle.Back, Enum.EasingDirection.Out)
			local _object_2 = {}
			local _left_2 = "C0"
			local _arg0_2 = CFrame.Angles(-math.pi / 4, 0, -math.pi / 3)
			_object_2[_left_2] = originalRightC0 * _arg0_2
			local rightArmRelease = TweenService:Create(rightShoulder, _exp_2, _object_2)
			local _exp_3 = TweenInfo.new(1, Enum.EasingStyle.Back, Enum.EasingDirection.Out)
			local _object_3 = {}
			local _left_3 = "C0"
			local _arg0_3 = CFrame.Angles(-math.pi / 4, 0, math.pi / 3)
			_object_3[_left_3] = originalLeftC0 * _arg0_3
			local leftArmRelease = TweenService:Create(leftShoulder, _exp_3, _object_3)
			rightArmRelease:Play()
			leftArmRelease:Play()
		end)
		-- Phase 4: Dominance pose (3-6s) - Intimidating stance
		task.delay(3, function()
			print("🔴 Haki dominance pose")
			local _exp_2 = TweenInfo.new(3, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut)
			local _object_2 = {}
			local _left_2 = "C0"
			local _arg0_2 = CFrame.Angles(-math.pi / 6, 0, -math.pi / 4)
			_object_2[_left_2] = originalRightC0 * _arg0_2
			local rightArmDominance = TweenService:Create(rightShoulder, _exp_2, _object_2)
			local _exp_3 = TweenInfo.new(3, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut)
			local _object_3 = {}
			local _left_3 = "C0"
			local _arg0_3 = CFrame.Angles(-math.pi / 6, 0, math.pi / 4)
			_object_3[_left_3] = originalLeftC0 * _arg0_3
			local leftArmDominance = TweenService:Create(leftShoulder, _exp_3, _object_3)
			rightArmDominance:Play()
			leftArmDominance:Play()
			-- Wide, powerful stance
			if rightHip and leftHip and originalRightHipC0 and originalLeftHipC0 then
				local _exp_4 = TweenInfo.new(3, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut)
				local _object_4 = {}
				local _left_4 = "C0"
				local _arg0_4 = CFrame.Angles(0, 0, math.pi / 12)
				_object_4[_left_4] = originalRightHipC0 * _arg0_4
				local rightLegDominance = TweenService:Create(rightHip, _exp_4, _object_4)
				local _exp_5 = TweenInfo.new(3, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut)
				local _object_5 = {}
				local _left_5 = "C0"
				local _arg0_5 = CFrame.Angles(0, 0, -math.pi / 12)
				_object_5[_left_5] = originalLeftHipC0 * _arg0_5
				local leftLegDominance = TweenService:Create(leftHip, _exp_5, _object_5)
				rightLegDominance:Play()
				leftLegDominance:Play()
			end
		end)
		-- Phase 5: Return to normal (6-8s)
		task.delay(6, function()
			print("🔴 Returning to normal stance")
			local rightArmReturn = TweenService:Create(rightShoulder, TweenInfo.new(2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
				C0 = originalRightC0,
			})
			local leftArmReturn = TweenService:Create(leftShoulder, TweenInfo.new(2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
				C0 = originalLeftC0,
			})
			rightArmReturn:Play()
			leftArmReturn:Play()
			-- Restore legs
			if rightHip and leftHip and originalRightHipC0 and originalLeftHipC0 then
				local rightLegReturn = TweenService:Create(rightHip, TweenInfo.new(2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
					C0 = originalRightHipC0,
				})
				local leftLegReturn = TweenService:Create(leftHip, TweenInfo.new(2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
					C0 = originalLeftHipC0,
				})
				rightLegReturn:Play()
				leftLegReturn:Play()
			end
		end)
	end
	function HakiDominanceAbility:animateR15Character(upperTorso)
		local rightShoulder = upperTorso:FindFirstChild("RightShoulder")
		local leftShoulder = upperTorso:FindFirstChild("LeftShoulder")
		if not rightShoulder or not leftShoulder then
			return nil
		end
		-- Store original C0 values
		local originalRightC0 = rightShoulder.C0
		local originalLeftC0 = leftShoulder.C0
		-- Similar animation sequence for R15
		print("🔴 R15 Haki Dominance animation")
		-- Phase 1: Concentration
		local _exp = TweenInfo.new(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
		local _object = {}
		local _left = "C0"
		local _arg0 = CFrame.Angles(math.pi / 12, 0, -math.pi / 12)
		_object[_left] = originalRightC0 * _arg0
		local rightArmConcentrate = TweenService:Create(rightShoulder, _exp, _object)
		local _exp_1 = TweenInfo.new(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
		local _object_1 = {}
		local _left_1 = "C0"
		local _arg0_1 = CFrame.Angles(math.pi / 12, 0, math.pi / 12)
		_object_1[_left_1] = originalLeftC0 * _arg0_1
		local leftArmConcentrate = TweenService:Create(leftShoulder, _exp_1, _object_1)
		rightArmConcentrate:Play()
		leftArmConcentrate:Play()
		-- Continue with similar phases...
		task.delay(1, function()
			local _exp_2 = TweenInfo.new(1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut)
			local _object_2 = {}
			local _left_2 = "C0"
			local _arg0_2 = CFrame.Angles(-math.pi / 8, 0, -math.pi / 6)
			_object_2[_left_2] = originalRightC0 * _arg0_2
			local rightArmBuildup = TweenService:Create(rightShoulder, _exp_2, _object_2)
			local _exp_3 = TweenInfo.new(1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut)
			local _object_3 = {}
			local _left_3 = "C0"
			local _arg0_3 = CFrame.Angles(-math.pi / 8, 0, math.pi / 6)
			_object_3[_left_3] = originalLeftC0 * _arg0_3
			local leftArmBuildup = TweenService:Create(leftShoulder, _exp_3, _object_3)
			rightArmBuildup:Play()
			leftArmBuildup:Play()
		end)
		-- Return to normal (6s delay)
		task.delay(6, function()
			local rightArmReturn = TweenService:Create(rightShoulder, TweenInfo.new(2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
				C0 = originalRightC0,
			})
			local leftArmReturn = TweenService:Create(leftShoulder, TweenInfo.new(2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
				C0 = originalLeftC0,
			})
			rightArmReturn:Play()
			leftArmReturn:Play()
		end)
	end
	function HakiDominanceAbility:createRedAtmosphere()
		print("🔴 Creating red atmospheric dominance")
		-- Create red fog/atmosphere effect
		local atmosphere = Lighting:FindFirstChild("Atmosphere")
		if atmosphere then
			local originalColor = atmosphere.Color
			local originalHaze = atmosphere.Haze
			local originalGlare = atmosphere.Glare
			-- Tween to red dominance atmosphere
			local atmosphereTween = TweenService:Create(atmosphere, TweenInfo.new(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
				Color = Color3.fromRGB(150, 0, 0),
				Haze = 2,
				Glare = 1,
			})
			atmosphereTween:Play()
			-- Restore after duration
			task.delay(7, function()
				local restoreTween = TweenService:Create(atmosphere, TweenInfo.new(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
					Color = originalColor,
					Haze = originalHaze,
					Glare = originalGlare,
				})
				restoreTween:Play()
			end)
		end
		-- Create red lighting effect
		local originalAmbient = Lighting.Ambient
		local originalOutdoorAmbient = Lighting.OutdoorAmbient
		local lightingTween = TweenService:Create(Lighting, TweenInfo.new(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
			Ambient = Color3.fromRGB(50, 0, 0),
			OutdoorAmbient = Color3.fromRGB(80, 0, 0),
		})
		lightingTween:Play()
		-- Restore lighting after duration
		task.delay(7, function()
			local restoreLightingTween = TweenService:Create(Lighting, TweenInfo.new(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
				Ambient = originalAmbient,
				OutdoorAmbient = originalOutdoorAmbient,
			})
			restoreLightingTween:Play()
		end)
	end
	function HakiDominanceAbility:createMapWideLightning()
		print("⚡ Creating map-wide red lightning effects")
		-- Create multiple lightning strikes across the map
		for i = 0, 14 do
			task.delay(i * 0.3, function()
				self:createRedLightningStrike()
			end)
		end
		-- Create continuous lightning for duration
		self.dominanceConnection = RunService.Heartbeat:Connect(function()
			if math.random() < 0.3 then
				self:createRedLightningStrike()
			end
		end)
		-- Stop continuous lightning after 7 seconds
		task.delay(7, function()
			if self.dominanceConnection then
				self.dominanceConnection:Disconnect()
				self.dominanceConnection = nil
			end
		end)
	end
	function HakiDominanceAbility:createRedLightningStrike()
		-- Random position across the map
		local randomX = math.random(-200, 200)
		local randomZ = math.random(-200, 200)
		local startY = 100
		local endY = 5
		local startPosition = Vector3.new(randomX, startY, randomZ)
		local endPosition = Vector3.new(randomX + math.random(-10, 10), endY, randomZ + math.random(-10, 10))
		-- Create lightning bolt
		local lightning = Instance.new("Part")
		lightning.Name = "HakiLightning"
		lightning.Size = Vector3.new(0.5, (startY - endY), 0.5)
		lightning.Color = Color3.fromRGB(255, 0, 0)
		lightning.Material = Enum.Material.Neon
		lightning.Transparency = 0.2
		lightning.CanCollide = false
		lightning.Anchored = true
		lightning.Shape = Enum.PartType.Cylinder
		-- Position between start and end
		local midPosition = (startPosition + endPosition) / 2
		lightning.Position = midPosition
		-- Rotate to point from start to end
		local direction = (endPosition - startPosition).Unit
		lightning.CFrame = CFrame.lookAt(midPosition, midPosition + direction)
		lightning.Parent = Workspace
		local _exp = self.lightningEffects
		table.insert(_exp, lightning)
		-- Add glow effect
		local pointLight = Instance.new("PointLight")
		pointLight.Color = Color3.fromRGB(255, 0, 0)
		pointLight.Brightness = 3
		pointLight.Range = 20
		pointLight.Parent = lightning
		-- Flicker effect
		local flickerTween = TweenService:Create(lightning, TweenInfo.new(0.1, Enum.EasingStyle.Quad, Enum.EasingDirection.InOut, -1, true), {
			Transparency = 0.8,
		})
		flickerTween:Play()
		-- Remove after short duration
		task.delay(0.5, function()
			flickerTween:Cancel()
			local fadeTween = TweenService:Create(lightning, TweenInfo.new(0.3, Enum.EasingStyle.Quad), {
				Transparency = 1,
			})
			fadeTween:Play()
			fadeTween.Completed:Connect(function()
				lightning:Destroy()
				local index = (table.find(self.lightningEffects, lightning) or 0) - 1
				if index > -1 then
					table.remove(self.lightningEffects, index + 1)
				end
			end)
		end)
	end
	function HakiDominanceAbility:createPressureEffect()
		print("💥 Creating Haki pressure effect")
		-- Create camera shake
		local camera = Workspace.CurrentCamera
		if not camera then
			return nil
		end
		local originalCFrame = camera.CFrame
		local shakeTime = 0
		local shakeDuration = 8
		local shakeConnection
		shakeConnection = RunService.RenderStepped:Connect(function(deltaTime)
			shakeTime += deltaTime
			if shakeTime >= shakeDuration then
				camera.CFrame = originalCFrame
				shakeConnection:Disconnect()
				return nil
			end
			-- Intense shaking that gradually reduces
			local intensity = 3 * (1 - shakeTime / shakeDuration)
			local randomOffset = Vector3.new((math.random() - 0.5) * intensity, (math.random() - 0.5) * intensity, (math.random() - 0.5) * intensity)
			camera.CFrame = originalCFrame + randomOffset
		end)
	end
	function HakiDominanceAbility:createCharacterAura(character)
		print("🔴 Creating character Haki aura")
		local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
		if not humanoidRootPart then
			return nil
		end
		-- Create red aura sphere around character
		local aura = Instance.new("Part")
		aura.Name = "HakiAura"
		aura.Shape = Enum.PartType.Ball
		aura.Size = Vector3.new(15, 15, 15)
		aura.Color = Color3.fromRGB(255, 0, 0)
		aura.Material = Enum.Material.ForceField
		aura.Transparency = 0.7
		aura.CanCollide = false
		aura.Anchored = true
		aura.Position = humanoidRootPart.Position
		aura.Parent = Workspace
		-- Add intense red light
		local auraLight = Instance.new("PointLight")
		auraLight.Color = Color3.fromRGB(255, 0, 0)
		auraLight.Brightness = 5
		auraLight.Range = 30
		auraLight.Parent = aura
		-- Pulsing effect
		local pulseTween = TweenService:Create(aura, TweenInfo.new(0.5, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true), {
			Size = Vector3.new(18, 18, 18),
			Transparency = 0.5,
		})
		pulseTween:Play()
		-- Create orbiting glass sparkles around the character
		self:createOrbitingSparkles(humanoidRootPart)
		-- Follow character
		local followConnection = RunService.Heartbeat:Connect(function()
			if humanoidRootPart.Parent and aura.Parent then
				aura.Position = humanoidRootPart.Position
			end
		end)
		-- Cleanup after duration
		task.delay(8, function()
			followConnection:Disconnect()
			pulseTween:Cancel()
			local fadeTween = TweenService:Create(aura, TweenInfo.new(1, Enum.EasingStyle.Quad), {
				Transparency = 1,
			})
			fadeTween:Play()
			fadeTween.Completed:Connect(function()
				return aura:Destroy()
			end)
		end)
	end
	function HakiDominanceAbility:cleanupEffects()
		print("🧹 Cleaning up Haki Dominance effects")
		-- Clean up any remaining lightning effects
		for _, lightning in self.lightningEffects do
			if lightning.Parent then
				lightning:Destroy()
			end
		end
		self.lightningEffects = {}
		-- Disconnect any remaining connections
		if self.dominanceConnection then
			self.dominanceConnection:Disconnect()
			self.dominanceConnection = nil
		end
	end
	function HakiDominanceAbility:createRedGlassSparkles()
		print("✨ Creating red glass sparkle effects")
		-- Create sparkles around the character and across the map
		for i = 0, 49 do
			task.delay(i * 0.1, function()
				self:createSingleGlassSparkle()
			end)
		end
		-- Create continuous sparkles during the ability duration
		local sparkleConnection = RunService.Heartbeat:Connect(function()
			if math.random() < 0.8 then
				self:createSingleGlassSparkle()
			end
		end)
		-- Stop sparkles after 7 seconds
		task.delay(7, function()
			sparkleConnection:Disconnect()
		end)
	end
	function HakiDominanceAbility:createSingleGlassSparkle()
		local player = Players.LocalPlayer
		local character = player.Character
		if not character then
			return nil
		end
		local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
		if not humanoidRootPart then
			return nil
		end
		-- Random position around the character and across the map
		local isNearCharacter = math.random() < 0.6
		local sparklePosition
		if isNearCharacter then
			-- Sparkles around character (within 30 studs)
			local randomOffset = Vector3.new(math.random(-30, 30), math.random(-10, 20), math.random(-30, 30))
			sparklePosition = humanoidRootPart.Position + randomOffset
		else
			-- Sparkles across the map
			sparklePosition = Vector3.new(math.random(-150, 150), math.random(5, 80), math.random(-150, 150))
		end
		-- Create sharp glass sparkle
		local sparkle = Instance.new("Part")
		sparkle.Name = "HakiGlassSparkle"
		sparkle.Shape = Enum.PartType.Block
		sparkle.Size = Vector3.new(0.3, 0.3, 0.3)
		sparkle.Color = Color3.fromRGB(255, 50, 50)
		sparkle.Material = Enum.Material.Glass
		sparkle.Transparency = 0.1
		sparkle.CanCollide = false
		sparkle.Anchored = true
		sparkle.Position = sparklePosition
		-- Random rotation for sharp glass effect
		local _cFrame = sparkle.CFrame
		local _arg0 = CFrame.Angles(math.random() * math.pi * 2, math.random() * math.pi * 2, math.random() * math.pi * 2)
		sparkle.CFrame = _cFrame * _arg0
		sparkle.Parent = Workspace
		local _exp = self.lightningEffects
		table.insert(_exp, sparkle)
		-- Add intense red light
		local sparkleLight = Instance.new("PointLight")
		sparkleLight.Color = Color3.fromRGB(255, 0, 0)
		sparkleLight.Brightness = 4
		sparkleLight.Range = 8
		sparkleLight.Parent = sparkle
		-- Create sparkle animation - rapid spinning and scaling
		local _exp_1 = TweenInfo.new(0.5, Enum.EasingStyle.Linear, Enum.EasingDirection.InOut, -1)
		local _object = {}
		local _left = "CFrame"
		local _cFrame_1 = sparkle.CFrame
		local _arg0_1 = CFrame.Angles(math.pi * 4, math.pi * 4, math.pi * 4)
		_object[_left] = _cFrame_1 * _arg0_1
		local spinTween = TweenService:Create(sparkle, _exp_1, _object)
		spinTween:Play()
		-- Pulsing scale effect
		local scaleTween = TweenService:Create(sparkle, TweenInfo.new(0.3, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true), {
			Size = Vector3.new(0.6, 0.6, 0.6),
		})
		scaleTween:Play()
		-- Flickering transparency
		local flickerTween = TweenService:Create(sparkle, TweenInfo.new(0.1, Enum.EasingStyle.Quad, Enum.EasingDirection.InOut, -1, true), {
			Transparency = 0.5,
		})
		flickerTween:Play()
		-- Remove after random duration
		local lifetime = math.random(1, 3)
		task.delay(lifetime, function()
			spinTween:Cancel()
			scaleTween:Cancel()
			flickerTween:Cancel()
			local fadeTween = TweenService:Create(sparkle, TweenInfo.new(0.5, Enum.EasingStyle.Quad), {
				Transparency = 1,
				Size = Vector3.new(0.1, 0.1, 0.1),
			})
			fadeTween:Play()
			fadeTween.Completed:Connect(function()
				sparkle:Destroy()
				local index = (table.find(self.lightningEffects, sparkle) or 0) - 1
				if index > -1 then
					table.remove(self.lightningEffects, index + 1)
				end
			end)
		end)
	end
	function HakiDominanceAbility:createFloatingGlassShards()
		print("🔴 Creating floating red glass shards")
		local player = Players.LocalPlayer
		local character = player.Character
		if not character then
			return nil
		end
		local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
		if not humanoidRootPart then
			return nil
		end
		-- Create larger floating glass shards around the character
		for i = 0, 14 do
			task.delay(i * 0.2, function()
				self:createFloatingGlassShard(humanoidRootPart.Position)
			end)
		end
	end
	function HakiDominanceAbility:createFloatingGlassShard(centerPosition)
		-- Random position in a circle around the character
		local angle = math.random() * math.pi * 2
		local distance = math.random(10, 25)
		local height = math.random(5, 15)
		local _centerPosition = centerPosition
		local _vector3 = Vector3.new(math.cos(angle) * distance, height, math.sin(angle) * distance)
		local shardPosition = _centerPosition + _vector3
		-- Create sharp glass shard
		local shard = Instance.new("Part")
		shard.Name = "HakiGlassShard"
		shard.Shape = Enum.PartType.Block
		shard.Size = Vector3.new(math.random(0.5, 1.5), math.random(2, 4), math.random(0.3, 0.8))
		shard.Color = Color3.fromRGB(200, 0, 0)
		shard.Material = Enum.Material.Glass
		shard.Transparency = 0.2
		shard.CanCollide = false
		shard.Anchored = true
		shard.Position = shardPosition
		-- Random sharp rotation
		local _cFrame = shard.CFrame
		local _arg0 = CFrame.Angles(math.random() * math.pi, math.random() * math.pi * 2, math.random() * math.pi)
		shard.CFrame = _cFrame * _arg0
		shard.Parent = Workspace
		local _exp = self.lightningEffects
		table.insert(_exp, shard)
		-- Add red glow
		local shardLight = Instance.new("PointLight")
		shardLight.Color = Color3.fromRGB(255, 0, 0)
		shardLight.Brightness = 2
		shardLight.Range = 12
		shardLight.Parent = shard
		-- Floating animation - slow rotation and gentle bobbing
		local _exp_1 = TweenInfo.new(3, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true)
		local _object = {}
		local _left = "Position"
		local _vector3_1 = Vector3.new(0, 2, 0)
		_object[_left] = shardPosition + _vector3_1
		local floatTween = TweenService:Create(shard, _exp_1, _object)
		floatTween:Play()
		local _exp_2 = TweenInfo.new(4, Enum.EasingStyle.Linear, Enum.EasingDirection.InOut, -1)
		local _object_1 = {}
		local _left_1 = "CFrame"
		local _cFrame_1 = shard.CFrame
		local _arg0_1 = CFrame.Angles(0, math.pi * 2, 0)
		_object_1[_left_1] = _cFrame_1 * _arg0_1
		local rotateTween = TweenService:Create(shard, _exp_2, _object_1)
		rotateTween:Play()
		-- Subtle pulsing glow
		local glowTween = TweenService:Create(shardLight, TweenInfo.new(1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true), {
			Brightness = 3,
		})
		glowTween:Play()
		-- Remove after ability duration
		task.delay(8, function()
			floatTween:Cancel()
			rotateTween:Cancel()
			glowTween:Cancel()
			local fadeTween = TweenService:Create(shard, TweenInfo.new(1, Enum.EasingStyle.Quad), {
				Transparency = 1,
				Size = Vector3.new(0.1, 0.1, 0.1),
			})
			fadeTween:Play()
			fadeTween.Completed:Connect(function()
				shard:Destroy()
				local index = (table.find(self.lightningEffects, shard) or 0) - 1
				if index > -1 then
					table.remove(self.lightningEffects, index + 1)
				end
			end)
		end)
	end
	function HakiDominanceAbility:createOrbitingSparkles(humanoidRootPart)
		print("✨ Creating orbiting glass sparkles around character")
		-- Create 8 orbiting sparkles
		for i = 0, 7 do
			local angle = (i / 8) * math.pi * 2
			self:createOrbitingSparkle(humanoidRootPart, angle, i)
		end
	end
	function HakiDominanceAbility:createOrbitingSparkle(humanoidRootPart, startAngle, index)
		local orbitRadius = 8
		local orbitHeight = 3
		-- Create orbiting sparkle
		local sparkle = Instance.new("Part")
		sparkle.Name = "OrbitingHakiSparkle"
		sparkle.Shape = Enum.PartType.Block
		sparkle.Size = Vector3.new(0.4, 0.4, 0.4)
		sparkle.Color = Color3.fromRGB(255, 100, 100)
		sparkle.Material = Enum.Material.Glass
		sparkle.Transparency = 0.2
		sparkle.CanCollide = false
		sparkle.Anchored = true
		sparkle.Parent = Workspace
		local _exp = self.lightningEffects
		table.insert(_exp, sparkle)
		-- Add bright light
		local sparkleLight = Instance.new("PointLight")
		sparkleLight.Color = Color3.fromRGB(255, 0, 0)
		sparkleLight.Brightness = 3
		sparkleLight.Range = 6
		sparkleLight.Parent = sparkle
		-- Orbiting animation
		local currentAngle = startAngle
		local orbitSpeed = 2
		local orbitConnection
		orbitConnection = RunService.Heartbeat:Connect(function(deltaTime)
			if not humanoidRootPart.Parent or not sparkle.Parent then
				orbitConnection:Disconnect()
				return nil
			end
			currentAngle += orbitSpeed * deltaTime
			local _position = humanoidRootPart.Position
			local _vector3 = Vector3.new(math.cos(currentAngle) * orbitRadius, orbitHeight + math.sin(currentAngle * 3) * 1, math.sin(currentAngle) * orbitRadius)
			local orbitPosition = _position + _vector3
			sparkle.Position = orbitPosition
			-- Rotate the sparkle itself for extra sparkle effect
			local _exp_1 = CFrame.lookAt(orbitPosition, humanoidRootPart.Position)
			local _arg0 = CFrame.Angles(currentAngle * 2, currentAngle * 3, currentAngle * 1.5)
			sparkle.CFrame = _exp_1 * _arg0
		end)
		-- Pulsing scale effect
		local scaleTween = TweenService:Create(sparkle, TweenInfo.new(0.4, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true), {
			Size = Vector3.new(0.7, 0.7, 0.7),
		})
		scaleTween:Play()
		-- Cleanup after duration
		task.delay(8, function()
			orbitConnection:Disconnect()
			scaleTween:Cancel()
			local fadeTween = TweenService:Create(sparkle, TweenInfo.new(0.5, Enum.EasingStyle.Quad), {
				Transparency = 1,
				Size = Vector3.new(0.1, 0.1, 0.1),
			})
			fadeTween:Play()
			fadeTween.Completed:Connect(function()
				sparkle:Destroy()
				local index = (table.find(self.lightningEffects, sparkle) or 0) - 1
				if index > -1 then
					table.remove(self.lightningEffects, index + 1)
				end
			end)
		end)
	end
end
return {
	HakiDominanceAbility = HakiDominanceAbility,
}
