import * as React from "@rbxts/react";
interface AutoDockFrameProps {
    children?: React.ReactNode;
    dockPosition?: "TopLeft" | "TopRight" | "BottomLeft" | "BottomRight" | "Center";
    size?: UDim2;
    padding?: number;
    backgroundColor?: string;
    backgroundTransparency?: number;
    margin?: number;
    zIndex?: number;
    elementId?: string;
    bringToFrontOnMount?: boolean;
}
export declare function AutoDockFrame(props: AutoDockFrameProps): React.ReactElement;
export {};
