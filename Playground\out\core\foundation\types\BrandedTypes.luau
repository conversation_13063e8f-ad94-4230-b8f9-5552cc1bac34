-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local exports = {}
-- Re-export Error type for convenience
exports.createError = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "types", "RobloxError").createError
local BrandedTypes = {}
do
	local _container = BrandedTypes
	local function playerId(id)
		return id
	end
	_container.playerId = playerId
	local function entityId(id)
		return id
	end
	_container.entityId = entityId
	local function serviceName(name)
		return name
	end
	_container.serviceName = serviceName
	local function eventName(name)
		return name
	end
	_container.eventName = eventName
	local function componentId(id)
		return id
	end
	_container.componentId = componentId
	local function assetId(id)
		return id
	end
	_container.assetId = assetId
	local function configKey(key)
		return key
	end
	_container.configKey = configKey
end
exports.BrandedTypes = BrandedTypes
return exports
