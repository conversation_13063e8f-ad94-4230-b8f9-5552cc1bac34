import { Result } from "../foundation/types/Result";
import { Error } from "../foundation/types/RobloxError";
import { EventName } from "../foundation/types/BrandedTypes";
export declare class ClientCore {
    private static instance?;
    private eventHandlers;
    private remoteEvents;
    private isInitialized;
    private constructor();
    static getInstance(): ClientCore;
    initialize(): Promise<Result<void, Error>>;
    onServerEvent<T>(eventName: EventName, handler: (data: T) => void): Result<void, Error>;
    fireServer<T>(eventName: EventName, data: T): Result<void, Error>;
    private waitForServerEvents;
    getEventNames(): string[];
    isEventAvailable(eventName: EventName): boolean;
    waitForEvent(eventName: EventName, maxWaitTime?: number): Promise<Result<void, Error>>;
}
export declare const ClientCore_Instance: ClientCore;
export declare function initializeClientCore(): Promise<Result<void, Error>>;
export declare function onServerEvent<T>(eventName: EventName, handler: (data: T) => void): Result<void, Error>;
export declare function fireServer<T>(eventName: EventName, data: T): Result<void, Error>;
export declare function waitForEvent(eventName: EventName, maxWaitTime?: number): Promise<Result<void, Error>>;
