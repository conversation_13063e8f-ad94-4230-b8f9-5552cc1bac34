-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local _design = TS.import(script, game:GetService("ReplicatedStorage"), "core", "design")
local COLORS = _design.COLORS
local TYPOGRAPHY = _design.TYPOGRAPHY
local SIZES = _design.SIZES
local function Label(props)
	local _condition = props.textColor
	if _condition == nil then
		_condition = COLORS.text.main
	end
	local textColor = _condition
	local _condition_1 = props.fontSize
	if _condition_1 == nil then
		_condition_1 = SIZES.fontSize
	end
	local fontSize = _condition_1
	local alignment = props.alignment or Enum.TextXAlignment.Left
	local _condition_2 = props.textWrapped
	if _condition_2 == nil then
		_condition_2 = false
	end
	local textWrapped = _condition_2
	-- Smart sizing: use autoSize if specified or no size provided
	local _condition_3 = props.autoSize
	if _condition_3 == nil then
		_condition_3 = (props.size == nil)
	end
	local useAutoSize = _condition_3
	local size = props.size or (if useAutoSize then (if textWrapped then UDim2.new(1, 0, 0, 0) else UDim2.new(0, 0, 0, 0)) else UDim2.new(1, 0, 0, fontSize + 4))
	return React.createElement("textlabel", {
		Text = props.text,
		TextColor3 = Color3.fromHex(textColor),
		Font = TYPOGRAPHY.font,
		TextSize = fontSize,
		Size = size,
		Position = props.position,
		AnchorPoint = props.anchorPoint,
		LayoutOrder = props.layoutOrder,
		BackgroundTransparency = 1,
		TextXAlignment = alignment,
		TextWrapped = textWrapped,
		AutomaticSize = if useAutoSize then Enum.AutomaticSize.XY else Enum.AutomaticSize.None,
		FontFace = if props.bold then Font.new(TYPOGRAPHY.font.Name, Enum.FontWeight.Bold) else nil,
	})
end
return {
	Label = Label,
}
