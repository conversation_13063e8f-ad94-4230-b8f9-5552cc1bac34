-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local AbilityBase = TS.import(script, script.Parent, "AbilityBase").AbilityBase
local _services = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services")
local TweenService = _services.TweenService
local RunService = _services.RunService
local Workspace = _services.Workspace
local Players = _services.Players
local Lighting = _services.Lighting
local FireFistAbility
do
	local super = AbilityBase
	FireFistAbility = setmetatable({}, {
		__tostring = function()
			return "FireFistAbility"
		end,
		__index = super,
	})
	FireFistAbility.__index = FireFistAbility
	function FireFistAbility.new(...)
		local self = setmetatable({}, FireFistAbility)
		return self:constructor(...) or self
	end
	function FireFistAbility:constructor()
		super.constructor(self, "FIRE_FIST", 8)
		self.fireEffects = {}
		self.cooldownEndTime = 0
		self.isActive = false
	end
	function FireFistAbility:isOnCooldown()
		return tick() < self.cooldownEndTime
	end
	function FireFistAbility:startCooldown()
		self.cooldownEndTime = tick() + self:getCooldownTime()
	end
	function FireFistAbility:activate()
		if self:isOnCooldown() or self.isActive then
			return nil
		end
		local player = Players.LocalPlayer
		local character = player.Character
		if not character then
			return nil
		end
		local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
		local rightHand = character:FindFirstChild("RightHand")
		if not humanoidRootPart or not rightHand then
			return nil
		end
		print("🔥 Activating Ace's Fire Fist!")
		self.isActive = true
		-- Phase 1: Character fire preparation animation
		self:createFireFistAnimation(character)
		-- Phase 2: Character fire aura
		self:createCharacterFireAura(character)
		-- Phase 2: Create fire fist charging effect
		self:createFireFistCharge(rightHand)
		-- Phase 3: Launch the massive fire projectile
		task.delay(1.5, function()
			self:launchFireFist(humanoidRootPart)
		end)
		-- Phase 4: Create environmental fire effects
		self:createEnvironmentalFire()
		-- Duration: 5 seconds
		task.delay(5, function()
			self:cleanupEffects()
			self.isActive = false
		end)
		self:startCooldown()
	end
	function FireFistAbility:createFireFistAnimation(character)
		print("🔥 Creating Ace's Fire Fist animation")
		local humanoid = character:FindFirstChild("Humanoid")
		if not humanoid then
			return nil
		end
		-- Create proper Motor6D animation like QuakeAbility
		self:createCharacterAnimation(character)
	end
	function FireFistAbility:createCharacterAnimation(character)
		local torso = character:FindFirstChild("Torso")
		local upperTorso = character:FindFirstChild("UpperTorso")
		if torso then
			-- R6 character
			self:animateR6Character(torso)
		elseif upperTorso then
			-- R15 character
			self:animateR15Character(upperTorso)
		end
	end
	function FireFistAbility:animateR6Character(torso)
		local rightShoulder = torso:FindFirstChild("Right Shoulder")
		local leftShoulder = torso:FindFirstChild("Left Shoulder")
		local rightHip = torso:FindFirstChild("Right Hip")
		local leftHip = torso:FindFirstChild("Left Hip")
		if not rightShoulder or not leftShoulder then
			return nil
		end
		-- Store original C0 values
		local originalRightC0 = rightShoulder.C0
		local originalLeftC0 = leftShoulder.C0
		local originalRightHipC0 = if rightHip then rightHip.C0 else nil
		local originalLeftHipC0 = if leftHip then leftHip.C0 else nil
		-- Phase 1: Fire stance (0-0.5s) - Defensive position
		print("🔥 Fire stance preparation")
		local _exp = TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
		local _object = {}
		local _left = "C0"
		local _arg0 = CFrame.Angles(-math.pi / 6, 0, -math.pi / 8)
		_object[_left] = originalRightC0 * _arg0
		local rightArmStance = TweenService:Create(rightShoulder, _exp, _object)
		local _exp_1 = TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
		local _object_1 = {}
		local _left_1 = "C0"
		local _arg0_1 = CFrame.Angles(-math.pi / 6, 0, math.pi / 8)
		_object_1[_left_1] = originalLeftC0 * _arg0_1
		local leftArmStance = TweenService:Create(leftShoulder, _exp_1, _object_1)
		rightArmStance:Play()
		leftArmStance:Play()
		-- Phase 2: Charging pose (0.5-1.5s) - Right fist back, left forward
		task.delay(0.5, function()
			print("👊 Fire Fist charging")
			local _exp_2 = TweenInfo.new(1, Enum.EasingStyle.Back, Enum.EasingDirection.Out)
			local _object_2 = {}
			local _left_2 = "C0"
			local _arg0_2 = CFrame.Angles(-math.pi / 3, 0, -math.pi / 2)
			_object_2[_left_2] = originalRightC0 * _arg0_2
			local rightArmCharge = TweenService:Create(rightShoulder, _exp_2, _object_2)
			local _exp_3 = TweenInfo.new(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
			local _object_3 = {}
			local _left_3 = "C0"
			local _arg0_3 = CFrame.Angles(-math.pi / 4, 0, math.pi / 4)
			_object_3[_left_3] = originalLeftC0 * _arg0_3
			local leftArmBalance = TweenService:Create(leftShoulder, _exp_3, _object_3)
			rightArmCharge:Play()
			leftArmBalance:Play()
			-- Fighting stance with legs
			if rightHip and originalRightHipC0 then
				local _exp_4 = TweenInfo.new(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
				local _object_4 = {}
				local _left_4 = "C0"
				local _arg0_4 = CFrame.Angles(math.pi / 12, 0, 0)
				_object_4[_left_4] = originalRightHipC0 * _arg0_4
				local rightLegStance = TweenService:Create(rightHip, _exp_4, _object_4)
				rightLegStance:Play()
			end
		end)
		-- Phase 3: Fire Fist punch (1.5-2s) - Dramatic forward punch
		task.delay(1.5, function()
			print("🚀 Fire Fist punch!")
			local _exp_2 = TweenInfo.new(0.5, Enum.EasingStyle.Back, Enum.EasingDirection.Out)
			local _object_2 = {}
			local _left_2 = "C0"
			local _arg0_2 = CFrame.Angles(-math.pi / 2, 0, 0)
			_object_2[_left_2] = originalRightC0 * _arg0_2
			local rightArmPunch = TweenService:Create(rightShoulder, _exp_2, _object_2)
			rightArmPunch:Play()
		end)
		-- Phase 4: Follow through (2-3s) - Extended punch pose
		task.delay(2, function()
			print("💥 Fire Fist follow through")
			local _exp_2 = TweenInfo.new(1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut)
			local _object_2 = {}
			local _left_2 = "C0"
			local _arg0_2 = CFrame.Angles(-math.pi / 2, 0, -math.pi / 6)
			_object_2[_left_2] = originalRightC0 * _arg0_2
			local rightArmExtend = TweenService:Create(rightShoulder, _exp_2, _object_2)
			local _exp_3 = TweenInfo.new(1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut)
			local _object_3 = {}
			local _left_3 = "C0"
			local _arg0_3 = CFrame.Angles(math.pi / 6, 0, math.pi / 3)
			_object_3[_left_3] = originalLeftC0 * _arg0_3
			local leftArmBack = TweenService:Create(leftShoulder, _exp_3, _object_3)
			rightArmExtend:Play()
			leftArmBack:Play()
		end)
		-- Phase 5: Return to normal (4s) - Restore all joints
		task.delay(4, function()
			print("🔥 Returning to normal stance")
			local rightArmReturn = TweenService:Create(rightShoulder, TweenInfo.new(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
				C0 = originalRightC0,
			})
			local leftArmReturn = TweenService:Create(leftShoulder, TweenInfo.new(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
				C0 = originalLeftC0,
			})
			rightArmReturn:Play()
			leftArmReturn:Play()
			-- Restore legs
			if rightHip and originalRightHipC0 then
				local rightLegReturn = TweenService:Create(rightHip, TweenInfo.new(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
					C0 = originalRightHipC0,
				})
				rightLegReturn:Play()
			end
		end)
	end
	function FireFistAbility:animateR15Character(upperTorso)
		local rightShoulder = upperTorso:FindFirstChild("RightShoulder")
		local leftShoulder = upperTorso:FindFirstChild("LeftShoulder")
		if not rightShoulder or not leftShoulder then
			return nil
		end
		-- Store original C0 values
		local originalRightC0 = rightShoulder.C0
		local originalLeftC0 = leftShoulder.C0
		-- Similar animation sequence for R15
		print("🔥 R15 Fire Fist animation")
		-- Phase 1: Stance
		local _exp = TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
		local _object = {}
		local _left = "C0"
		local _arg0 = CFrame.Angles(-math.pi / 6, 0, -math.pi / 8)
		_object[_left] = originalRightC0 * _arg0
		local rightArmStance = TweenService:Create(rightShoulder, _exp, _object)
		local _exp_1 = TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
		local _object_1 = {}
		local _left_1 = "C0"
		local _arg0_1 = CFrame.Angles(-math.pi / 6, 0, math.pi / 8)
		_object_1[_left_1] = originalLeftC0 * _arg0_1
		local leftArmStance = TweenService:Create(leftShoulder, _exp_1, _object_1)
		rightArmStance:Play()
		leftArmStance:Play()
		-- Phase 2: Charging (0.5s delay)
		task.delay(0.5, function()
			local _exp_2 = TweenInfo.new(1, Enum.EasingStyle.Back, Enum.EasingDirection.Out)
			local _object_2 = {}
			local _left_2 = "C0"
			local _arg0_2 = CFrame.Angles(-math.pi / 3, 0, -math.pi / 2)
			_object_2[_left_2] = originalRightC0 * _arg0_2
			local rightArmCharge = TweenService:Create(rightShoulder, _exp_2, _object_2)
			local _exp_3 = TweenInfo.new(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
			local _object_3 = {}
			local _left_3 = "C0"
			local _arg0_3 = CFrame.Angles(-math.pi / 4, 0, math.pi / 4)
			_object_3[_left_3] = originalLeftC0 * _arg0_3
			local leftArmBalance = TweenService:Create(leftShoulder, _exp_3, _object_3)
			rightArmCharge:Play()
			leftArmBalance:Play()
		end)
		-- Phase 3: Punch (1.5s delay)
		task.delay(1.5, function()
			local _exp_2 = TweenInfo.new(0.5, Enum.EasingStyle.Back, Enum.EasingDirection.Out)
			local _object_2 = {}
			local _left_2 = "C0"
			local _arg0_2 = CFrame.Angles(-math.pi / 2, 0, 0)
			_object_2[_left_2] = originalRightC0 * _arg0_2
			local rightArmPunch = TweenService:Create(rightShoulder, _exp_2, _object_2)
			rightArmPunch:Play()
		end)
		-- Phase 4: Follow through (2s delay)
		task.delay(2, function()
			local _exp_2 = TweenInfo.new(1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut)
			local _object_2 = {}
			local _left_2 = "C0"
			local _arg0_2 = CFrame.Angles(-math.pi / 2, 0, -math.pi / 6)
			_object_2[_left_2] = originalRightC0 * _arg0_2
			local rightArmExtend = TweenService:Create(rightShoulder, _exp_2, _object_2)
			local _exp_3 = TweenInfo.new(1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut)
			local _object_3 = {}
			local _left_3 = "C0"
			local _arg0_3 = CFrame.Angles(math.pi / 6, 0, math.pi / 3)
			_object_3[_left_3] = originalLeftC0 * _arg0_3
			local leftArmBack = TweenService:Create(leftShoulder, _exp_3, _object_3)
			rightArmExtend:Play()
			leftArmBack:Play()
		end)
		-- Phase 5: Return (4s delay)
		task.delay(4, function()
			local rightArmReturn = TweenService:Create(rightShoulder, TweenInfo.new(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
				C0 = originalRightC0,
			})
			local leftArmReturn = TweenService:Create(leftShoulder, TweenInfo.new(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
				C0 = originalLeftC0,
			})
			rightArmReturn:Play()
			leftArmReturn:Play()
		end)
	end
	function FireFistAbility:createCharacterFireAura(character)
		print("🔥 Creating Ace's fire aura")
		local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
		if not humanoidRootPart then
			return nil
		end
		-- Create fire aura around character
		local fireAura = Instance.new("Part")
		fireAura.Name = "FireAura"
		fireAura.Shape = Enum.PartType.Ball
		fireAura.Size = Vector3.new(10, 10, 10)
		fireAura.Color = Color3.fromRGB(255, 100, 0)
		fireAura.Material = Enum.Material.Neon
		fireAura.Transparency = 0.5
		fireAura.CanCollide = false
		fireAura.Anchored = true
		fireAura.Position = humanoidRootPart.Position
		fireAura.Parent = Workspace
		local _exp = self.fireEffects
		table.insert(_exp, fireAura)
		-- Add intense fire light
		local auraLight = Instance.new("PointLight")
		auraLight.Color = Color3.fromRGB(255, 150, 0)
		auraLight.Brightness = 6
		auraLight.Range = 30
		auraLight.Parent = fireAura
		-- Flickering fire effect
		local flickerTween = TweenService:Create(fireAura, TweenInfo.new(0.2, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true), {
			Size = Vector3.new(12, 12, 12),
			Transparency = 0.3,
		})
		flickerTween:Play()
		-- Follow character
		local followConnection = RunService.Heartbeat:Connect(function()
			if humanoidRootPart.Parent and fireAura.Parent then
				fireAura.Position = humanoidRootPart.Position
			end
		end)
		-- Create fire particles around character
		self:createFireParticles(fireAura)
		-- Cleanup after duration
		task.delay(5, function()
			followConnection:Disconnect()
			flickerTween:Cancel()
			local fadeTween = TweenService:Create(fireAura, TweenInfo.new(1, Enum.EasingStyle.Quad), {
				Transparency = 1,
			})
			fadeTween:Play()
			fadeTween.Completed:Connect(function()
				fireAura:Destroy()
				local index = (table.find(self.fireEffects, fireAura) or 0) - 1
				if index > -1 then
					table.remove(self.fireEffects, index + 1)
				end
			end)
		end)
	end
	function FireFistAbility:createFireParticles(parent)
		-- Create attachment for particles
		local attachment = Instance.new("Attachment")
		attachment.Name = "FireAttachment"
		attachment.Parent = parent
		-- Create fire particle emitter
		local fireParticles = Instance.new("ParticleEmitter")
		fireParticles.Name = "FireParticles"
		fireParticles.Texture = "rbxasset://textures/particles/fire_main.dds"
		fireParticles.Color = ColorSequence.new({ ColorSequenceKeypoint.new(0, Color3.fromRGB(255, 255, 0)), ColorSequenceKeypoint.new(0.5, Color3.fromRGB(255, 100, 0)), ColorSequenceKeypoint.new(1, Color3.fromRGB(255, 0, 0)) })
		fireParticles.Size = NumberSequence.new(1, 3)
		fireParticles.Lifetime = NumberRange.new(0.5, 1.5)
		fireParticles.Rate = 100
		fireParticles.SpreadAngle = Vector2.new(45, 45)
		fireParticles.Speed = NumberRange.new(5, 15)
		fireParticles.Parent = attachment
		-- Stop particles after duration
		task.delay(5, function()
			fireParticles.Enabled = false
			task.delay(2, function()
				if attachment.Parent then
					attachment:Destroy()
				end
			end)
		end)
	end
	function FireFistAbility:createFireFistCharge(rightHand)
		print("👊 Creating Fire Fist charging effect")
		-- Create charging fire sphere in hand
		local chargeSphere = Instance.new("Part")
		chargeSphere.Name = "FireFistCharge"
		chargeSphere.Shape = Enum.PartType.Ball
		chargeSphere.Size = Vector3.new(2, 2, 2)
		chargeSphere.Color = Color3.fromRGB(255, 200, 0)
		chargeSphere.Material = Enum.Material.Neon
		chargeSphere.Transparency = 0.2
		chargeSphere.CanCollide = false
		chargeSphere.Anchored = true
		chargeSphere.Position = rightHand.Position
		chargeSphere.Parent = Workspace
		local _exp = self.fireEffects
		table.insert(_exp, chargeSphere)
		-- Add intense light
		local chargeLight = Instance.new("PointLight")
		chargeLight.Color = Color3.fromRGB(255, 150, 0)
		chargeLight.Brightness = 8
		chargeLight.Range = 20
		chargeLight.Parent = chargeSphere
		-- Growing charge animation
		local chargeTween = TweenService:Create(chargeSphere, TweenInfo.new(1.5, Enum.EasingStyle.Back, Enum.EasingDirection.Out), {
			Size = Vector3.new(6, 6, 6),
			Transparency = 0.1,
		})
		chargeTween:Play()
		-- Follow hand
		local followConnection = RunService.Heartbeat:Connect(function()
			if rightHand.Parent and chargeSphere.Parent then
				chargeSphere.Position = rightHand.Position
			end
		end)
		-- Create charge particles
		self:createFireParticles(chargeSphere)
		-- Remove after charge time
		task.delay(1.5, function()
			followConnection:Disconnect()
			chargeSphere:Destroy()
			local index = (table.find(self.fireEffects, chargeSphere) or 0) - 1
			if index > -1 then
				table.remove(self.fireEffects, index + 1)
			end
		end)
	end
	function FireFistAbility:launchFireFist(humanoidRootPart)
		print("🚀 Launching Fire Fist projectile!")
		local _position = humanoidRootPart.Position
		local _arg0 = humanoidRootPart.CFrame.LookVector * 3
		local startPosition = _position + _arg0
		local direction = humanoidRootPart.CFrame.LookVector
		-- Create massive fire projectile
		local fireFist = Instance.new("Part")
		fireFist.Name = "FireFistProjectile"
		fireFist.Shape = Enum.PartType.Ball
		fireFist.Size = Vector3.new(8, 8, 8)
		fireFist.Color = Color3.fromRGB(255, 100, 0)
		fireFist.Material = Enum.Material.Neon
		fireFist.Transparency = 0.1
		fireFist.CanCollide = false
		fireFist.Anchored = true
		fireFist.Position = startPosition
		fireFist.Parent = Workspace
		local _exp = self.fireEffects
		table.insert(_exp, fireFist)
		-- Add massive fire light
		local projectileLight = Instance.new("PointLight")
		projectileLight.Color = Color3.fromRGB(255, 150, 0)
		projectileLight.Brightness = 10
		projectileLight.Range = 40
		projectileLight.Parent = fireFist
		-- Create fire trail particles
		self:createFireParticles(fireFist)
		-- Projectile movement
		local speed = 80
		local maxDistance = 150
		local travelDistance = 0
		local moveConnection
		moveConnection = RunService.Heartbeat:Connect(function(deltaTime)
			if not fireFist.Parent then
				moveConnection:Disconnect()
				return nil
			end
			local moveDistance = speed * deltaTime
			travelDistance += moveDistance
			if travelDistance >= maxDistance then
				-- Create explosion at max range
				self:createFireExplosion(fireFist.Position)
				moveConnection:Disconnect()
				fireFist:Destroy()
				return nil
			end
			-- Move projectile
			local _position_1 = fireFist.Position
			local _arg0_1 = direction * moveDistance
			fireFist.Position = _position_1 + _arg0_1
			-- Check for collision with objects
			self:checkFireProjectileCollision(fireFist)
		end)
		-- Safety cleanup after 3 seconds
		task.delay(3, function()
			if moveConnection.Connected then
				moveConnection:Disconnect()
			end
			if fireFist.Parent then
				self:createFireExplosion(fireFist.Position)
				fireFist:Destroy()
			end
		end)
	end
	function FireFistAbility:checkFireProjectileCollision(projectile)
		-- Simple collision detection with raycasting
		local raycast = Workspace:Raycast(projectile.Position, projectile.CFrame.LookVector * 5, RaycastParams.new())
		if raycast and raycast.Instance then
			local hitPart = raycast.Instance
			-- Skip if it's our own fire effects or player characters
			local _condition = (string.find(hitPart.Name, "Fire"))
			if not (_condition ~= 0 and _condition == _condition and _condition) then
				_condition = Players:GetPlayerFromCharacter(hitPart.Parent)
			end
			if _condition ~= 0 and _condition == _condition and _condition then
				return nil
			end
			-- Create explosion at hit point
			self:createFireExplosion(raycast.Position)
			-- Remove projectile
			projectile:Destroy()
			local _fireEffects = self.fireEffects
			local _projectile = projectile
			local index = (table.find(_fireEffects, _projectile) or 0) - 1
			if index > -1 then
				table.remove(self.fireEffects, index + 1)
			end
		end
	end
	function FireFistAbility:createFireExplosion(position)
		print("💥 Creating Fire Fist explosion!")
		-- Create explosion sphere
		local explosion = Instance.new("Part")
		explosion.Name = "FireExplosion"
		explosion.Shape = Enum.PartType.Ball
		explosion.Size = Vector3.new(5, 5, 5)
		explosion.Color = Color3.fromRGB(255, 200, 0)
		explosion.Material = Enum.Material.Neon
		explosion.Transparency = 0.2
		explosion.CanCollide = false
		explosion.Anchored = true
		explosion.Position = position
		explosion.Parent = Workspace
		local _exp = self.fireEffects
		table.insert(_exp, explosion)
		-- Add explosion light
		local explosionLight = Instance.new("PointLight")
		explosionLight.Color = Color3.fromRGB(255, 150, 0)
		explosionLight.Brightness = 15
		explosionLight.Range = 50
		explosionLight.Parent = explosion
		-- Explosion expansion
		local explosionTween = TweenService:Create(explosion, TweenInfo.new(0.8, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
			Size = Vector3.new(25, 25, 25),
			Transparency = 0.8,
		})
		explosionTween:Play()
		-- Create explosion particles
		self:createFireParticles(explosion)
		-- Create fire rings
		self:createFireRings(position)
		-- Remove explosion after animation
		explosionTween.Completed:Connect(function()
			explosion:Destroy()
			local index = (table.find(self.fireEffects, explosion) or 0) - 1
			if index > -1 then
				table.remove(self.fireEffects, index + 1)
			end
		end)
	end
	function FireFistAbility:createFireRings(position)
		-- Create expanding fire rings
		for i = 0, 2 do
			task.delay(i * 0.2, function()
				local fireRing = Instance.new("Part")
				fireRing.Name = "FireRing"
				fireRing.Shape = Enum.PartType.Cylinder
				fireRing.Size = Vector3.new(0.5, 8, 8)
				fireRing.Color = Color3.fromRGB(255, 100, 0)
				fireRing.Material = Enum.Material.Neon
				fireRing.Transparency = 0.4
				fireRing.CanCollide = false
				fireRing.Anchored = true
				local _position = position
				local _vector3 = Vector3.new(0, 1, 0)
				fireRing.Position = _position + _vector3
				local _cFrame = fireRing.CFrame
				local _arg0 = CFrame.Angles(0, 0, math.pi / 2)
				fireRing.CFrame = _cFrame * _arg0
				fireRing.Parent = Workspace
				local _exp = self.fireEffects
				table.insert(_exp, fireRing)
				-- Expand ring
				local ringTween = TweenService:Create(fireRing, TweenInfo.new(1.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
					Size = Vector3.new(0.5, 40, 40),
					Transparency = 1,
				})
				ringTween:Play()
				ringTween.Completed:Connect(function()
					fireRing:Destroy()
					local index = (table.find(self.fireEffects, fireRing) or 0) - 1
					if index > -1 then
						table.remove(self.fireEffects, index + 1)
					end
				end)
			end)
		end
	end
	function FireFistAbility:createEnvironmentalFire()
		print("🔥 Creating environmental fire effects")
		-- Create fire atmosphere
		self:createFireAtmosphere()
		-- Create floating fire orbs
		self:createFloatingFireOrbs()
		-- Create ground fire patches
		self:createGroundFirePatches()
	end
	function FireFistAbility:createFireAtmosphere()
		-- Change lighting to warm fire atmosphere
		local originalAmbient = Lighting.Ambient
		local originalOutdoorAmbient = Lighting.OutdoorAmbient
		local originalBrightness = Lighting.Brightness
		local lightingTween = TweenService:Create(Lighting, TweenInfo.new(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
			Ambient = Color3.fromRGB(200, 100, 50),
			OutdoorAmbient = Color3.fromRGB(220, 120, 60),
			Brightness = 2,
		})
		lightingTween:Play()
		-- Restore lighting after duration
		task.delay(4, function()
			local restoreLightingTween = TweenService:Create(Lighting, TweenInfo.new(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
				Ambient = originalAmbient,
				OutdoorAmbient = originalOutdoorAmbient,
				Brightness = originalBrightness,
			})
			restoreLightingTween:Play()
		end)
	end
	function FireFistAbility:createFloatingFireOrbs()
		-- Create floating fire orbs around the area
		for i = 0, 9 do
			task.delay(i * 0.3, function()
				local randomPosition = Vector3.new(math.random(-50, 50), math.random(5, 25), math.random(-50, 50))
				local fireOrb = Instance.new("Part")
				fireOrb.Name = "FloatingFireOrb"
				fireOrb.Shape = Enum.PartType.Ball
				fireOrb.Size = Vector3.new(math.random(2, 4), math.random(2, 4), math.random(2, 4))
				fireOrb.Color = Color3.fromRGB(255, math.random(50, 150), 0)
				fireOrb.Material = Enum.Material.Neon
				fireOrb.Transparency = 0.3
				fireOrb.CanCollide = false
				fireOrb.Anchored = true
				fireOrb.Position = randomPosition
				fireOrb.Parent = Workspace
				local _exp = self.fireEffects
				table.insert(_exp, fireOrb)
				-- Add fire light
				local orbLight = Instance.new("PointLight")
				orbLight.Color = Color3.fromRGB(255, 150, 0)
				orbLight.Brightness = 3
				orbLight.Range = 15
				orbLight.Parent = fireOrb
				-- Floating animation
				local _exp_1 = TweenInfo.new(3, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true)
				local _object = {}
				local _left = "Position"
				local _vector3 = Vector3.new(0, 5, 0)
				_object[_left] = randomPosition + _vector3
				local floatTween = TweenService:Create(fireOrb, _exp_1, _object)
				floatTween:Play()
				-- Flickering effect
				local flickerTween = TweenService:Create(fireOrb, TweenInfo.new(0.3, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true), {
					Transparency = 0.6,
				})
				flickerTween:Play()
				-- Create fire particles
				self:createFireParticles(fireOrb)
			end)
		end
	end
	function FireFistAbility:createGroundFirePatches()
		-- Create fire patches on the ground
		for i = 0, 7 do
			task.delay(i * 0.4, function()
				local randomPosition = Vector3.new(math.random(-40, 40), 0.5, math.random(-40, 40))
				local firePatch = Instance.new("Part")
				firePatch.Name = "GroundFirePatch"
				firePatch.Shape = Enum.PartType.Cylinder
				firePatch.Size = Vector3.new(1, math.random(6, 12), math.random(6, 12))
				firePatch.Color = Color3.fromRGB(255, math.random(80, 120), 0)
				firePatch.Material = Enum.Material.Neon
				firePatch.Transparency = 0.4
				firePatch.CanCollide = false
				firePatch.Anchored = true
				firePatch.Position = randomPosition
				local _cFrame = firePatch.CFrame
				local _arg0 = CFrame.Angles(0, 0, math.pi / 2)
				firePatch.CFrame = _cFrame * _arg0
				firePatch.Parent = Workspace
				local _exp = self.fireEffects
				table.insert(_exp, firePatch)
				-- Add ground fire light
				local patchLight = Instance.new("PointLight")
				patchLight.Color = Color3.fromRGB(255, 150, 0)
				patchLight.Brightness = 4
				patchLight.Range = 20
				patchLight.Parent = firePatch
				-- Flickering fire animation
				local _exp_1 = TweenInfo.new(0.2, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true)
				local _object = {}
				local _left = "Size"
				local _size = firePatch.Size
				local _vector3 = Vector3.new(0.2, 1, 1)
				_object[_left] = _size + _vector3
				_object.Transparency = 0.6
				local patchFlicker = TweenService:Create(firePatch, _exp_1, _object)
				patchFlicker:Play()
				-- Create fire particles
				self:createFireParticles(firePatch)
			end)
		end
	end
	function FireFistAbility:cleanupEffects()
		print("🧹 Cleaning up Fire Fist effects")
		-- Clean up all fire effects
		for _, effect in self.fireEffects do
			if effect.Parent then
				-- Fade out effect
				local fadeTween = TweenService:Create(effect, TweenInfo.new(1, Enum.EasingStyle.Quad), {
					Transparency = 1,
				})
				fadeTween:Play()
				fadeTween.Completed:Connect(function()
					return effect:Destroy()
				end)
			end
		end
		self.fireEffects = {}
		-- Disconnect any remaining connections
		if self.fireConnection then
			self.fireConnection:Disconnect()
			self.fireConnection = nil
		end
	end
end
return {
	FireFistAbility = FireFistAbility,
}
