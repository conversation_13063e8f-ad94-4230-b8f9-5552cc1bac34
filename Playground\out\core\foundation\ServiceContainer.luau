-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local Result = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "types", "Result").Result
local ServiceLifecycle = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "enums", "ServiceLifecycle").ServiceLifecycle
local ServiceError = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "errors", "ServiceError").ServiceError
local ServiceContainer
do
	ServiceContainer = setmetatable({}, {
		__tostring = function()
			return "ServiceContainer"
		end,
	})
	ServiceContainer.__index = ServiceContainer
	function ServiceContainer.new(...)
		local self = setmetatable({}, ServiceContainer)
		return self:constructor(...) or self
	end
	function ServiceContainer:constructor()
		self.services = {}
		self.singletons = {}
		self.isInitialized = false
	end
	function ServiceContainer:getInstance()
		if not ServiceContainer.instance then
			ServiceContainer.instance = ServiceContainer.new()
		end
		return ServiceContainer.instance
	end
	function ServiceContainer:register(name, factory, lifecycle, dependencies)
		if lifecycle == nil then
			lifecycle = ServiceLifecycle.Singleton
		end
		if dependencies == nil then
			dependencies = {}
		end
		local _services = self.services
		local _name = name
		if _services[_name] ~= nil then
			return Result:err(ServiceError.new(`Service '{name}' is already registered`))
		end
		local _services_1 = self.services
		local _name_1 = name
		local _arg1 = {
			name = name,
			factory = factory,
			lifecycle = lifecycle,
			dependencies = dependencies,
			isInitialized = false,
		}
		_services_1[_name_1] = _arg1
		return Result:ok(nil)
	end
	function ServiceContainer:resolve(name)
		local _services = self.services
		local _name = name
		local descriptor = _services[_name]
		if not descriptor then
			return Result:err(ServiceError.new(`Service '{name}' is not registered`))
		end
		if descriptor.lifecycle == ServiceLifecycle.Singleton then
			local _singletons = self.singletons
			local _name_1 = name
			local existing = _singletons[_name_1]
			if existing then
				return Result:ok(existing)
			end
		end
		local dependencyResult = self:resolveDependencies(descriptor.dependencies)
		if dependencyResult:isError() then
			return Result:err(dependencyResult:getError())
		end
		local _exitType, _returns = TS.try(function()
			local service = descriptor.factory()
			if descriptor.lifecycle == ServiceLifecycle.Singleton then
				local _singletons = self.singletons
				local _name_1 = name
				_singletons[_name_1] = service
			end
			return TS.TRY_RETURN, { Result:ok(service) }
		end, function(error)
			return TS.TRY_RETURN, { Result:err(ServiceError.new(`Failed to create service '{name}': {error}`)) }
		end)
		if _exitType then
			return unpack(_returns)
		end
	end
	ServiceContainer.initialize = TS.async(function(self)
		if self.isInitialized then
			return Result:ok(nil)
		end
		local initOrder = self:calculateInitializationOrder()
		if initOrder:isError() then
			return Result:err(initOrder:getError())
		end
		for _, serviceName in initOrder:getValue() do
			local serviceResult = self:resolve(serviceName)
			if serviceResult:isError() then
				return Result:err(serviceResult:getError())
			end
			local service = serviceResult:getValue()
			if service.initialize ~= nil then
				local initResult = TS.await(service:initialize())
				if initResult:isError() then
					return Result:err(ServiceError.new(`Failed to initialize service '{serviceName}': {initResult:getError().message}`))
				end
			end
			local descriptor = self.services[serviceName]
			descriptor.isInitialized = true
		end
		self.isInitialized = true
		return Result:ok(nil)
	end)
	ServiceContainer.shutdown = TS.async(function(self)
		local serviceNames = {}
		for name in self.singletons do
			table.insert(serviceNames, name)
		end
		-- Reverse the array manually
		local shutdownOrder = {}
		for i = #serviceNames - 1, 0, -1 do
			local _arg0 = serviceNames[i + 1]
			table.insert(shutdownOrder, _arg0)
		end
		for _, serviceName in shutdownOrder do
			local service = self.singletons[serviceName]
			if service and service.shutdown then
				local shutdownResult = TS.await(service:shutdown())
				if shutdownResult:isError() then
					print(`Warning: Failed to shutdown service '{serviceName}': {shutdownResult:getError()}`)
				end
			end
		end
		table.clear(self.singletons)
		self.isInitialized = false
		return Result:ok(nil)
	end)
	function ServiceContainer:resolveDependencies(dependencies)
		local resolved = {}
		for _, dep in dependencies do
			local result = self:resolve(dep)
			if result:isError() then
				return Result:err(result:getError())
			end
			local _arg0 = result:getValue()
			table.insert(resolved, _arg0)
		end
		return Result:ok(resolved)
	end
	function ServiceContainer:calculateInitializationOrder()
		local visited = {}
		local visiting = {}
		local order = {}
		local visit
		visit = function(serviceName)
			local _serviceName = serviceName
			if visiting[_serviceName] ~= nil then
				return Result:err(ServiceError.new(`Circular dependency detected involving service '{serviceName}'`))
			end
			local _serviceName_1 = serviceName
			if visited[_serviceName_1] ~= nil then
				return Result:ok(nil)
			end
			local _serviceName_2 = serviceName
			visiting[_serviceName_2] = true
			local _services = self.services
			local _serviceName_3 = serviceName
			local descriptor = _services[_serviceName_3]
			if not descriptor then
				return Result:err(ServiceError.new(`Service '{serviceName}' is not registered`))
			end
			for _, dep in descriptor.dependencies do
				local result = visit(dep)
				if result:isError() then
					return result
				end
			end
			local _serviceName_4 = serviceName
			visiting[_serviceName_4] = nil
			local _serviceName_5 = serviceName
			visited[_serviceName_5] = true
			local _serviceName_6 = serviceName
			table.insert(order, _serviceName_6)
			return Result:ok(nil)
		end
		for serviceName in self.services do
			local result = visit(serviceName)
			if result:isError() then
				return Result:err(result:getError())
			end
		end
		return Result:ok(order)
	end
end
return {
	ServiceContainer = ServiceContainer,
}
