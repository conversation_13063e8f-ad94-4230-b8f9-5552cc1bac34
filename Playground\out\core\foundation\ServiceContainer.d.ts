import { Result } from "./types/Result";
import { ServiceLifecycle } from "./enums/ServiceLifecycle";
import { IService } from "./interfaces/IService";
import { ServiceError } from "./errors/ServiceError";
export declare class ServiceContainer {
    private static instance;
    private services;
    private singletons;
    private isInitialized;
    private constructor();
    static getInstance(): ServiceContainer;
    register<T extends IService>(name: string, factory: () => T, lifecycle?: ServiceLifecycle, dependencies?: string[]): Result<void, ServiceError>;
    resolve<T extends IService>(name: string): Result<T, ServiceError>;
    initialize(): Promise<Result<void, ServiceError>>;
    shutdown(): Promise<Result<void, ServiceError>>;
    private resolveDependencies;
    private calculateInitializationOrder;
}
