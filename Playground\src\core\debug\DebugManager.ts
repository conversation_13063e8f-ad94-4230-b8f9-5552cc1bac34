import { RunService, Players } from "@rbxts/services";
import { DebugRenderer } from "./DebugRenderer";
import { AIDebugger } from "./AIDebugger";
import { PlayerDebugger } from "./PlayerDebugger";
import { PerformanceMonitor } from "./PerformanceMonitor";
import { ResponsiveManager } from "../gui/layout/ResponsiveManager";

export interface DebugConfig {
	showAI: boolean;
	showPlayers: boolean;
	showPerformance: boolean;
	showPositions: boolean;
	showPaths: boolean;
	showVision: boolean;
	showCollisions: boolean;
	showFPS: boolean;
	showMemory: boolean;
	enabled: boolean;
}

export class DebugManager {
	private static instance: DebugManager;
	private config: DebugConfig;
	private renderer: DebugRenderer;
	private aiDebugger: AIDebugger;
	private playerDebugger: PlayerDebugger;
	private performanceMonitor: PerformanceMonitor;
	private heartbeatConnection?: RBXScriptConnection;
	private inputConnection?: RBXScriptConnection;
	private responsiveUnsubscribe?: () => void;
	private isInitialized = false;

	private constructor() {
		this.config = {
			showAI: true,
			showPlayers: true,
			showPerformance: true,
			showPositions: true,
			showPaths: true,
			showVision: true,
			showCollisions: false,
			showFPS: true,
			showMemory: true,
			enabled: false // Start disabled
		};

		this.renderer = new DebugRenderer();
		this.aiDebugger = new AIDebugger(this.renderer);
		this.playerDebugger = new PlayerDebugger(this.renderer);
		this.performanceMonitor = new PerformanceMonitor(this.renderer);
	}

	public static getInstance(): DebugManager {
		if (!DebugManager.instance) {
			DebugManager.instance = new DebugManager();
		}
		return DebugManager.instance;
	}

	public initialize(): void {
		if (this.isInitialized) return;

		print(`🔧 [${tick()}] DebugManager: Starting initialization`);
		this.setupInputHandling();
		this.setupResponsiveLayout();
		this.startDebugLoop();
		this.isInitialized = true;

		print(`🔧 [${tick()}] Debug Manager initialized! Press F3 to toggle debug overlay`);
	}

	public toggle(): void {
		this.config.enabled = !this.config.enabled;

		if (this.config.enabled) {
			print(`🔍 [${tick()}] Debug overlay enabled - creating debug panels`);
			this.renderer.show();
		} else {
			print(`🔍 [${tick()}] Debug overlay disabled`);
			this.renderer.hide();
		}
	}

	public setConfig(newConfig: Partial<DebugConfig>): void {
		this.config = { ...this.config, ...newConfig };
	}

	public getConfig(): DebugConfig {
		return { ...this.config };
	}

	public isEnabled(): boolean {
		return this.config.enabled;
	}

	// Toggle specific debug features
	public toggleAI(): void {
		this.config.showAI = !this.config.showAI;
		print(`🤖 AI Debug: ${this.config.showAI ? "ON" : "OFF"}`);
	}

	public togglePlayers(): void {
		this.config.showPlayers = !this.config.showPlayers;
		print(`👤 Player Debug: ${this.config.showPlayers ? "ON" : "OFF"}`);
	}

	public togglePerformance(): void {
		this.config.showPerformance = !this.config.showPerformance;
		print(`⚡ Performance Debug: ${this.config.showPerformance ? "ON" : "OFF"}`);
	}

	public togglePaths(): void {
		this.config.showPaths = !this.config.showPaths;
		print(`🛤️ Path Debug: ${this.config.showPaths ? "ON" : "OFF"}`);
	}

	public toggleVision(): void {
		this.config.showVision = !this.config.showVision;
		print(`👁️ Vision Debug: ${this.config.showVision ? "ON" : "OFF"}`);
	}

	private setupInputHandling(): void {
		// Keyboard shortcuts disabled - use the debug panel buttons instead
		// Only setup input handling on client
		if (!Players.LocalPlayer) return;

		// No keyboard shortcuts - debug panel controls everything
		this.inputConnection = undefined;
	}

	private setupResponsiveLayout(): void {
		const responsiveManager = ResponsiveManager.getInstance();

		// Listen for screen size changes and recreate debug panels
		this.responsiveUnsubscribe = responsiveManager.onScreenSizeChange(() => {
			if (this.config.enabled) {
				// Recreate debug panels with new responsive positioning
				this.recreateDebugPanels();
			}
		});
	}

	private recreateDebugPanels(): void {
		// Recreate all debug components to apply new responsive positioning
		this.performanceMonitor = new PerformanceMonitor(this.renderer);
		this.aiDebugger = new AIDebugger(this.renderer);
		this.playerDebugger = new PlayerDebugger(this.renderer);

		print("🔧 Debug panels recreated for new screen size");
	}

	private startDebugLoop(): void {
		this.heartbeatConnection = RunService.Heartbeat.Connect(() => {
			if (!this.config.enabled) return;

			const deltaTime = RunService.Heartbeat.Wait()[0];

			// Update performance monitor
			if (this.config.showPerformance) {
				this.performanceMonitor.update(deltaTime);
			}

			// Update AI debugging
			if (this.config.showAI) {
				this.aiDebugger.update(this.config);
			}

			// Update player debugging
			if (this.config.showPlayers) {
				this.playerDebugger.update(this.config);
			}

			// Render all debug information
			this.renderer.render();
		});
	}

	public cleanup(): void {
		if (this.heartbeatConnection) {
			this.heartbeatConnection.Disconnect();
		}

		if (this.inputConnection) {
			this.inputConnection.Disconnect();
		}

		if (this.responsiveUnsubscribe) {
			this.responsiveUnsubscribe();
		}

		this.renderer.cleanup();
		this.aiDebugger.cleanup();
		this.playerDebugger.cleanup();
		this.performanceMonitor.cleanup();

		this.isInitialized = false;
	}

	// Utility methods for external debugging
	public drawLine(from: Vector3, to: Vector3, color = Color3.fromRGB(255, 255, 255), duration = 0.1): void {
		if (this.config.enabled) {
			this.renderer.drawLine(from, to, color, duration);
		}
	}

	public drawSphere(position: Vector3, radius = 1, color = Color3.fromRGB(255, 255, 255), duration = 0.1): void {
		if (this.config.enabled) {
			this.renderer.drawSphere(position, radius, color, duration);
		}
	}

	public drawText(position: Vector3, text: string, color = Color3.fromRGB(255, 255, 255), duration = 0.1): void {
		if (this.config.enabled) {
			this.renderer.drawText(position, text, color, duration);
		}
	}

	public logDebug(category: string, message: string, data?: unknown): void {
		if (this.config.enabled) {
			const timestamp = os.date("%H:%M:%S");
			const logMessage = `[${timestamp}] [${category}] ${message}`;
			
			if (data) {
				print(logMessage, data);
			} else {
				print(logMessage);
			}
		}
	}
}
