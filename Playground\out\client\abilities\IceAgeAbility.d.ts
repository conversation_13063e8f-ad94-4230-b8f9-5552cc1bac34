import { AbilityBase } from "./AbilityBase";
export declare class IceAgeAbility extends AbilityBase {
    private iceEffects;
    private iceConnection?;
    private cooldownEndTime;
    private isActive;
    private frozenObjects;
    private originalLighting;
    constructor();
    isOnCooldown(): boolean;
    private startCooldown;
    activate(): void;
    private createIceAgeAnimation;
    private createCharacterAnimation;
    private animateR6Character;
    private animateR15Character;
    private createCharacterIceAura;
    private createExpandingIceWave;
    private createIceEnvironment;
    private createIceAtmosphere;
    private createFloatingIceCrystals;
    private createIceSpears;
    private cleanupEffects;
    private freezeAllObjects;
    private getAllPartsInWorkspace;
    private shouldSkipPart;
    private applyIceTransformation;
    private createObjectIceShell;
    private addFrostParticles;
    private restoreAllFrozenObjects;
}
