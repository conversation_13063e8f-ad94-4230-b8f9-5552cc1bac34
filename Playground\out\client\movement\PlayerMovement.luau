-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local _services = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services")
local Players = _services.Players
local RunService = _services.RunService
local TweenService = _services.TweenService
local UserInputService = _services.UserInputService
local Workspace = _services.Workspace
local PlayerMovement
do
	PlayerMovement = setmetatable({}, {
		__tostring = function()
			return "PlayerMovement"
		end,
	})
	PlayerMovement.__index = PlayerMovement
	function PlayerMovement.new(...)
		local self = setmetatable({}, PlayerMovement)
		return self:constructor(...) or self
	end
	function PlayerMovement:constructor()
		self.player = Players.LocalPlayer
		self.connections = {}
		self:setup<PERSON>haracter()
		-- Listen for character respawning
		self.player.CharacterAdded:Connect(function(character)
			self:setup<PERSON>haracter()
		end)
	end
	function PlayerMovement:setup<PERSON>haracter()
		self.character = self.player.Character or (self.player.CharacterAdded:Wait())
		self.humanoid = self.character:WaitForChild("Humanoid")
		self.humanoidRootPart = self.character:WaitForChild("HumanoidRootPart")
		print("✅ PlayerMovement setup complete")
	end
	function PlayerMovement:setWalkSpeed(speed)
		if self.humanoid then
			self.humanoid.WalkSpeed = speed
			print(`🚶 Walk speed set to: {speed}`)
		end
	end
	function PlayerMovement:jump()
		if self.humanoid then
			self.humanoid.Jump = true
			print("🦘 Player jumped!")
		end
	end
	function PlayerMovement:moveTo(position)
		if self.humanoid then
			self.humanoid:MoveTo(position)
			print(`🎯 Moving to position: {position}`)
		end
	end
	function PlayerMovement:dash(direction, power, duration)
		if power == nil then
			power = 50
		end
		if duration == nil then
			duration = 0.3
		end
		if not self.humanoidRootPart then
			return nil
		end
		-- Create BodyVelocity for the dash
		local bodyVelocity = Instance.new("BodyVelocity")
		bodyVelocity.MaxForce = Vector3.new(4000, 0, 4000)
		local _unit = direction.Unit
		local _power = power
		bodyVelocity.Velocity = _unit * _power
		bodyVelocity.Parent = self.humanoidRootPart
		-- Remove the BodyVelocity after duration
		task.delay(duration, function()
			if bodyVelocity.Parent then
				bodyVelocity:Destroy()
			end
		end)
		print(`💨 Dashed in direction: {direction} with power: {power}`)
	end
	function PlayerMovement:launch(upwardForce, forwardForce)
		if upwardForce == nil then
			upwardForce = 50
		end
		if forwardForce == nil then
			forwardForce = 0
		end
		if not self.humanoidRootPart then
			return nil
		end
		local lookDirection = self.humanoidRootPart.CFrame.LookVector
		local launchDirection = Vector3.new(lookDirection.X * forwardForce, upwardForce, lookDirection.Z * forwardForce)
		-- Use AssemblyLinearVelocity for modern approach
		self.humanoidRootPart.AssemblyLinearVelocity = launchDirection
		print(`🚀 Launched player with force: {launchDirection}`)
	end
	function PlayerMovement:smoothMoveTo(targetPosition, duration)
		if duration == nil then
			duration = 2
		end
		if not self.humanoidRootPart then
			return nil
		end
		local tween = TweenService:Create(self.humanoidRootPart, TweenInfo.new(duration, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut), {
			CFrame = CFrame.new(targetPosition),
		})
		tween:Play()
		print(`🌊 Smoothly moving to: {targetPosition} over {duration} seconds`)
	end
	function PlayerMovement:teleport(position)
		if not self.humanoidRootPart then
			return nil
		end
		self.humanoidRootPart.CFrame = CFrame.new(position)
		print(`⚡ Teleported to: {position}`)
	end
	function PlayerMovement:setMovementEnabled(enabled)
		if self.humanoid then
			self.humanoid.PlatformStand = not enabled
			print(`🔒 Movement {if enabled then "enabled" else "disabled"}`)
		end
	end
	function PlayerMovement:getVelocity()
		if self.humanoidRootPart then
			return self.humanoidRootPart.AssemblyLinearVelocity
		end
		return Vector3.new(0, 0, 0)
	end
	function PlayerMovement:isMoving()
		local velocity = self:getVelocity()
		local horizontalSpeed = math.sqrt(velocity.X * velocity.X + velocity.Z * velocity.Z)
		return horizontalSpeed > 1
	end
	function PlayerMovement:setupCustomMovement(speed)
		if speed == nil then
			speed = 16
		end
		-- Disable default movement
		if self.humanoid then
			self.humanoid.PlatformStand = true
		end
		-- Custom movement with WASD
		local connection = RunService.Heartbeat:Connect(function()
			if not self.humanoidRootPart then
				return nil
			end
			local camera = Workspace.CurrentCamera
			if not camera then
				return nil
			end
			local moveVector = Vector3.new(0, 0, 0)
			-- Check input
			if UserInputService:IsKeyDown(Enum.KeyCode.W) then
				local _moveVector = moveVector
				local _lookVector = camera.CFrame.LookVector
				moveVector = _moveVector + _lookVector
			end
			if UserInputService:IsKeyDown(Enum.KeyCode.S) then
				local _moveVector = moveVector
				local _lookVector = camera.CFrame.LookVector
				moveVector = _moveVector - _lookVector
			end
			if UserInputService:IsKeyDown(Enum.KeyCode.A) then
				local _moveVector = moveVector
				local _rightVector = camera.CFrame.RightVector
				moveVector = _moveVector - _rightVector
			end
			if UserInputService:IsKeyDown(Enum.KeyCode.D) then
				local _moveVector = moveVector
				local _rightVector = camera.CFrame.RightVector
				moveVector = _moveVector + _rightVector
			end
			-- Apply movement
			if moveVector.Magnitude > 0 then
				local normalizedMove = moveVector.Unit
				self.humanoidRootPart.AssemblyLinearVelocity = Vector3.new(normalizedMove.X * speed, self.humanoidRootPart.AssemblyLinearVelocity.Y, normalizedMove.Z * speed)
			else
				-- Stop horizontal movement when no input
				self.humanoidRootPart.AssemblyLinearVelocity = Vector3.new(0, self.humanoidRootPart.AssemblyLinearVelocity.Y, 0)
			end
		end)
		local _exp = self.connections
		table.insert(_exp, connection)
		print("🎮 Custom WASD movement enabled")
	end
	function PlayerMovement:restoreDefaultMovement()
		if self.humanoid then
			self.humanoid.PlatformStand = false
		end
		-- Disconnect custom movement connections
		for _, connection in self.connections do
			connection:Disconnect()
		end
		self.connections = {}
		print("🔄 Default movement restored")
	end
	function PlayerMovement:destroy()
		self:restoreDefaultMovement()
	end
end
return {
	PlayerMovement = PlayerMovement,
}
