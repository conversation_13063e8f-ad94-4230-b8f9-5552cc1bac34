-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local TweenService = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services").TweenService
local AnimationBuilder
do
	AnimationBuilder = setmetatable({}, {
		__tostring = function()
			return "AnimationBuilder"
		end,
	})
	AnimationBuilder.__index = AnimationBuilder
	function AnimationBuilder.new(...)
		local self = setmetatable({}, AnimationBuilder)
		return self:constructor(...) or self
	end
	function AnimationBuilder:constructor(joint)
		self.delayTime = 0
		self.joint = joint
		self.tweenInfo = TweenInfo.new(0.3, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
		self.targetCFrame = joint.C0
	end
	function AnimationBuilder:forJoint(joint)
		return AnimationBuilder.new(joint)
	end
	function AnimationBuilder:to(relativeCFrame)
		local _c0 = self.joint.C0
		local _relativeCFrame = relativeCFrame
		self.targetCFrame = _c0 * _relativeCFrame
		return self
	end
	function AnimationBuilder:toAbsolute(absoluteCFrame)
		self.targetCFrame = absoluteCFrame
		return self
	end
	function AnimationBuilder:duration(time)
		self.tweenInfo = TweenInfo.new(time, self.tweenInfo.EasingStyle, self.tweenInfo.EasingDirection)
		return self
	end
	function AnimationBuilder:easing(style, direction)
		self.tweenInfo = TweenInfo.new(self.tweenInfo.Time, style, direction)
		return self
	end
	function AnimationBuilder:delay(delay)
		self.delayTime = delay
		return self
	end
	function AnimationBuilder:onComplete(callback)
		self.completionCallback = callback
		return self
	end
	function AnimationBuilder:play()
		local tween = TweenService:Create(self.joint, self.tweenInfo, {
			C0 = self.targetCFrame,
		})
		if self.delayTime > 0 then
			task.delay(self.delayTime, function()
				return tween:Play()
			end)
		else
			tween:Play()
		end
		if self.completionCallback then
			tween.Completed:Connect(self.completionCallback)
		end
		return tween
	end
end
return {
	AnimationBuilder = AnimationBuilder,
}
