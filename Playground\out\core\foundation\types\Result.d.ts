export declare class Result<T, E> {
    private readonly _value?;
    private readonly _error?;
    private readonly _isSuccess;
    private constructor();
    static ok<T, E>(value: T): Result<T, E>;
    static err<T, E>(errorValue: E): Result<T, E>;
    isOk(): boolean;
    isError(): boolean;
    getValue(): T;
    getError(): E;
    map<U>(fn: (value: T) => U): Result<U, E>;
    mapError<F>(fn: (error: E) => F): Result<T, F>;
    flatMap<U>(fn: (value: T) => Result<U, E>): Result<U, E>;
    unwrapOr(defaultValue: T): T;
    unwrapOrElse(fn: (error: E) => T): T;
    match<U>(onOk: (value: T) => U, onError: (error: E) => U): U;
}
