-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
-- Shared networking events for ability system
local _services = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services")
local ReplicatedStorage = _services.ReplicatedStorage
local RunService = _services.RunService
-- Function to safely get RemoteEvents (wait for them to be created)
local function getRemoteEvents()
	if RunService:IsServer() then
		-- Server should create the events, so we can find them directly
		return ReplicatedStorage:FindFirstChild("RemoteEvents")
	else
		-- Client waits for server to create them
		return ReplicatedStorage:WaitForChild("RemoteEvents")
	end
end
-- Lazy initialization of RemoteEvents
local remoteEventsFolder
local castAbilityEvent
local abilityCastConfirmEvent
local abilityEffectEvent
local requestAbilitiesEvent
local abilitiesDataEvent
local function initializeEvents()
	if not remoteEventsFolder then
		remoteEventsFolder = getRemoteEvents()
	end
	if not castAbilityEvent then
		castAbilityEvent = remoteEventsFolder:WaitForChild("CastAbility")
	end
	if not abilityCastConfirmEvent then
		abilityCastConfirmEvent = remoteEventsFolder:WaitForChild("AbilityCastConfirm")
	end
	if not abilityEffectEvent then
		abilityEffectEvent = remoteEventsFolder:WaitForChild("AbilityEffect")
	end
	if not requestAbilitiesEvent then
		requestAbilitiesEvent = remoteEventsFolder:WaitForChild("RequestAbilities")
	end
	if not abilitiesDataEvent then
		abilitiesDataEvent = remoteEventsFolder:WaitForChild("AbilitiesData")
	end
end
-- Getter functions that initialize events on first access
local function getCastAbilityEvent()
	initializeEvents()
	return castAbilityEvent
end
local function getAbilityCastConfirmEvent()
	initializeEvents()
	return abilityCastConfirmEvent
end
local function getAbilityEffectEvent()
	initializeEvents()
	return abilityEffectEvent
end
local function getRequestAbilitiesEvent()
	initializeEvents()
	return requestAbilitiesEvent
end
local function getAbilitiesDataEvent()
	initializeEvents()
	return abilitiesDataEvent
end
-- Type-safe event firing functions
local AbilityNetworking = {}
do
	local _container = AbilityNetworking
	-- Client-side functions
	local function requestCastAbility(request)
		getCastAbilityEvent():FireServer(request)
	end
	_container.requestCastAbility = requestCastAbility
	local function requestAbilitiesData()
		getRequestAbilitiesEvent():FireServer()
	end
	_container.requestAbilitiesData = requestAbilitiesData
	-- Server-side functions (will be used in server code)
	local function confirmAbilityCast(player, response)
		getAbilityCastConfirmEvent():FireClient(player, response)
	end
	_container.confirmAbilityCast = confirmAbilityCast
	local function replicateAbilityEffect(effectData)
		getAbilityEffectEvent():FireAllClients(effectData)
	end
	_container.replicateAbilityEffect = replicateAbilityEffect
	local function sendAbilitiesData(player, abilities)
		getAbilitiesDataEvent():FireClient(player, abilities)
	end
	_container.sendAbilitiesData = sendAbilitiesData
end
return {
	getCastAbilityEvent = getCastAbilityEvent,
	getAbilityCastConfirmEvent = getAbilityCastConfirmEvent,
	getAbilityEffectEvent = getAbilityEffectEvent,
	getRequestAbilitiesEvent = getRequestAbilitiesEvent,
	getAbilitiesDataEvent = getAbilitiesDataEvent,
	AbilityNetworking = AbilityNetworking,
}
