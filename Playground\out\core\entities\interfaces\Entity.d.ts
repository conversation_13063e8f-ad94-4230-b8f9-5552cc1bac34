import { EntityType } from "../enums/EntityType";
export interface Entity {
    id: string;
    type: EntityType;
    instance: Instance;
    position: Vector3;
    isActive: boolean;
    createdAt: number;
    data?: Record<string, unknown>;
}
export interface EntitySpawnOptions {
    type: EntityType;
    position: Vector3;
    rotation?: Vector3;
    data?: Record<string, unknown>;
    parent?: Instance;
    lifetime?: number;
}
