-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local AbilityBase = TS.import(script, script.Parent, "AbilityBase").AbilityBase
local _services = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services")
local TweenService = _services.TweenService
local RunService = _services.RunService
local Workspace = _services.Workspace
local Players = _services.Players
local Lighting = _services.Lighting
local IceAgeAbility
do
	local super = AbilityBase
	IceAgeAbility = setmetatable({}, {
		__tostring = function()
			return "IceAgeAbility"
		end,
		__index = super,
	})
	IceAgeAbility.__index = IceAgeAbility
	function IceAgeAbility.new(...)
		local self = setmetatable({}, IceAgeAbility)
		return self:constructor(...) or self
	end
	function IceAgeAbility:constructor()
		super.constructor(self, "ICE_AGE", 30)
		self.iceEffects = {}
		self.cooldownEndTime = 0
		self.isActive = false
		self.frozenObjects = {}
		-- Store original lighting values
		self.originalLighting = {
			Ambient = Lighting.Ambient,
			OutdoorAmbient = Lighting.OutdoorAmbient,
			Brightness = Lighting.Brightness,
			ColorShift_Top = Lighting.ColorShift_Top,
		}
	end
	function IceAgeAbility:isOnCooldown()
		return tick() < self.cooldownEndTime
	end
	function IceAgeAbility:startCooldown()
		self.cooldownEndTime = tick() + self:getCooldownTime()
	end
	function IceAgeAbility:activate()
		if self:isOnCooldown() or self.isActive then
			return nil
		end
		local player = Players.LocalPlayer
		local character = player.Character
		if not character then
			return nil
		end
		local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
		if not humanoidRootPart then
			return nil
		end
		print("❄️ Activating Aokiji's Ice Age - Freezing the world!")
		self.isActive = true
		-- Phase 1: Character ice preparation animation
		self:createIceAgeAnimation(character)
		-- Phase 2: Character ice aura
		self:createCharacterIceAura(character)
		-- Phase 2: Create expanding ice wave
		self:createExpandingIceWave(humanoidRootPart.Position)
		-- Phase 3: Transform environment to ice
		self:createIceEnvironment()
		-- Phase 4: Create ice atmosphere
		self:createIceAtmosphere()
		-- Phase 5: Create floating ice crystals
		self:createFloatingIceCrystals()
		-- Phase 6: Create ice spears
		self:createIceSpears()
		-- Phase 7: Freeze all objects on the map
		self:freezeAllObjects()
		-- Duration: 12 seconds
		task.delay(12, function()
			self:cleanupEffects()
			self.isActive = false
		end)
		self:startCooldown()
	end
	function IceAgeAbility:createIceAgeAnimation(character)
		print("🧊 Creating Aokiji's Ice Age animation")
		local humanoid = character:FindFirstChild("Humanoid")
		if not humanoid then
			return nil
		end
		-- Create proper Motor6D animation like QuakeAbility
		self:createCharacterAnimation(character)
	end
	function IceAgeAbility:createCharacterAnimation(character)
		local torso = character:FindFirstChild("Torso")
		local upperTorso = character:FindFirstChild("UpperTorso")
		if torso then
			-- R6 character
			self:animateR6Character(torso)
		elseif upperTorso then
			-- R15 character
			self:animateR15Character(upperTorso, character)
		end
	end
	function IceAgeAbility:animateR6Character(torso)
		local rightShoulder = torso:FindFirstChild("Right Shoulder")
		local leftShoulder = torso:FindFirstChild("Left Shoulder")
		local rightHip = torso:FindFirstChild("Right Hip")
		local leftHip = torso:FindFirstChild("Left Hip")
		local neck = torso:FindFirstChild("Neck")
		if not rightShoulder or not leftShoulder then
			return nil
		end
		-- Store original C0 values
		local originalRightC0 = rightShoulder.C0
		local originalLeftC0 = leftShoulder.C0
		local originalRightHipC0 = if rightHip then rightHip.C0 else nil
		local originalLeftHipC0 = if leftHip then leftHip.C0 else nil
		local originalNeckC0 = if neck then neck.C0 else nil
		-- Phase 1: Ice preparation stance (0-1s) - Arms spread wide
		print("❄️ Ice preparation stance")
		local _exp = TweenInfo.new(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
		local _object = {}
		local _left = "C0"
		local _arg0 = CFrame.Angles(-math.pi / 4, 0, -math.pi / 2)
		_object[_left] = originalRightC0 * _arg0
		local rightArmPrep = TweenService:Create(rightShoulder, _exp, _object)
		local _exp_1 = TweenInfo.new(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
		local _object_1 = {}
		local _left_1 = "C0"
		local _arg0_1 = CFrame.Angles(-math.pi / 4, 0, math.pi / 2)
		_object_1[_left_1] = originalLeftC0 * _arg0_1
		local leftArmPrep = TweenService:Create(leftShoulder, _exp_1, _object_1)
		rightArmPrep:Play()
		leftArmPrep:Play()
		-- Phase 2: Ice channeling (1-2s) - Arms down, channeling cold
		task.delay(1, function()
			print("🧊 Ice channeling pose")
			local _exp_2 = TweenInfo.new(1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut)
			local _object_2 = {}
			local _left_2 = "C0"
			local _arg0_2 = CFrame.Angles(math.pi / 3, 0, -math.pi / 6)
			_object_2[_left_2] = originalRightC0 * _arg0_2
			local rightArmChannel = TweenService:Create(rightShoulder, _exp_2, _object_2)
			local _exp_3 = TweenInfo.new(1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut)
			local _object_3 = {}
			local _left_3 = "C0"
			local _arg0_3 = CFrame.Angles(math.pi / 3, 0, math.pi / 6)
			_object_3[_left_3] = originalLeftC0 * _arg0_3
			local leftArmChannel = TweenService:Create(leftShoulder, _exp_3, _object_3)
			rightArmChannel:Play()
			leftArmChannel:Play()
		end)
		-- Phase 3: Ice release (2-3s) - Dramatic arm sweep
		task.delay(2, function()
			print("❄️ Ice release gesture!")
			local _exp_2 = TweenInfo.new(1, Enum.EasingStyle.Back, Enum.EasingDirection.Out)
			local _object_2 = {}
			local _left_2 = "C0"
			local _arg0_2 = CFrame.Angles(-math.pi / 2, 0, -math.pi / 3)
			_object_2[_left_2] = originalRightC0 * _arg0_2
			local rightArmRelease = TweenService:Create(rightShoulder, _exp_2, _object_2)
			local _exp_3 = TweenInfo.new(1, Enum.EasingStyle.Back, Enum.EasingDirection.Out)
			local _object_3 = {}
			local _left_3 = "C0"
			local _arg0_3 = CFrame.Angles(-math.pi / 2, 0, math.pi / 3)
			_object_3[_left_3] = originalLeftC0 * _arg0_3
			local leftArmRelease = TweenService:Create(leftShoulder, _exp_3, _object_3)
			rightArmRelease:Play()
			leftArmRelease:Play()
			-- Add leg stance for power
			if rightHip and leftHip and originalRightHipC0 and originalLeftHipC0 then
				local _exp_4 = TweenInfo.new(1, Enum.EasingStyle.Back, Enum.EasingDirection.Out)
				local _object_4 = {}
				local _left_4 = "C0"
				local _arg0_4 = CFrame.Angles(0, 0, math.pi / 12)
				_object_4[_left_4] = originalRightHipC0 * _arg0_4
				local rightLegStance = TweenService:Create(rightHip, _exp_4, _object_4)
				local _exp_5 = TweenInfo.new(1, Enum.EasingStyle.Back, Enum.EasingDirection.Out)
				local _object_5 = {}
				local _left_5 = "C0"
				local _arg0_5 = CFrame.Angles(0, 0, -math.pi / 12)
				_object_5[_left_5] = originalLeftHipC0 * _arg0_5
				local leftLegStance = TweenService:Create(leftHip, _exp_5, _object_5)
				rightLegStance:Play()
				leftLegStance:Play()
			end
		end)
		-- Phase 4: Return to normal (10s) - Restore all joints
		task.delay(10, function()
			print("🧊 Returning to normal stance")
			local rightArmReturn = TweenService:Create(rightShoulder, TweenInfo.new(2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
				C0 = originalRightC0,
			})
			local leftArmReturn = TweenService:Create(leftShoulder, TweenInfo.new(2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
				C0 = originalLeftC0,
			})
			rightArmReturn:Play()
			leftArmReturn:Play()
			-- Restore legs
			if rightHip and leftHip and originalRightHipC0 and originalLeftHipC0 then
				local rightLegReturn = TweenService:Create(rightHip, TweenInfo.new(2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
					C0 = originalRightHipC0,
				})
				local leftLegReturn = TweenService:Create(leftHip, TweenInfo.new(2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
					C0 = originalLeftHipC0,
				})
				rightLegReturn:Play()
				leftLegReturn:Play()
			end
		end)
	end
	function IceAgeAbility:animateR15Character(upperTorso, character)
		local rightShoulder = upperTorso:FindFirstChild("RightShoulder")
		local leftShoulder = upperTorso:FindFirstChild("LeftShoulder")
		local lowerTorso = character:FindFirstChild("LowerTorso")
		if not rightShoulder or not leftShoulder then
			return nil
		end
		-- Store original C0 values
		local originalRightC0 = rightShoulder.C0
		local originalLeftC0 = leftShoulder.C0
		-- Similar animation sequence for R15
		print("❄️ R15 Ice Age animation")
		-- Phase 1: Preparation
		local _exp = TweenInfo.new(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
		local _object = {}
		local _left = "C0"
		local _arg0 = CFrame.Angles(-math.pi / 4, 0, -math.pi / 2)
		_object[_left] = originalRightC0 * _arg0
		local rightArmPrep = TweenService:Create(rightShoulder, _exp, _object)
		local _exp_1 = TweenInfo.new(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
		local _object_1 = {}
		local _left_1 = "C0"
		local _arg0_1 = CFrame.Angles(-math.pi / 4, 0, math.pi / 2)
		_object_1[_left_1] = originalLeftC0 * _arg0_1
		local leftArmPrep = TweenService:Create(leftShoulder, _exp_1, _object_1)
		rightArmPrep:Play()
		leftArmPrep:Play()
		-- Phase 2: Channeling (1s delay)
		task.delay(1, function()
			local _exp_2 = TweenInfo.new(1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut)
			local _object_2 = {}
			local _left_2 = "C0"
			local _arg0_2 = CFrame.Angles(math.pi / 3, 0, -math.pi / 6)
			_object_2[_left_2] = originalRightC0 * _arg0_2
			local rightArmChannel = TweenService:Create(rightShoulder, _exp_2, _object_2)
			local _exp_3 = TweenInfo.new(1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut)
			local _object_3 = {}
			local _left_3 = "C0"
			local _arg0_3 = CFrame.Angles(math.pi / 3, 0, math.pi / 6)
			_object_3[_left_3] = originalLeftC0 * _arg0_3
			local leftArmChannel = TweenService:Create(leftShoulder, _exp_3, _object_3)
			rightArmChannel:Play()
			leftArmChannel:Play()
		end)
		-- Phase 3: Release (2s delay)
		task.delay(2, function()
			local _exp_2 = TweenInfo.new(1, Enum.EasingStyle.Back, Enum.EasingDirection.Out)
			local _object_2 = {}
			local _left_2 = "C0"
			local _arg0_2 = CFrame.Angles(-math.pi / 2, 0, -math.pi / 3)
			_object_2[_left_2] = originalRightC0 * _arg0_2
			local rightArmRelease = TweenService:Create(rightShoulder, _exp_2, _object_2)
			local _exp_3 = TweenInfo.new(1, Enum.EasingStyle.Back, Enum.EasingDirection.Out)
			local _object_3 = {}
			local _left_3 = "C0"
			local _arg0_3 = CFrame.Angles(-math.pi / 2, 0, math.pi / 3)
			_object_3[_left_3] = originalLeftC0 * _arg0_3
			local leftArmRelease = TweenService:Create(leftShoulder, _exp_3, _object_3)
			rightArmRelease:Play()
			leftArmRelease:Play()
		end)
		-- Phase 4: Return (10s delay)
		task.delay(10, function()
			local rightArmReturn = TweenService:Create(rightShoulder, TweenInfo.new(2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
				C0 = originalRightC0,
			})
			local leftArmReturn = TweenService:Create(leftShoulder, TweenInfo.new(2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
				C0 = originalLeftC0,
			})
			rightArmReturn:Play()
			leftArmReturn:Play()
		end)
	end
	function IceAgeAbility:createCharacterIceAura(character)
		print("❄️ Creating Aokiji's ice aura")
		local humanoidRootPart = character:FindFirstChild("HumanoidRootPart")
		if not humanoidRootPart then
			return nil
		end
		-- Create ice aura around character
		local iceAura = Instance.new("Part")
		iceAura.Name = "IceAura"
		iceAura.Shape = Enum.PartType.Ball
		iceAura.Size = Vector3.new(12, 12, 12)
		iceAura.Color = Color3.fromRGB(150, 200, 255)
		iceAura.Material = Enum.Material.Ice
		iceAura.Transparency = 0.6
		iceAura.CanCollide = false
		iceAura.Anchored = true
		iceAura.Position = humanoidRootPart.Position
		iceAura.Parent = Workspace
		local _exp = self.iceEffects
		table.insert(_exp, iceAura)
		-- Add cold blue light
		local auraLight = Instance.new("PointLight")
		auraLight.Color = Color3.fromRGB(150, 200, 255)
		auraLight.Brightness = 4
		auraLight.Range = 25
		auraLight.Parent = iceAura
		-- Pulsing effect
		local pulseTween = TweenService:Create(iceAura, TweenInfo.new(1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true), {
			Size = Vector3.new(15, 15, 15),
			Transparency = 0.4,
		})
		pulseTween:Play()
		-- Follow character
		local followConnection = RunService.Heartbeat:Connect(function()
			if humanoidRootPart.Parent and iceAura.Parent then
				iceAura.Position = humanoidRootPart.Position
			end
		end)
		-- Cleanup after duration
		task.delay(12, function()
			followConnection:Disconnect()
			pulseTween:Cancel()
			local fadeTween = TweenService:Create(iceAura, TweenInfo.new(2, Enum.EasingStyle.Quad), {
				Transparency = 1,
			})
			fadeTween:Play()
			fadeTween.Completed:Connect(function()
				iceAura:Destroy()
				local index = (table.find(self.iceEffects, iceAura) or 0) - 1
				if index > -1 then
					table.remove(self.iceEffects, index + 1)
				end
			end)
		end)
	end
	function IceAgeAbility:createExpandingIceWave(centerPosition)
		print("🌊 Creating expanding ice wave")
		-- Create multiple expanding ice rings
		for ring = 0, 4 do
			task.delay(ring * 0.3, function()
				local iceRing = Instance.new("Part")
				iceRing.Name = "IceWaveRing"
				iceRing.Shape = Enum.PartType.Cylinder
				iceRing.Size = Vector3.new(0.5, 10, 10)
				iceRing.Color = Color3.fromRGB(200, 230, 255)
				iceRing.Material = Enum.Material.Ice
				iceRing.Transparency = 0.3
				iceRing.CanCollide = false
				iceRing.Anchored = true
				local _centerPosition = centerPosition
				local _vector3 = Vector3.new(0, 0.5, 0)
				iceRing.Position = _centerPosition + _vector3
				local _cFrame = iceRing.CFrame
				local _arg0 = CFrame.Angles(0, 0, math.pi / 2)
				iceRing.CFrame = _cFrame * _arg0
				iceRing.Parent = Workspace
				local _exp = self.iceEffects
				table.insert(_exp, iceRing)
				-- Expand the ring outward
				local expandTween = TweenService:Create(iceRing, TweenInfo.new(3, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
					Size = Vector3.new(0.5, 200, 200),
					Transparency = 0.8,
				})
				expandTween:Play()
				-- Remove after expansion
				expandTween.Completed:Connect(function()
					iceRing:Destroy()
					local index = (table.find(self.iceEffects, iceRing) or 0) - 1
					if index > -1 then
						table.remove(self.iceEffects, index + 1)
					end
				end)
			end)
		end
	end
	function IceAgeAbility:createIceEnvironment()
		print("🧊 Creating ice environment transformation")
		-- Create ice floor patches across the map
		for i = 0, 24 do
			task.delay(i * 0.2, function()
				local randomX = math.random(-150, 150)
				local randomZ = math.random(-150, 150)
				local icePosition = Vector3.new(randomX, 0.5, randomZ)
				local iceFloor = Instance.new("Part")
				iceFloor.Name = "IceFloor"
				iceFloor.Shape = Enum.PartType.Block
				iceFloor.Size = Vector3.new(math.random(8, 15), 1, math.random(8, 15))
				iceFloor.Color = Color3.fromRGB(220, 240, 255)
				iceFloor.Material = Enum.Material.Ice
				iceFloor.Transparency = 0.2
				iceFloor.CanCollide = true
				iceFloor.Anchored = true
				iceFloor.Position = icePosition
				iceFloor.Parent = Workspace
				local _exp = self.iceEffects
				table.insert(_exp, iceFloor)
				-- Add frost effect
				local frostDecal = Instance.new("Decal")
				frostDecal.Texture = "rbxasset://textures/face.png"
				frostDecal.Face = Enum.NormalId.Top
				frostDecal.Transparency = 0.7
				frostDecal.Color3 = Color3.fromRGB(200, 230, 255)
				frostDecal.Parent = iceFloor
			end)
		end
	end
	function IceAgeAbility:createIceAtmosphere()
		print("❄️ Creating ice atmosphere")
		-- Change lighting to cold ice atmosphere
		local lightingTween = TweenService:Create(Lighting, TweenInfo.new(2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
			Ambient = Color3.fromRGB(100, 150, 200),
			OutdoorAmbient = Color3.fromRGB(120, 170, 220),
			Brightness = 1.5,
			ColorShift_Top = Color3.fromRGB(150, 200, 255),
		})
		lightingTween:Play()
		-- Create atmospheric fog effect
		local atmosphere = Lighting:FindFirstChild("Atmosphere")
		if atmosphere then
			local originalAtmosphere = {
				Color = atmosphere.Color,
				Haze = atmosphere.Haze,
				Glare = atmosphere.Glare,
			}
			local atmosphereTween = TweenService:Create(atmosphere, TweenInfo.new(2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
				Color = Color3.fromRGB(200, 230, 255),
				Haze = 1.5,
				Glare = 0.5,
			})
			atmosphereTween:Play()
			-- Restore atmosphere after duration
			task.delay(10, function()
				local restoreAtmosphereTween = TweenService:Create(atmosphere, TweenInfo.new(2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), originalAtmosphere)
				restoreAtmosphereTween:Play()
			end)
		end
		-- Restore lighting after duration
		task.delay(10, function()
			local restoreLightingTween = TweenService:Create(Lighting, TweenInfo.new(2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), self.originalLighting)
			restoreLightingTween:Play()
		end)
	end
	function IceAgeAbility:createFloatingIceCrystals()
		print("💎 Creating floating ice crystals")
		-- Create floating ice crystals across the map
		for i = 0, 19 do
			task.delay(i * 0.3, function()
				local randomPosition = Vector3.new(math.random(-100, 100), math.random(10, 40), math.random(-100, 100))
				local crystal = Instance.new("Part")
				crystal.Name = "IceCrystal"
				crystal.Shape = Enum.PartType.Block
				crystal.Size = Vector3.new(math.random(1, 3), math.random(3, 6), math.random(1, 3))
				crystal.Color = Color3.fromRGB(180, 220, 255)
				crystal.Material = Enum.Material.Ice
				crystal.Transparency = 0.1
				crystal.CanCollide = false
				crystal.Anchored = true
				crystal.Position = randomPosition
				-- Random crystal rotation
				local _cFrame = crystal.CFrame
				local _arg0 = CFrame.Angles(math.random() * math.pi, math.random() * math.pi * 2, math.random() * math.pi)
				crystal.CFrame = _cFrame * _arg0
				crystal.Parent = Workspace
				local _exp = self.iceEffects
				table.insert(_exp, crystal)
				-- Add cold light
				local crystalLight = Instance.new("PointLight")
				crystalLight.Color = Color3.fromRGB(150, 200, 255)
				crystalLight.Brightness = 2
				crystalLight.Range = 12
				crystalLight.Parent = crystal
				-- Floating animation
				local _exp_1 = TweenInfo.new(4, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true)
				local _object = {}
				local _left = "Position"
				local _vector3 = Vector3.new(0, 3, 0)
				_object[_left] = randomPosition + _vector3
				local floatTween = TweenService:Create(crystal, _exp_1, _object)
				floatTween:Play()
				-- Slow rotation
				local _exp_2 = TweenInfo.new(6, Enum.EasingStyle.Linear, Enum.EasingDirection.InOut, -1)
				local _object_1 = {}
				local _left_1 = "CFrame"
				local _cFrame_1 = crystal.CFrame
				local _arg0_1 = CFrame.Angles(0, math.pi * 2, 0)
				_object_1[_left_1] = _cFrame_1 * _arg0_1
				local rotateTween = TweenService:Create(crystal, _exp_2, _object_1)
				rotateTween:Play()
			end)
		end
	end
	function IceAgeAbility:createIceSpears()
		print("🗡️ Creating ice spears")
		-- Create ice spears that shoot up from the ground
		for i = 0, 14 do
			task.delay(i * 0.4, function()
				local randomPosition = Vector3.new(math.random(-80, 80), 0, math.random(-80, 80))
				local spear = Instance.new("Part")
				spear.Name = "IceSpear"
				spear.Shape = Enum.PartType.Block
				spear.Size = Vector3.new(1, 0.5, 1)
				spear.Color = Color3.fromRGB(200, 230, 255)
				spear.Material = Enum.Material.Ice
				spear.Transparency = 0.1
				spear.CanCollide = false
				spear.Anchored = true
				spear.Position = randomPosition
				spear.Parent = Workspace
				local _exp = self.iceEffects
				table.insert(_exp, spear)
				-- Add sharp light
				local spearLight = Instance.new("PointLight")
				spearLight.Color = Color3.fromRGB(150, 200, 255)
				spearLight.Brightness = 3
				spearLight.Range = 10
				spearLight.Parent = spear
				-- Shoot up animation
				local _exp_1 = TweenInfo.new(0.8, Enum.EasingStyle.Back, Enum.EasingDirection.Out)
				local _object = {
					Size = Vector3.new(1.5, 8, 1.5),
				}
				local _left = "Position"
				local _vector3 = Vector3.new(0, 4, 0)
				_object[_left] = randomPosition + _vector3
				local shootTween = TweenService:Create(spear, _exp_1, _object)
				shootTween:Play()
				-- Create ice impact effect
				local impactEffect = Instance.new("Part")
				impactEffect.Name = "IceImpact"
				impactEffect.Shape = Enum.PartType.Ball
				impactEffect.Size = Vector3.new(0.5, 0.5, 0.5)
				impactEffect.Color = Color3.fromRGB(255, 255, 255)
				impactEffect.Material = Enum.Material.Neon
				impactEffect.Transparency = 0.2
				impactEffect.CanCollide = false
				impactEffect.Anchored = true
				impactEffect.Position = randomPosition
				impactEffect.Parent = Workspace
				local _exp_2 = self.iceEffects
				table.insert(_exp_2, impactEffect)
				-- Impact expansion
				local impactTween = TweenService:Create(impactEffect, TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
					Size = Vector3.new(6, 6, 6),
					Transparency = 1,
				})
				impactTween:Play()
				impactTween.Completed:Connect(function()
					return impactEffect:Destroy()
				end)
			end)
		end
	end
	function IceAgeAbility:cleanupEffects()
		print("🧹 Cleaning up Ice Age effects")
		-- Clean up all ice effects
		for _, effect in self.iceEffects do
			if effect.Parent then
				-- Fade out effect
				local fadeTween = TweenService:Create(effect, TweenInfo.new(2, Enum.EasingStyle.Quad), {
					Transparency = 1,
				})
				fadeTween:Play()
				fadeTween.Completed:Connect(function()
					return effect:Destroy()
				end)
			end
		end
		self.iceEffects = {}
		-- Disconnect any remaining connections
		if self.iceConnection then
			self.iceConnection:Disconnect()
			self.iceConnection = nil
		end
		-- Restore all frozen objects
		self:restoreAllFrozenObjects()
	end
	function IceAgeAbility:freezeAllObjects()
		print("🧊 Freezing all objects on the map")
		-- Get all parts in the workspace
		local allParts = self:getAllPartsInWorkspace()
		for _, part in allParts do
			-- Skip if it's a player character part or already an ice effect
			if self:shouldSkipPart(part) then
				continue
			end
			-- Store original properties
			local _frozenObjects = self.frozenObjects
			local _arg1 = {
				originalMaterial = part.Material,
				originalColor = part.Color,
				originalTransparency = part.Transparency,
			}
			_frozenObjects[part] = _arg1
			-- Apply ice transformation
			self:applyIceTransformation(part)
		end
		-- ▼ ReadonlyMap.size ▼
		local _size = 0
		for _ in self.frozenObjects do
			_size += 1
		end
		-- ▲ ReadonlyMap.size ▲
		print(`❄️ Froze {_size} objects on the map`)
	end
	function IceAgeAbility:getAllPartsInWorkspace()
		local parts = {}
		local scanDescendants
		scanDescendants = function(parent)
			for _, child in parent:GetChildren() do
				if child:IsA("Part") then
					table.insert(parts, child)
				end
				-- Recursively scan children
				scanDescendants(child)
			end
		end
		scanDescendants(Workspace)
		return parts
	end
	function IceAgeAbility:shouldSkipPart(part)
		-- Skip player character parts
		local character = part:FindFirstAncestorOfClass("Model")
		if character and Players:GetPlayerFromCharacter(character) then
			return true
		end
		-- Skip our own ice effects
		local _condition = (string.find(part.Name, "Ice"))
		if not (_condition ~= 0 and _condition == _condition and _condition) then
			_condition = (string.find(part.Name, "Haki"))
		end
		if _condition ~= 0 and _condition == _condition and _condition then
			return true
		end
		-- Skip terrain and other special parts
		if part.Name == "Terrain" or part.Name == "SpawnLocation" then
			return true
		end
		-- Skip very small parts (likely decorative)
		local size = part.Size
		if size.X < 0.5 and size.Y < 0.5 and size.Z < 0.5 then
			return true
		end
		return false
	end
	function IceAgeAbility:applyIceTransformation(part)
		-- Transform the part to look frozen
		part.Material = Enum.Material.Ice
		part.Color = Color3.fromRGB(200, 230, 255)
		part.Transparency = math.min(part.Transparency + 0.2, 0.8)
		-- Create ice shell around the object
		self:createObjectIceShell(part)
		-- Add frost particles if the part is large enough
		if part.Size.Magnitude > 5 then
			self:addFrostParticles(part)
		end
	end
	function IceAgeAbility:createObjectIceShell(part)
		-- Create ice shell around the frozen object
		local iceShell = Instance.new("Part")
		iceShell.Name = "ObjectIceShell"
		iceShell.Shape = if part.Shape == Enum.PartType.Ball then Enum.PartType.Ball else Enum.PartType.Block
		local _size = part.Size
		local _vector3 = Vector3.new(0.5, 0.5, 0.5)
		iceShell.Size = _size + _vector3
		iceShell.Color = Color3.fromRGB(180, 220, 255)
		iceShell.Material = Enum.Material.Ice
		iceShell.Transparency = 0.6
		iceShell.CanCollide = false
		iceShell.Anchored = true
		iceShell.CFrame = part.CFrame
		iceShell.Parent = Workspace
		local _exp = self.iceEffects
		table.insert(_exp, iceShell)
		-- Store reference to ice shell
		local _frozenObjects = self.frozenObjects
		local _part = part
		local frozenData = _frozenObjects[_part]
		if frozenData then
			frozenData.iceShell = iceShell
		end
		-- Add cold light
		local iceLight = Instance.new("PointLight")
		iceLight.Color = Color3.fromRGB(150, 200, 255)
		iceLight.Brightness = 1
		iceLight.Range = 8
		iceLight.Parent = iceShell
		-- Follow the original part if it moves
		local followConnection
		followConnection = RunService.Heartbeat:Connect(function()
			if part.Parent and iceShell.Parent then
				iceShell.CFrame = part.CFrame
			else
				followConnection:Disconnect()
			end
		end)
		-- Cleanup connection after duration
		task.delay(12, function()
			followConnection:Disconnect()
		end)
	end
	function IceAgeAbility:addFrostParticles(part)
		-- Create frost particle effect for large objects
		local attachment = Instance.new("Attachment")
		attachment.Name = "FrostAttachment"
		attachment.Parent = part
		-- Create particle emitter for frost effect
		local particles = Instance.new("ParticleEmitter")
		particles.Name = "FrostParticles"
		particles.Texture = "rbxasset://textures/particles/sparkles_main.dds"
		particles.Color = ColorSequence.new(Color3.fromRGB(200, 230, 255))
		particles.Size = NumberSequence.new(0.2, 0.5)
		particles.Lifetime = NumberRange.new(2, 4)
		particles.Rate = 20
		particles.SpreadAngle = Vector2.new(45, 45)
		particles.Speed = NumberRange.new(2, 5)
		particles.Parent = attachment
		-- Stop particles after duration
		task.delay(12, function()
			particles.Enabled = false
			task.delay(5, function()
				if attachment.Parent then
					attachment:Destroy()
				end
			end)
		end)
	end
	function IceAgeAbility:restoreAllFrozenObjects()
		print("🔥 Restoring all frozen objects")
		for part, frozenData in self.frozenObjects do
			if part.Parent and part:IsA("Part") then
				local partInstance = part
				-- Restore original properties with smooth transition
				local restoreTween = TweenService:Create(partInstance, TweenInfo.new(2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
					Color = frozenData.originalColor,
					Transparency = frozenData.originalTransparency,
				})
				restoreTween:Play()
				-- Restore material immediately (can't tween enums)
				partInstance.Material = frozenData.originalMaterial
				-- Remove ice shell
				if frozenData.iceShell and frozenData.iceShell.Parent then
					local shellFadeTween = TweenService:Create(frozenData.iceShell, TweenInfo.new(2, Enum.EasingStyle.Quad), {
						Transparency = 1,
					})
					shellFadeTween:Play()
					shellFadeTween.Completed:Connect(function()
						if frozenData.iceShell and frozenData.iceShell.Parent then
							frozenData.iceShell:Destroy()
						end
					end)
				end
				-- Remove frost particles
				local frostAttachment = partInstance:FindFirstChild("FrostAttachment")
				if frostAttachment then
					local particles = frostAttachment:FindFirstChild("FrostParticles")
					if particles then
						particles.Enabled = false
					end
					task.delay(3, function()
						if frostAttachment.Parent then
							frostAttachment:Destroy()
						end
					end)
				end
			end
		end
		-- Clear the frozen objects map
		table.clear(self.frozenObjects)
		print("✅ All objects restored to original state")
	end
end
return {
	IceAgeAbility = IceAgeAbility,
}
