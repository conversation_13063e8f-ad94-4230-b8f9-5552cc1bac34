import { WorldEvent, EarthquakeEvent, TsunamiEvent, MeteorEvent, LightningStormEvent } from "./interfaces/WorldEventOptions";
export declare class WorldEventBroadcaster {
    private static activeEvents;
    private static eventCounter;
    private static updateConnection?;
    private static eventHistory;
    /**
     * Initialize the world event system
     */
    static initialize(): void;
    /**
     * Trigger a world-wide earthquake event (perfect for Whitebeard abilities)
     */
    static triggerEarthquake(options: EarthquakeEvent): string;
    /**
     * Trigger a tsunami event
     */
    static triggerTsunami(options: TsunamiEvent): string;
    /**
     * Trigger a meteor impact event
     */
    static triggerMeteorImpact(options: MeteorEvent): string;
    /**
     * Trigger a lightning storm event
     */
    static triggerLightningStorm(options: LightningStormEvent): string;
    /**
     * Execute earthquake event phases
     */
    private static executeEarthquakePhases;
    /**
     * Execute tsunami event phases
     */
    private static executeTsunamiPhases;
    /**
     * Execute meteor impact phases
     */
    private static executeMeteorPhases;
    /**
     * Execute lightning storm phases
     */
    private static executeLightningStormPhases;
    /**
     * Execute event phases with proper timing
     */
    private static executeEventPhases;
    /**
     * Execute a single event phase
     */
    private static executePhase;
    /**
     * Apply a specific event effect
     */
    private static applyEventEffect;
    /**
     * Apply environmental effects (lighting, weather, etc.)
     */
    private static applyEnvironmentalEffect;
    /**
     * Apply visual effects
     */
    private static applyVisualEffect;
    /**
     * Apply audio effects
     */
    private static applyAudioEffect;
    /**
     * Apply physics effects
     */
    private static applyPhysicsEffect;
    /**
     * Calculate affected regions for an event
     */
    private static calculateAffectedRegions;
    /**
     * Broadcast event start to all players
     */
    private static broadcastEventStart;
    /**
     * Get players affected by an event
     */
    private static getAffectedPlayers;
    /**
     * Get players within a radius
     */
    private static getPlayersInRadius;
    /**
     * Update all active events
     */
    private static updateActiveEvents;
    /**
     * Complete an event
     */
    private static completeEvent;
    /**
     * Cancel an active event
     */
    static cancelEvent(eventId: string): void;
    /**
     * Get all active events
     */
    static getActiveEvents(): Map<string, WorldEvent>;
    /**
     * Get event history
     */
    static getEventHistory(): WorldEvent[];
    /**
     * Shutdown the world event system
     */
    static shutdown(): void;
}
