-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitFor<PERSON>hild("RuntimeLib"))
local TweenService = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services").TweenService
local function createKneeBendStance(character)
	-- Create Whitebeard-style knee bend stance for cross punch
	print("🦵 Creating knee bend stance like <PERSON>be<PERSON>")
	local humanoid = character:Find<PERSON>irst<PERSON>hild("Humanoid")
	local humanoidRootPart = character:<PERSON><PERSON><PERSON><PERSON><PERSON>hild("HumanoidRootPart")
	if not humanoid or not humanoidRootPart then
		return nil
	end
	-- Find leg joints for knee bending
	local leftHip = character:Find<PERSON><PERSON>t<PERSON>hild("Left Hip")
	local rightHip = character:Find<PERSON>irst<PERSON>hild("Right Hip")
	local leftKnee = character:Find<PERSON>irs<PERSON><PERSON>hild("Left Knee")
	local rightKnee = character:<PERSON><PERSON><PERSON><PERSON><PERSON>hil<PERSON>("Right Knee")
	if leftHip and rightHip and leftKnee and rightKnee then
		-- Store original positions
		local originalLeftHip = leftHip.C0
		local originalRightHip = rightHip.C0
		-- Create knee bend animation (slight crouch like Whitebeard's stance)
		local _exp = TweenInfo.new(0.3, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
		local _object = {}
		local _left = "C0"
		local _arg0 = CFrame.Angles(math.rad(15), 0, 0)
		_object[_left] = originalLeftHip * _arg0
		local kneeBendTween = TweenService:Create(leftHip, _exp, _object)
		local _exp_1 = TweenInfo.new(0.3, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
		local _object_1 = {}
		local _left_1 = "C0"
		local _arg0_1 = CFrame.Angles(math.rad(15), 0, 0)
		_object_1[_left_1] = originalRightHip * _arg0_1
		local rightKneeBendTween = TweenService:Create(rightHip, _exp_1, _object_1)
		kneeBendTween:Play()
		rightKneeBendTween:Play()
		-- Reset after animation
		task.delay(2, function()
			local resetLeftTween = TweenService:Create(leftHip, TweenInfo.new(0.5), {
				C0 = originalLeftHip,
			})
			local resetRightTween = TweenService:Create(rightHip, TweenInfo.new(0.5), {
				C0 = originalRightHip,
			})
			resetLeftTween:Play()
			resetRightTween:Play()
		end)
	end
end
local function createArmCrossingPose(character)
	-- Create Whitebeard-style arm crossing pose before punches
	print("💪 Creating arm crossing pose like Whitebeard")
	local leftShoulder = character:FindFirstChild("Left Shoulder")
	local rightShoulder = character:FindFirstChild("Right Shoulder")
	local leftElbow = character:FindFirstChild("Left Elbow")
	local rightElbow = character:FindFirstChild("Right Elbow")
	if leftShoulder and rightShoulder and leftElbow and rightElbow then
		-- Store original positions
		local originalLeftShoulder = leftShoulder.C0
		local originalRightShoulder = rightShoulder.C0
		-- Cross arms in front of body (Whitebeard style)
		local _exp = TweenInfo.new(0.4, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
		local _object = {}
		local _left = "C0"
		local _arg0 = CFrame.Angles(math.rad(-45), math.rad(30), math.rad(-20))
		_object[_left] = originalLeftShoulder * _arg0
		local leftArmCrossTween = TweenService:Create(leftShoulder, _exp, _object)
		local _exp_1 = TweenInfo.new(0.4, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
		local _object_1 = {}
		local _left_1 = "C0"
		local _arg0_1 = CFrame.Angles(math.rad(-45), math.rad(-30), math.rad(20))
		_object_1[_left_1] = originalRightShoulder * _arg0_1
		local rightArmCrossTween = TweenService:Create(rightShoulder, _exp_1, _object_1)
		leftArmCrossTween:Play()
		rightArmCrossTween:Play()
		-- Reset arms after punches (2s delay)
		task.delay(2, function()
			local resetLeftTween = TweenService:Create(leftShoulder, TweenInfo.new(0.5), {
				C0 = originalLeftShoulder,
			})
			local resetRightTween = TweenService:Create(rightShoulder, TweenInfo.new(0.5), {
				C0 = originalRightShoulder,
			})
			resetLeftTween:Play()
			resetRightTween:Play()
		end)
	end
end
return {
	createKneeBendStance = createKneeBendStance,
	createArmCrossingPose = createArmCrossingPose,
}
