import { AbilityBase } from "./AbilityBase";
export declare class HakiDominanceAbility extends AbilityBase {
    private dominanceEffect?;
    private lightningEffects;
    private dominanceConnection?;
    private cooldownEndTime;
    private isActive;
    constructor();
    isOnCooldown(): boolean;
    private startCooldown;
    activate(): void;
    private createHakiDominanceAnimation;
    private createCharacterAnimation;
    private animateR6Character;
    private animateR15<PERSON>haracter;
    private createRedAtmosphere;
    private createMapWideLightning;
    private createRedLightningStrike;
    private createPressureEffect;
    private createCharacterAura;
    private cleanupEffects;
    private createRedGlassSparkles;
    private createSingleGlassSparkle;
    private createFloatingGlassShards;
    private createFloatingGlassShard;
    private createOrbitingSparkles;
    private createOrbitingSparkle;
}
