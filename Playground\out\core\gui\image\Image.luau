-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local BORDER_RADIUS = TS.import(script, game:GetService("ReplicatedStorage"), "core", "design").BORDER_RADIUS
local function Image(props)
	local size = props.size or UDim2.new(0, 24, 0, 24)
	local _condition = props.imageTransparency
	if _condition == nil then
		_condition = 0
	end
	local imageTransparency = _condition
	local _condition_1 = props.backgroundTransparency
	if _condition_1 == nil then
		_condition_1 = 1
	end
	local backgroundTransparency = _condition_1
	local _condition_2 = props.cornerRadius
	if _condition_2 == nil then
		_condition_2 = BORDER_RADIUS.sm
	end
	local cornerRadius = _condition_2
	return React.createElement("imagelabel", {
		Image = props.image,
		Size = size,
		Position = props.position,
		AnchorPoint = props.anchorPoint,
		LayoutOrder = props.layoutOrder,
		ImageColor3 = props.imageColor,
		ImageTransparency = imageTransparency,
		ScaleType = props.scaleType or Enum.ScaleType.Fit,
		BackgroundColor3 = props.backgroundColor,
		BackgroundTransparency = backgroundTransparency,
		BorderSizePixel = 0,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, cornerRadius),
	}))
end
return {
	Image = Image,
}
