export declare class FrameAnimationHelper {
    private static activeAnimations;
    static animate(id: string, duration: number, updateCallback: (progress: number, elapsed: number) => void, onComplete?: () => void, delay?: number): RBXScriptConnection | undefined;
    static createConnection(updateCallback: (elapsed: number) => void, id?: string): RBXScriptConnection;
    static stopAnimation(id: string): void;
    static stopAllAnimations(): void;
    static isAnimating(id: string): boolean;
}
