-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local Result = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "types", "Result").Result
local createError = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "types", "RobloxError").createError
-- Client-specific state interface
-- State action interface
-- State update function type
local ClientStateManager
do
	ClientStateManager = setmetatable({}, {
		__tostring = function()
			return "ClientStateManager"
		end,
	})
	ClientStateManager.__index = ClientStateManager
	function ClientStateManager.new(...)
		local self = setmetatable({}, ClientStateManager)
		return self:constructor(...) or self
	end
	function ClientStateManager:constructor()
		self.listeners = {}
		self.state = self:getInitialState()
	end
	function ClientStateManager:getInstance()
		if not ClientStateManager.instance then
			ClientStateManager.instance = ClientStateManager.new()
		end
		return ClientStateManager.instance
	end
	function ClientStateManager:getInitialState()
		return {
			ui = {
				debugPanel = {
					isOpen = false,
					activeTab = "overview",
				},
				worldTestingPanel = {
					isOpen = false,
				},
				actionBar = {
					isVisible = true,
					abilities = {},
				},
				voiceChat = {
					isOpen = false,
					participants = {},
				},
			},
			player = {
				abilities = {},
				cooldowns = {},
				position = Vector3.new(0, 0, 0),
				health = 100,
				maxHealth = 100,
			},
			world = {
				weather = "clear",
				timeOfDay = 12,
				gravity = 196.2,
			},
			network = {
				ping = 0,
				isConnected = true,
				lastServerUpdate = tick(),
			},
		}
	end
	function ClientStateManager:getState()
		return self.state
	end
	function ClientStateManager:dispatch(action, updater)
		local _exitType, _returns = TS.try(function()
			local newState = updater(self.state, action.payload)
			self.state = newState
			-- Notify all listeners
			for listener in self.listeners do
				listener(newState)
			end
			return TS.TRY_RETURN, { Result:ok(newState) }
		end, function(error)
			return TS.TRY_RETURN, { Result:err(createError(`Failed to dispatch action '{action.type}': {error}`)) }
		end)
		if _exitType then
			return unpack(_returns)
		end
	end
	function ClientStateManager:subscribe(listener)
		local _listeners = self.listeners
		local _listener = listener
		_listeners[_listener] = true
		-- Return unsubscribe function
		return function()
			local _listeners_1 = self.listeners
			local _listener_1 = listener
			_listeners_1[_listener_1] = nil
		end
	end
	function ClientStateManager:updateUI(section, updates)
		return self:dispatch({
			type = `UPDATE_UI_{string.upper(tostring(section))}`,
			payload = updates,
		}, function(state, payload)
			local _object = table.clone(state)
			setmetatable(_object, nil)
			local _left = "ui"
			local _object_1 = table.clone(state.ui)
			setmetatable(_object_1, nil)
			local _left_1 = section
			local _object_2 = table.clone(state.ui[section])
			setmetatable(_object_2, nil)
			for _k, _v in payload do
				_object_2[_k] = _v
			end
			_object_1[_left_1] = _object_2
			_object[_left] = _object_1
			return _object
		end)
	end
	function ClientStateManager:updatePlayer(field, value)
		return self:dispatch({
			type = `UPDATE_PLAYER_{string.upper(tostring(field))}`,
			payload = value,
		}, function(state, payload)
			local _object = table.clone(state)
			setmetatable(_object, nil)
			local _left = "player"
			local _object_1 = table.clone(state.player)
			setmetatable(_object_1, nil)
			_object_1[field] = payload
			_object[_left] = _object_1
			return _object
		end)
	end
	function ClientStateManager:updateWorld(field, value)
		return self:dispatch({
			type = `UPDATE_WORLD_{string.upper(tostring(field))}`,
			payload = value,
		}, function(state, payload)
			local _object = table.clone(state)
			setmetatable(_object, nil)
			local _left = "world"
			local _object_1 = table.clone(state.world)
			setmetatable(_object_1, nil)
			_object_1[field] = payload
			_object[_left] = _object_1
			return _object
		end)
	end
end
-- Global client state manager instance
local ClientState = ClientStateManager:getInstance()
-- React hook for using client state
local function useClientState()
	local state, setState = React.useState(ClientState:getState())
	React.useEffect(function()
		local unsubscribe = ClientState:subscribe(function(newState)
			setState(newState)
		end)
		return unsubscribe
	end, {})
	return state
end
-- React hook for specific UI section
local function useUIState(section)
	local state = useClientState()
	return state.ui[section]
end
return {
	useClientState = useClientState,
	useUIState = useUIState,
	ClientState = ClientState,
}
