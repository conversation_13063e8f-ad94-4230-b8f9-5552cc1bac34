import { WeatherOptions, WeatherType } from "./interfaces/WeatherOptions";
/**
 * WeatherController - Manages weather effects and atmospheric conditions
 * Provides simple methods for changing weather states with smooth transitions
 */
export declare class WeatherController {
    private static instance;
    private currentWeather;
    private weatherEffects;
    private constructor();
    static getInstance(): WeatherController;
    /**
     * Change the weather with optional smooth transition
     */
    setWeather(options: WeatherOptions): void;
    /**
     * Get current weather type
     */
    getCurrentWeather(): WeatherType;
    private setClearWeather;
    private setRainWeather;
    private setSnowWeather;
    private setStormWeather;
    private setThunderstormWeather;
    private setFogWeather;
    private setSandstormWeather;
    private createRainEffect;
    private createRainParticles;
    private createSnowEffect;
    private createSnowParticles;
    private createStormEffect;
    private createStormParticles;
    private createThunderstormEffect;
    private createThunderstormParticles;
    private createLightningFlash;
    private createSandstormEffect;
    private createSandParticles;
    private startLightningEffects;
    private clearWeatherEffects;
    /**
     * Cleanup all weather effects
     */
    cleanup(): void;
}
