-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local _services = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services")
local RunService = _services.RunService
local Players = _services.Players
local DebugRenderer = TS.import(script, game:GetService("ReplicatedStorage"), "core", "debug", "DebugRenderer").DebugRenderer
local AIDebugger = TS.import(script, game:GetService("ReplicatedStorage"), "core", "debug", "AIDebugger").AIDebugger
local PlayerDebugger = TS.import(script, game:GetService("ReplicatedStorage"), "core", "debug", "PlayerDebugger").PlayerDebugger
local PerformanceMonitor = TS.import(script, game:GetService("ReplicatedStorage"), "core", "debug", "PerformanceMonitor").PerformanceMonitor
local ResponsiveManager = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "layout", "ResponsiveManager").ResponsiveManager
local DebugManager
do
	DebugManager = setmetatable({}, {
		__tostring = function()
			return "DebugManager"
		end,
	})
	DebugManager.__index = DebugManager
	function DebugManager.new(...)
		local self = setmetatable({}, DebugManager)
		return self:constructor(...) or self
	end
	function DebugManager:constructor()
		self.isInitialized = false
		self.config = {
			showAI = true,
			showPlayers = true,
			showPerformance = true,
			showPositions = true,
			showPaths = true,
			showVision = true,
			showCollisions = false,
			showFPS = true,
			showMemory = true,
			enabled = false,
		}
		self.renderer = DebugRenderer.new()
		self.aiDebugger = AIDebugger.new(self.renderer)
		self.playerDebugger = PlayerDebugger.new(self.renderer)
		self.performanceMonitor = PerformanceMonitor.new(self.renderer)
	end
	function DebugManager:getInstance()
		if not DebugManager.instance then
			DebugManager.instance = DebugManager.new()
		end
		return DebugManager.instance
	end
	function DebugManager:initialize()
		if self.isInitialized then
			return nil
		end
		print(`🔧 [{tick()}] DebugManager: Starting initialization`)
		self:setupInputHandling()
		self:setupResponsiveLayout()
		self:startDebugLoop()
		self.isInitialized = true
		print(`🔧 [{tick()}] Debug Manager initialized! Press F3 to toggle debug overlay`)
	end
	function DebugManager:toggle()
		self.config.enabled = not self.config.enabled
		if self.config.enabled then
			print(`🔍 [{tick()}] Debug overlay enabled - creating debug panels`)
			self.renderer:show()
		else
			print(`🔍 [{tick()}] Debug overlay disabled`)
			self.renderer:hide()
		end
	end
	function DebugManager:setConfig(newConfig)
		local _object = table.clone(self.config)
		setmetatable(_object, nil)
		for _k, _v in newConfig do
			_object[_k] = _v
		end
		self.config = _object
	end
	function DebugManager:getConfig()
		local _object = table.clone(self.config)
		setmetatable(_object, nil)
		return _object
	end
	function DebugManager:isEnabled()
		return self.config.enabled
	end
	function DebugManager:toggleAI()
		self.config.showAI = not self.config.showAI
		print(`🤖 AI Debug: {if self.config.showAI then "ON" else "OFF"}`)
	end
	function DebugManager:togglePlayers()
		self.config.showPlayers = not self.config.showPlayers
		print(`👤 Player Debug: {if self.config.showPlayers then "ON" else "OFF"}`)
	end
	function DebugManager:togglePerformance()
		self.config.showPerformance = not self.config.showPerformance
		print(`⚡ Performance Debug: {if self.config.showPerformance then "ON" else "OFF"}`)
	end
	function DebugManager:togglePaths()
		self.config.showPaths = not self.config.showPaths
		print(`🛤️ Path Debug: {if self.config.showPaths then "ON" else "OFF"}`)
	end
	function DebugManager:toggleVision()
		self.config.showVision = not self.config.showVision
		print(`👁️ Vision Debug: {if self.config.showVision then "ON" else "OFF"}`)
	end
	function DebugManager:setupInputHandling()
		-- Keyboard shortcuts disabled - use the debug panel buttons instead
		-- Only setup input handling on client
		if not Players.LocalPlayer then
			return nil
		end
		-- No keyboard shortcuts - debug panel controls everything
		self.inputConnection = nil
	end
	function DebugManager:setupResponsiveLayout()
		local responsiveManager = ResponsiveManager:getInstance()
		-- Listen for screen size changes and recreate debug panels
		self.responsiveUnsubscribe = responsiveManager:onScreenSizeChange(function()
			if self.config.enabled then
				-- Recreate debug panels with new responsive positioning
				self:recreateDebugPanels()
			end
		end)
	end
	function DebugManager:recreateDebugPanels()
		-- Recreate all debug components to apply new responsive positioning
		self.performanceMonitor = PerformanceMonitor.new(self.renderer)
		self.aiDebugger = AIDebugger.new(self.renderer)
		self.playerDebugger = PlayerDebugger.new(self.renderer)
		print("🔧 Debug panels recreated for new screen size")
	end
	function DebugManager:startDebugLoop()
		self.heartbeatConnection = RunService.Heartbeat:Connect(function()
			if not self.config.enabled then
				return nil
			end
			local deltaTime = (RunService.Heartbeat:Wait())
			-- Update performance monitor
			if self.config.showPerformance then
				self.performanceMonitor:update(deltaTime)
			end
			-- Update AI debugging
			if self.config.showAI then
				self.aiDebugger:update(self.config)
			end
			-- Update player debugging
			if self.config.showPlayers then
				self.playerDebugger:update(self.config)
			end
			-- Render all debug information
			self.renderer:render()
		end)
	end
	function DebugManager:cleanup()
		if self.heartbeatConnection then
			self.heartbeatConnection:Disconnect()
		end
		if self.inputConnection then
			self.inputConnection:Disconnect()
		end
		if self.responsiveUnsubscribe then
			self.responsiveUnsubscribe()
		end
		self.renderer:cleanup()
		self.aiDebugger:cleanup()
		self.playerDebugger:cleanup()
		self.performanceMonitor:cleanup()
		self.isInitialized = false
	end
	function DebugManager:drawLine(from, to, color, duration)
		if color == nil then
			color = Color3.fromRGB(255, 255, 255)
		end
		if duration == nil then
			duration = 0.1
		end
		if self.config.enabled then
			self.renderer:drawLine(from, to, color, duration)
		end
	end
	function DebugManager:drawSphere(position, radius, color, duration)
		if radius == nil then
			radius = 1
		end
		if color == nil then
			color = Color3.fromRGB(255, 255, 255)
		end
		if duration == nil then
			duration = 0.1
		end
		if self.config.enabled then
			self.renderer:drawSphere(position, radius, color, duration)
		end
	end
	function DebugManager:drawText(position, text, color, duration)
		if color == nil then
			color = Color3.fromRGB(255, 255, 255)
		end
		if duration == nil then
			duration = 0.1
		end
		if self.config.enabled then
			self.renderer:drawText(position, text, color, duration)
		end
	end
	function DebugManager:logDebug(category, message, data)
		if self.config.enabled then
			local timestamp = os.date("%H:%M:%S")
			local logMessage = `[{timestamp}] [{category}] {message}`
			if data ~= 0 and data == data and data ~= "" and data then
				print(logMessage, data)
			else
				print(logMessage)
			end
		end
	end
end
return {
	DebugManager = DebugManager,
}
