import { Debu<PERSON><PERSON><PERSON><PERSON> } from "./DebugRenderer";
import { DebugConfig } from "./DebugManager";
import { PositionHelper } from "../helper/PositionHelper";
import { ResponsiveManager } from "../gui/layout/ResponsiveManager";

export class AIDebugger {
	private renderer: DebugRenderer;
	private aiInfoLabel?: TextLabel;

	constructor(renderer: DebugRenderer) {
		this.renderer = renderer;
		this.setupGUI();
	}

	public update(_config: DebugConfig): void {
		// For now, show placeholder info until we can safely access AIController
		this.updateAIInfo(0);

		// TODO: Find a way to access AI agents without circular dependency
		// This could be done by having the AIController register itself with the debug system
	}

	private setupGUI(): void {
		// No need to calculate position since this will be part of the unified panel
		this.aiInfoLabel = this.renderer.createGUIElement(
			"AIInfo",
			new UDim2(0, 0, 0, 0), // Position will be ignored in unified system
			new UDim2(0, 0, 0, 0), // Size will be ignored in unified system
			"AI Debug Info"
		);
	}

	private updateAIInfo(aiCount: number): void {
		if (!this.aiInfoLabel) return;

		const info = [
			"=== AI DEBUG INFO ===",
			`Active AI Agents: ${aiCount}`,
		];

		this.aiInfoLabel.Text = info.join("\n");
	}

	private debugAIAgent(aiAgent: unknown, config: DebugConfig): void {
		// Get AI agent data (we need to access private members for debugging)
		const entityId = this.getPrivateField(aiAgent, "entityId") as string;
		const entity = this.getPrivateField(aiAgent, "entity") as Instance;
		const state = (aiAgent as { getState(): string }).getState();
		const currentBehavior = this.getPrivateField(aiAgent, "currentBehavior") as { name?: string } | undefined;
		const aiConfig = this.getPrivateField(aiAgent, "config") as { detectionRange?: number } | undefined;

		if (!entity || !entity.Parent) return;

		const position = PositionHelper.getPosition(entity);
		const behaviorName = currentBehavior?.name || "None";

		// Show AI position and state
		if (config.showPositions) {
			this.renderer.drawSphere(position, 1, this.getStateColor(state), 0.1);
			
			const infoText = [
				`ID: ${entityId}`,
				`State: ${state}`,
				`Behavior: ${behaviorName}`,
				`Pos: ${math.floor(position.X)}, ${math.floor(position.Y)}, ${math.floor(position.Z)}`
			].join("\n");
			
			this.renderer.drawText(position.add(new Vector3(0, 3, 0)), infoText, Color3.fromRGB(255, 255, 255), 0.1);
		}

		// Show detection range
		if (config.showAI && aiConfig && aiConfig.detectionRange) {
			const detectionColor = Color3.fromRGB(255, 165, 0); // Orange
			this.renderer.drawSphere(position, aiConfig.detectionRange, detectionColor, 0.1);
		}

		// Show vision cone
		if (config.showVision) {
			const lookDirection = this.getEntityLookDirection(entity);
			if (lookDirection && aiConfig && aiConfig.detectionRange) {
				this.renderer.drawVisionCone(
					position,
					lookDirection,
					aiConfig.detectionRange,
					90, // 90 degree vision cone
					Color3.fromRGB(0, 255, 0),
					0.1
				);
			}
		}

		// Show AI paths
		if (config.showPaths) {
			this.debugAIPaths(aiAgent, position);
		}

		// Show target information
		this.debugAITarget(aiAgent, position);

		// Show blackboard data
		if (config.showAI) {
			this.debugBlackboard(aiAgent, position);
		}
	}

	private debugAIPaths(aiAgent: unknown, position: Vector3): void {
		const blackboard = this.getPrivateField(aiAgent, "blackboard") as Map<string, unknown>;
		
		// Show patrol path
		const patrolPoints = blackboard.get("patrolPoints") as Vector3[];
		if (patrolPoints && patrolPoints.size() > 0) {
			this.renderer.drawPath(patrolPoints, Color3.fromRGB(255, 255, 0), 0.1);
			
			// Highlight current patrol target
			const currentIndex = blackboard.get("currentPatrolIndex") as number || 0;
			if (currentIndex < patrolPoints.size()) {
				this.renderer.drawLine(position, patrolPoints[currentIndex], Color3.fromRGB(255, 0, 255), 0.1);
			}
		}

		// Show wander target
		const wanderTarget = blackboard.get("wanderTarget") as Vector3;
		if (wanderTarget) {
			this.renderer.drawLine(position, wanderTarget, Color3.fromRGB(0, 255, 255), 0.1);
			this.renderer.drawSphere(wanderTarget, 0.5, Color3.fromRGB(0, 255, 255), 0.1);
		}

		// Show investigate position
		const investigatePos = blackboard.get("investigatePosition") as Vector3;
		if (investigatePos) {
			this.renderer.drawLine(position, investigatePos, Color3.fromRGB(255, 128, 0), 0.1);
			this.renderer.drawSphere(investigatePos, 1, Color3.fromRGB(255, 128, 0), 0.1);
		}
	}

	private debugAITarget(aiAgent: unknown, position: Vector3): void {
		// Find the target using the same method as AIAgent
		const target = this.callPrivateMethod(aiAgent, "findNearestPlayer") as Instance | undefined;

		if (target && target.IsA("Model") && target.PrimaryPart) {
			const targetModel = target as Model;
			const targetPosition = targetModel.PrimaryPart!.Position;
			const distance = position.sub(targetPosition).Magnitude;

			// Draw line to target
			const aiEntity = this.getPrivateField(aiAgent, "entity") as Instance;
			const hasLineOfSight = PositionHelper.hasLineOfSight(position, targetPosition, [aiEntity]);
			const lineColor = hasLineOfSight ? Color3.fromRGB(0, 255, 0) : Color3.fromRGB(255, 0, 0);

			this.renderer.drawLine(position, targetPosition, lineColor, 0.1);

			// Show target info
			const targetInfo = [
				`Target: ${targetModel.Name}`,
				`Distance: ${math.floor(distance)}`,
				`LOS: ${hasLineOfSight ? "YES" : "NO"}`
			].join("\n");

			const midpoint = position.add(targetPosition).div(2);
			this.renderer.drawText(midpoint, targetInfo, Color3.fromRGB(255, 255, 0), 0.1);
		}
	}

	private debugBlackboard(aiAgent: unknown, position: Vector3): void {
		const blackboard = this.getPrivateField(aiAgent, "blackboard") as Map<string, unknown>;
		const blackboardData: string[] = [];

		blackboard.forEach((value, key) => {
			if (typeOf(value) === "number") {
				blackboardData.push(`${key}: ${math.floor(value as number * 100) / 100}`);
			} else if (typeOf(value) === "Vector3") {
				const vec = value as Vector3;
				blackboardData.push(`${key}: (${math.floor(vec.X)}, ${math.floor(vec.Y)}, ${math.floor(vec.Z)})`);
			} else if (typeOf(value) === "boolean") {
				blackboardData.push(`${key}: ${value ? "true" : "false"}`);
			} else {
				blackboardData.push(`${key}: ${tostring(value)}`);
			}
		});

		if (blackboardData.size() > 0) {
			const blackboardText = "Blackboard:\n" + blackboardData.join("\n");
			this.renderer.drawText(position.add(new Vector3(3, 0, 0)), blackboardText, Color3.fromRGB(200, 200, 200), 0.1);
		}
	}

	private getStateColor(state: string): Color3 {
		switch (state) {
			case "Idle": return Color3.fromRGB(128, 128, 128);
			case "Moving": return Color3.fromRGB(0, 255, 0);
			case "Following": return Color3.fromRGB(0, 0, 255);
			case "Patrolling": return Color3.fromRGB(255, 255, 0);
			case "Attacking": return Color3.fromRGB(255, 0, 0);
			case "Fleeing": return Color3.fromRGB(255, 0, 255);
			case "Investigating": return Color3.fromRGB(255, 165, 0);
			default: return Color3.fromRGB(255, 255, 255);
		}
	}

	private getEntityLookDirection(entity: Instance): Vector3 | undefined {
		if (entity.IsA("Model") && entity.PrimaryPart) {
			return entity.PrimaryPart.CFrame.LookVector;
		} else if (entity.IsA("BasePart")) {
			return entity.CFrame.LookVector;
		}
		return undefined;
	}

	// Helper methods to access private fields (for debugging purposes)
	private getPrivateField(obj: unknown, fieldName: string): unknown {
		return (obj as Record<string, unknown>)[fieldName];
	}

	private callPrivateMethod(obj: unknown, methodName: string, ...args: unknown[]): unknown {
		return ((obj as Record<string, unknown>)[methodName] as (...args: unknown[]) => unknown)(...args);
	}

	public cleanup(): void {
		// Cleanup any resources
	}
}
