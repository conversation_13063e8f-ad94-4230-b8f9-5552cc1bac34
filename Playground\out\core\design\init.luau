-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local exports = {}
exports.COLORS = TS.import(script, game:GetService("ReplicatedStorage"), "core", "design", "Colors").COLORS
exports.SIZES = TS.import(script, game:GetService("ReplicatedStorage"), "core", "design", "Sizes").SIZES
exports.TYPOGRAPHY = TS.import(script, game:GetService("ReplicatedStorage"), "core", "design", "Typography").TYPOGRAPHY
exports.BORDER_RADIUS = TS.import(script, game:GetService("ReplicatedStorage"), "core", "design", "BorderRadius").BORDER_RADIUS
return exports
