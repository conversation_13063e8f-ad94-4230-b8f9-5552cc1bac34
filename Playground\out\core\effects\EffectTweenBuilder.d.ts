export declare class EffectTweenBuilder {
    private instance;
    private tweenInfo;
    private properties;
    private delayTime;
    private completionCallback?;
    constructor(instance: Instance);
    static for(instance: Instance): EffectTweenBuilder;
    expand(size: Vector3): this;
    fade(trans: number): this;
    move(pos: Vector3): this;
    rotate(angles: CFrame): this;
    duration(time: number): this;
    easing(style: Enum.EasingStyle, direction: Enum.EasingDirection): this;
    delay(delay: number): this;
    brightness(brightness: number): this;
    onComplete(callback: () => void): this;
    play(): Tween;
}
