-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local Result = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "types", "Result").Result
local createError = TS.import(script, game:GetService("ReplicatedStorage"), "core", "foundation", "types", "RobloxError").createError
local BaseService
do
	BaseService = {}
	function BaseService:constructor(name)
		self.name = name
		self.isInitialized = false
		self.isShutdown = false
	end
	BaseService.initialize = TS.async(function(self)
		if self.isInitialized then
			return Result:ok(nil)
		end
		local _exitType, _returns = TS.try(function()
			local result = TS.await(self:onInitialize())
			if result:isError() then
				return TS.TRY_RETURN, { result }
			end
			self.isInitialized = true
			self:logInfo(`Service '{self.name}' initialized successfully`)
			return TS.TRY_RETURN, { Result:ok(nil) }
		end, function(error)
			local errorMessage = `Failed to initialize service '{self.name}': {error}`
			self:logError(errorMessage)
			return TS.TRY_RETURN, { Result:err(createError(errorMessage)) }
		end)
		if _exitType then
			return unpack(_returns)
		end
	end)
	BaseService.shutdown = TS.async(function(self)
		if self.isShutdown or not self.isInitialized then
			return Result:ok(nil)
		end
		local _exitType, _returns = TS.try(function()
			local result = TS.await(self:onShutdown())
			if result:isError() then
				return TS.TRY_RETURN, { result }
			end
			self.isShutdown = true
			self.isInitialized = false
			self:logInfo(`Service '{self.name}' shutdown successfully`)
			return TS.TRY_RETURN, { Result:ok(nil) }
		end, function(error)
			local errorMessage = `Failed to shutdown service '{self.name}': {error}`
			self:logError(errorMessage)
			return TS.TRY_RETURN, { Result:err(createError(errorMessage)) }
		end)
		if _exitType then
			return unpack(_returns)
		end
	end)
	function BaseService:ensureInitialized()
		if not self.isInitialized then
			return Result:err(createError(`Service '{self.name}' is not initialized`))
		end
		return Result:ok(nil)
	end
	function BaseService:ensureNotShutdown()
		if self.isShutdown then
			return Result:err(createError(`Service '{self.name}' has been shutdown`))
		end
		return Result:ok(nil)
	end
	function BaseService:logInfo(message)
		print(`[INFO] [{self.name}] {message}`)
	end
	function BaseService:logWarning(message)
		warn(`[WARN] [{self.name}] {message}`)
	end
	function BaseService:logError(message)
		warn(`[ERROR] [{self.name}] {message}`)
	end
	function BaseService:logDebug(message)
		print(`[DEBUG] [{self.name}] {message}`)
	end
end
return {
	BaseService = BaseService,
}
