-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local TweenService = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services").TweenService
local function findBodyJoints(character)
	local joints = {}
	local torso = character:FindFirstChild("Torso")
	local upperTorso = character:FindFirstChild("UpperTorso")
	if torso then
		-- R6
		local _arg1 = torso:FindFirstChild("Right Shoulder")
		joints.RightShoulder = _arg1
		local _arg1_1 = torso:FindFirstChild("Left Shoulder")
		joints.LeftShoulder = _arg1_1
		local _arg1_2 = torso:FindFirstChild("Neck")
		joints.Neck = _arg1_2
		local _arg1_3 = torso:FindFirstChild("Left Hip")
		joints.LeftHip = _arg1_3
		local _arg1_4 = torso:FindFirstChild("Right Hip")
		joints.RightHip = _arg1_4
		local hrp = character:FindFirstChild("HumanoidRootPart")
		if hrp then
			local _arg1_5 = hrp:FindFirstChild("RootJoint")
			joints.RootJoint = _arg1_5
		end
	elseif upperTorso then
		-- R15
		local _exp = character:GetDescendants()
		-- ▼ ReadonlyArray.filter ▼
		local _newValue = {}
		local _callback = function(inst)
			return inst:IsA("Motor6D")
		end
		local _length = 0
		for _k, _v in _exp do
			if _callback(_v, _k - 1, _exp) == true then
				_length += 1
				_newValue[_length] = _v
			end
		end
		-- ▲ ReadonlyArray.filter ▲
		local parts = _newValue
		for _, motor in parts do
			-- Right Shoulder
			local _condition = motor.Name == "RightShoulder"
			if not _condition then
				local _result = motor.Part0
				if _result ~= nil then
					_result = _result.Name
				end
				local _condition_1 = _result == "UpperTorso"
				if _condition_1 then
					local _result_1 = motor.Part1
					if _result_1 ~= nil then
						_result_1 = _result_1.Name
					end
					_condition_1 = _result_1 == "RightUpperArm"
				end
				_condition = _condition_1
				if not _condition then
					local _result_1 = motor.Part1
					if _result_1 ~= nil then
						_result_1 = _result_1.Name
					end
					local _condition_2 = _result_1 == "UpperTorso"
					if _condition_2 then
						local _result_2 = motor.Part0
						if _result_2 ~= nil then
							_result_2 = _result_2.Name
						end
						_condition_2 = _result_2 == "RightUpperArm"
					end
					_condition = _condition_2
				end
			end
			if _condition then
				joints.RightShoulder = motor
			end
			-- Left Shoulder
			local _condition_1 = motor.Name == "LeftShoulder"
			if not _condition_1 then
				local _result = motor.Part0
				if _result ~= nil then
					_result = _result.Name
				end
				local _condition_2 = _result == "UpperTorso"
				if _condition_2 then
					local _result_1 = motor.Part1
					if _result_1 ~= nil then
						_result_1 = _result_1.Name
					end
					_condition_2 = _result_1 == "LeftUpperArm"
				end
				_condition_1 = _condition_2
				if not _condition_1 then
					local _result_1 = motor.Part1
					if _result_1 ~= nil then
						_result_1 = _result_1.Name
					end
					local _condition_3 = _result_1 == "UpperTorso"
					if _condition_3 then
						local _result_2 = motor.Part0
						if _result_2 ~= nil then
							_result_2 = _result_2.Name
						end
						_condition_3 = _result_2 == "LeftUpperArm"
					end
					_condition_1 = _condition_3
				end
			end
			if _condition_1 then
				joints.LeftShoulder = motor
			end
			-- Neck
			local _condition_2 = motor.Name == "Neck"
			if _condition_2 then
				local _result = motor.Parent
				if _result ~= nil then
					_result = _result.Name
				end
				_condition_2 = _result == "Head"
			end
			if _condition_2 then
				joints.Neck = motor
			end
			-- Waist
			local _condition_3 = motor.Name == "Waist"
			if not _condition_3 then
				local _result = motor.Part0
				if _result ~= nil then
					_result = _result.Name
				end
				local _condition_4 = _result == "LowerTorso"
				if _condition_4 then
					local _result_1 = motor.Part1
					if _result_1 ~= nil then
						_result_1 = _result_1.Name
					end
					_condition_4 = _result_1 == "UpperTorso"
				end
				_condition_3 = _condition_4
				if not _condition_3 then
					local _result_1 = motor.Part1
					if _result_1 ~= nil then
						_result_1 = _result_1.Name
					end
					local _condition_5 = _result_1 == "LowerTorso"
					if _condition_5 then
						local _result_2 = motor.Part0
						if _result_2 ~= nil then
							_result_2 = _result_2.Name
						end
						_condition_5 = _result_2 == "UpperTorso"
					end
					_condition_3 = _condition_5
				end
			end
			if _condition_3 then
				joints.Waist = motor
			end
			-- RootJoint
			local _condition_4 = motor.Name == "Root"
			if not _condition_4 then
				local _result = motor.Part0
				if _result ~= nil then
					_result = _result.Name
				end
				local _condition_5 = _result == "HumanoidRootPart"
				if _condition_5 then
					local _result_1 = motor.Part1
					if _result_1 ~= nil then
						_result_1 = _result_1.Name
					end
					_condition_5 = _result_1 == "LowerTorso"
				end
				_condition_4 = _condition_5
				if not _condition_4 then
					local _result_1 = motor.Part1
					if _result_1 ~= nil then
						_result_1 = _result_1.Name
					end
					local _condition_6 = _result_1 == "HumanoidRootPart"
					if _condition_6 then
						local _result_2 = motor.Part0
						if _result_2 ~= nil then
							_result_2 = _result_2.Name
						end
						_condition_6 = _result_2 == "LowerTorso"
					end
					_condition_4 = _condition_6
				end
			end
			if _condition_4 then
				joints.RootJoint = motor
			end
			-- Left Hip
			local _condition_5 = motor.Name == "LeftHip"
			if not _condition_5 then
				local _result = motor.Part0
				if _result ~= nil then
					_result = _result.Name
				end
				local _condition_6 = _result == "LowerTorso"
				if _condition_6 then
					local _result_1 = motor.Part1
					if _result_1 ~= nil then
						_result_1 = _result_1.Name
					end
					_condition_6 = _result_1 == "LeftUpperLeg"
				end
				_condition_5 = _condition_6
				if not _condition_5 then
					local _result_1 = motor.Part1
					if _result_1 ~= nil then
						_result_1 = _result_1.Name
					end
					local _condition_7 = _result_1 == "LowerTorso"
					if _condition_7 then
						local _result_2 = motor.Part0
						if _result_2 ~= nil then
							_result_2 = _result_2.Name
						end
						_condition_7 = _result_2 == "LeftUpperLeg"
					end
					_condition_5 = _condition_7
				end
			end
			if _condition_5 then
				joints.LeftHip = motor
			end
			-- Right Hip
			local _condition_6 = motor.Name == "RightHip"
			if not _condition_6 then
				local _result = motor.Part0
				if _result ~= nil then
					_result = _result.Name
				end
				local _condition_7 = _result == "LowerTorso"
				if _condition_7 then
					local _result_1 = motor.Part1
					if _result_1 ~= nil then
						_result_1 = _result_1.Name
					end
					_condition_7 = _result_1 == "RightUpperLeg"
				end
				_condition_6 = _condition_7
				if not _condition_6 then
					local _result_1 = motor.Part1
					if _result_1 ~= nil then
						_result_1 = _result_1.Name
					end
					local _condition_8 = _result_1 == "LowerTorso"
					if _condition_8 then
						local _result_2 = motor.Part0
						if _result_2 ~= nil then
							_result_2 = _result_2.Name
						end
						_condition_8 = _result_2 == "RightUpperLeg"
					end
					_condition_6 = _condition_8
				end
			end
			if _condition_6 then
				joints.RightHip = motor
			end
			-- Left Knee
			local _condition_7 = motor.Name == "LeftKnee"
			if not _condition_7 then
				local _result = motor.Part0
				if _result ~= nil then
					_result = _result.Name
				end
				local _condition_8 = _result == "LeftUpperLeg"
				if _condition_8 then
					local _result_1 = motor.Part1
					if _result_1 ~= nil then
						_result_1 = _result_1.Name
					end
					_condition_8 = _result_1 == "LeftLowerLeg"
				end
				_condition_7 = _condition_8
				if not _condition_7 then
					local _result_1 = motor.Part1
					if _result_1 ~= nil then
						_result_1 = _result_1.Name
					end
					local _condition_9 = _result_1 == "LeftUpperLeg"
					if _condition_9 then
						local _result_2 = motor.Part0
						if _result_2 ~= nil then
							_result_2 = _result_2.Name
						end
						_condition_9 = _result_2 == "LeftLowerLeg"
					end
					_condition_7 = _condition_9
				end
			end
			if _condition_7 then
				joints.LeftKnee = motor
			end
			-- Right Knee
			local _condition_8 = motor.Name == "RightKnee"
			if not _condition_8 then
				local _result = motor.Part0
				if _result ~= nil then
					_result = _result.Name
				end
				local _condition_9 = _result == "RightUpperLeg"
				if _condition_9 then
					local _result_1 = motor.Part1
					if _result_1 ~= nil then
						_result_1 = _result_1.Name
					end
					_condition_9 = _result_1 == "RightLowerLeg"
				end
				_condition_8 = _condition_9
				if not _condition_8 then
					local _result_1 = motor.Part1
					if _result_1 ~= nil then
						_result_1 = _result_1.Name
					end
					local _condition_10 = _result_1 == "RightUpperLeg"
					if _condition_10 then
						local _result_2 = motor.Part0
						if _result_2 ~= nil then
							_result_2 = _result_2.Name
						end
						_condition_10 = _result_2 == "RightLowerLeg"
					end
					_condition_8 = _condition_10
				end
			end
			if _condition_8 then
				joints.RightKnee = motor
			end
			-- Left Elbow
			local _condition_9 = motor.Name == "LeftElbow"
			if not _condition_9 then
				local _result = motor.Part0
				if _result ~= nil then
					_result = _result.Name
				end
				local _condition_10 = _result == "LeftUpperArm"
				if _condition_10 then
					local _result_1 = motor.Part1
					if _result_1 ~= nil then
						_result_1 = _result_1.Name
					end
					_condition_10 = _result_1 == "LeftLowerArm"
				end
				_condition_9 = _condition_10
				if not _condition_9 then
					local _result_1 = motor.Part1
					if _result_1 ~= nil then
						_result_1 = _result_1.Name
					end
					local _condition_11 = _result_1 == "LeftUpperArm"
					if _condition_11 then
						local _result_2 = motor.Part0
						if _result_2 ~= nil then
							_result_2 = _result_2.Name
						end
						_condition_11 = _result_2 == "LeftLowerArm"
					end
					_condition_9 = _condition_11
				end
			end
			if _condition_9 then
				joints.LeftElbow = motor
			end
			-- Right Elbow
			local _condition_10 = motor.Name == "RightElbow"
			if not _condition_10 then
				local _result = motor.Part0
				if _result ~= nil then
					_result = _result.Name
				end
				local _condition_11 = _result == "RightUpperArm"
				if _condition_11 then
					local _result_1 = motor.Part1
					if _result_1 ~= nil then
						_result_1 = _result_1.Name
					end
					_condition_11 = _result_1 == "RightLowerArm"
				end
				_condition_10 = _condition_11
				if not _condition_10 then
					local _result_1 = motor.Part1
					if _result_1 ~= nil then
						_result_1 = _result_1.Name
					end
					local _condition_12 = _result_1 == "RightUpperArm"
					if _condition_12 then
						local _result_2 = motor.Part0
						if _result_2 ~= nil then
							_result_2 = _result_2.Name
						end
						_condition_12 = _result_2 == "RightLowerArm"
					end
					_condition_10 = _condition_12
				end
			end
			if _condition_10 then
				joints.RightElbow = motor
			end
		end
	end
	-- Debug: Print found joints
	for name, joint in joints do
		print(`🔍 Joint {name}: {if joint then "✅ Found" else "❌ Missing"}`)
	end
	return joints
end
local function storeJointPositions(joints)
	-- ▼ ReadonlyMap.size ▼
	local _size = 0
	for _ in joints do
		_size += 1
	end
	-- ▲ ReadonlyMap.size ▲
	print(`💾 Storing original positions for {_size} joints...`)
	for name, joint in joints do
		if joint then
			local originalC0 = joint.C0
			joint:SetAttribute(`Original_{name}`, originalC0)
			print(`✅ Stored original for {name}: {originalC0}`)
		else
			print(`❌ Cannot store original for {name} - joint is undefined`)
		end
	end
end
local function restoreBodyJoints(joints)
	-- ▼ ReadonlyMap.size ▼
	local _size = 0
	for _ in joints do
		_size += 1
	end
	-- ▲ ReadonlyMap.size ▲
	print(`🔄 Restoring {_size} joints...`)
	for name, joint in joints do
		if joint then
			local original = joint:GetAttribute(`Original_{name}`)
			local current = joint.C0
			if original then
				print(`🔄 Restoring {name}: {current} -> {original}`)
				local info = TweenInfo.new(0.8, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut)
				local tween = TweenService:Create(joint, info, {
					C0 = original,
				})
				tween:Play()
				tween.Completed:Connect(function()
					print(`✅ {name} restoration completed`)
					joint:SetAttribute(`Original_{name}`, nil)
				end)
			else
				print(`❌ No original stored for {name}, current: {current}`)
			end
		else
			print(`❌ Joint {name} is undefined`)
		end
	end
end
local function getJoint(character, jointName)
	local joints = findBodyJoints(character)
	local _jointName = jointName
	return joints[_jointName]
end
return {
	findBodyJoints = findBodyJoints,
	storeJointPositions = storeJointPositions,
	restoreBodyJoints = restoreBodyJoints,
	getJoint = getJoint,
}
