import { BaseService } from "../foundation/BaseService";
import { Result } from "../foundation/types/Result";
import { EventName, PlayerId } from "../foundation/types/BrandedTypes";
import { Error } from "../foundation/types/RobloxError";
import { NetworkError } from "./errors/NetworkError";
import { NetworkValidationService } from "./NetworkValidationService";
export declare class NetworkService extends BaseService {
    private remoteEvents;
    private eventDescriptors;
    private validationService?;
    constructor();
    protected onInitialize(): Promise<Result<void, Error>>;
    protected onShutdown(): Promise<Result<void, Error>>;
    setValidationService(validationService: NetworkValidationService): void;
    registerEvent<TRequest, TResponse>(eventName: EventName, handler: (player: Player, request: TRequest) => Promise<Result<TResponse, Error>>, options?: {
        validateRequest?: (request: unknown) => Result<TRequest, Error>;
        rateLimit?: {
            maxCalls: number;
            windowMs: number;
        };
        requiresAuth?: boolean;
    }): Result<void, NetworkError>;
    fireClient<T>(eventName: EventName, playerId: PlayerId, data: T): Result<void, NetworkError>;
    fireAllClients<T>(eventName: EventName, data: T, excludePlayer?: PlayerId): Result<void, NetworkError>;
    private createRemoteEventsFolder;
    private createRemoteEvent;
    private setupEventHandler;
    private sendErrorResponse;
}
