export declare class TrailHelper {
    private trail;
    private attachment0;
    private attachment1?;
    constructor(parent: Instance);
    static create(parent: Instance): TrailHelper;
    color(color: Color3): this;
    transparency(transparency: number): this;
    lifetime(lifetime: number): this;
    width(width: number): this;
    texture(textureId: string): this;
    minLength(length: number): this;
    faceCamera(enabled?: boolean): this;
    addSecondAttachment(offset?: Vector3): this;
    spawn(): Trail;
    static createProjectileTrail(parent: Instance, color?: Color3): Trail;
    static createMagicTrail(parent: Instance, color?: Color3): Trail;
    static createSmokeTrail(parent: Instance): Trail;
}
