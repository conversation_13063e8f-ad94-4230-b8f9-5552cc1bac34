import * as React from "@rbxts/react";
import { Result } from "../foundation/types/Result";
import { Error, createError } from "../foundation/types/RobloxError";

// Client-specific state interface
export interface ClientGameState {
    ui: {
        debugPanel: {
            isOpen: boolean;
            activeTab: string;
        };
        worldTestingPanel: {
            isOpen: boolean;
        };
        actionBar: {
            isVisible: boolean;
            abilities: ClientAbilitySlot[];
        };
        voiceChat: {
            isOpen: boolean;
            participants: VoiceChatParticipant[];
        };
    };
    player: {
        abilities: PlayerAbility[];
        cooldowns: Map<string, number>;
        position: Vector3;
        health: number;
        maxHealth: number;
    };
    world: {
        weather: string;
        timeOfDay: number;
        gravity: number;
    };
    network: {
        ping: number;
        isConnected: boolean;
        lastServerUpdate: number;
    };
}

export interface ClientAbilitySlot {
    id: string;
    name: string;
    icon: string;
    cooldown: number;
    maxCooldown: number;
    isActive: boolean;
}

export interface VoiceChatParticipant {
    userId: number;
    displayName: string;
    isTalking: boolean;
    isMuted: boolean;
}

export interface PlayerAbility {
    id: string;
    name: string;
    type: string;
    cooldown: number;
    damage?: number;
    range?: number;
}

// State action interface
export interface ClientStateAction<T = unknown> {
    type: string;
    payload: T;
}

// State update function type
export type ClientStateUpdater<T = unknown> = (state: ClientGameState, payload: T) => ClientGameState;

class ClientStateManager {
    private static instance?: ClientStateManager;
    private state: ClientGameState;
    private listeners = new Set<(state: ClientGameState) => void>();

    private constructor() {
        this.state = this.getInitialState();
    }

    public static getInstance(): ClientStateManager {
        if (!ClientStateManager.instance) {
            ClientStateManager.instance = new ClientStateManager();
        }
        return ClientStateManager.instance;
    }

    private getInitialState(): ClientGameState {
        return {
            ui: {
                debugPanel: {
                    isOpen: false,
                    activeTab: "overview"
                },
                worldTestingPanel: {
                    isOpen: false
                },
                actionBar: {
                    isVisible: true,
                    abilities: []
                },
                voiceChat: {
                    isOpen: false,
                    participants: []
                }
            },
            player: {
                abilities: [],
                cooldowns: new Map(),
                position: new Vector3(0, 0, 0),
                health: 100,
                maxHealth: 100
            },
            world: {
                weather: "clear",
                timeOfDay: 12,
                gravity: 196.2
            },
            network: {
                ping: 0,
                isConnected: true,
                lastServerUpdate: tick()
            }
        };
    }

    public getState(): ClientGameState {
        return this.state;
    }

    public dispatch<T>(action: ClientStateAction<T>, updater: ClientStateUpdater<T>): Result<ClientGameState, Error> {
        try {
            const newState = updater(this.state, action.payload);
            this.state = newState;
            
            // Notify all listeners
            for (const listener of this.listeners) {
                listener(newState);
            }

            return Result.ok(newState);
        } catch (error) {
            return Result.err(createError(`Failed to dispatch action '${action.type}': ${error}`));
        }
    }

    public subscribe(listener: (state: ClientGameState) => void): () => void {
        this.listeners.add(listener);
        
        // Return unsubscribe function
        return () => {
            this.listeners.delete(listener);
        };
    }

    // Convenience methods for common state updates
    public updateUI<K extends keyof ClientGameState["ui"]>(
        section: K, 
        updates: Partial<ClientGameState["ui"][K]>
    ): Result<ClientGameState, Error> {
        return this.dispatch(
            { type: `UPDATE_UI_${tostring(section).upper()}`, payload: updates },
            (state, payload) => ({
                ...state,
                ui: {
                    ...state.ui,
                    [section]: {
                        ...state.ui[section],
                        ...payload
                    }
                }
            })
        );
    }

    public updatePlayer<K extends keyof ClientGameState["player"]>(
        field: K,
        value: ClientGameState["player"][K]
    ): Result<ClientGameState, Error> {
        return this.dispatch(
            { type: `UPDATE_PLAYER_${tostring(field).upper()}`, payload: value },
            (state, payload) => ({
                ...state,
                player: {
                    ...state.player,
                    [field]: payload
                }
            })
        );
    }

    public updateWorld<K extends keyof ClientGameState["world"]>(
        field: K,
        value: ClientGameState["world"][K]
    ): Result<ClientGameState, Error> {
        return this.dispatch(
            { type: `UPDATE_WORLD_${tostring(field).upper()}`, payload: value },
            (state, payload) => ({
                ...state,
                world: {
                    ...state.world,
                    [field]: payload
                }
            })
        );
    }
}

// Global client state manager instance
export const ClientState = ClientStateManager.getInstance();

// React hook for using client state
export function useClientState(): ClientGameState {
    const [state, setState] = React.useState(ClientState.getState());

    React.useEffect(() => {
        const unsubscribe = ClientState.subscribe((newState) => {
            setState(newState);
        });

        return unsubscribe;
    }, []);

    return state;
}

// React hook for specific UI section
export function useUIState<K extends keyof ClientGameState["ui"]>(section: K): ClientGameState["ui"][K] {
    const state = useClientState();
    return state.ui[section];
}
