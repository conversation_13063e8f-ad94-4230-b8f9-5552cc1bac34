-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local RunService = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services").RunService
local FrameAnimationHelper
do
	FrameAnimationHelper = setmetatable({}, {
		__tostring = function()
			return "FrameAnimationHelper"
		end,
	})
	FrameAnimationHelper.__index = FrameAnimationHelper
	function FrameAnimationHelper.new(...)
		local self = setmetatable({}, FrameAnimationHelper)
		return self:constructor(...) or self
	end
	function FrameAnimationHelper:constructor()
	end
	function FrameAnimationHelper:animate(id, duration, updateCallback, onComplete, delay)
		if delay == nil then
			delay = 0
		end
		-- Stop any existing animation with the same ID
		self:stopAnimation(id)
		local startAnimation = function()
			local startTime = tick()
			local connection
			connection = RunService.RenderStepped:Connect(function()
				local elapsed = tick() - startTime
				local progress = math.min(elapsed / duration, 1)
				updateCallback(progress, elapsed)
				if progress >= 1 then
					connection:Disconnect()
					local _activeAnimations = self.activeAnimations
					local _id = id
					_activeAnimations[_id] = nil
					if onComplete then
						onComplete()
					end
				end
			end)
			local _activeAnimations = self.activeAnimations
			local _id = id
			_activeAnimations[_id] = connection
			return connection
		end
		if delay > 0 then
			task.delay(delay, function()
				return startAnimation()
			end)
			return nil
		else
			return startAnimation()
		end
	end
	function FrameAnimationHelper:createConnection(updateCallback, id)
		local startTime = tick()
		local connection = RunService.RenderStepped:Connect(function()
			local elapsed = tick() - startTime
			updateCallback(elapsed)
		end)
		if id ~= "" and id then
			local _activeAnimations = self.activeAnimations
			local _id = id
			_activeAnimations[_id] = connection
		end
		return connection
	end
	function FrameAnimationHelper:stopAnimation(id)
		local _activeAnimations = self.activeAnimations
		local _id = id
		local connection = _activeAnimations[_id]
		if connection then
			connection:Disconnect()
			local _activeAnimations_1 = self.activeAnimations
			local _id_1 = id
			_activeAnimations_1[_id_1] = nil
		end
	end
	function FrameAnimationHelper:stopAllAnimations()
		for id, connection in self.activeAnimations do
			connection:Disconnect()
		end
		table.clear(self.activeAnimations)
	end
	function FrameAnimationHelper:isAnimating(id)
		local _activeAnimations = self.activeAnimations
		local _id = id
		return _activeAnimations[_id] ~= nil
	end
	FrameAnimationHelper.activeAnimations = {}
end
return {
	FrameAnimationHelper = FrameAnimationHelper,
}
