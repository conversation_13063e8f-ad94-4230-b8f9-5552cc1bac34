-- Compiled with roblox-ts v3.0.0
local NetworkError
do
	NetworkError = setmetatable({}, {
		__tostring = function()
			return "NetworkError"
		end,
	})
	NetworkError.__index = NetworkError
	function NetworkError.new(...)
		local self = setmetatable({}, NetworkError)
		return self:constructor(...) or self
	end
	function NetworkError:constructor(message, cause)
		self.name = "NetworkError"
		self.message = message
		self.cause = cause
	end
end
return {
	NetworkError = NetworkError,
}
