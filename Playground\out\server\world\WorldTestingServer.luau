-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local Lighting = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services").Lighting
local _core = TS.import(script, game:GetService("ReplicatedStorage"), "core")
local Core = _core.Core
local Result = _core.Result
local createError = _core.createError
local WorldStateManager = TS.import(script, game:GetService("ReplicatedStorage"), "core", "world").WorldStateManager
local WorldTestingServer
do
	WorldTestingServer = setmetatable({}, {
		__tostring = function()
			return "WorldTestingServer"
		end,
	})
	WorldTestingServer.__index = WorldTestingServer
	function WorldTestingServer.new(...)
		local self = setmetatable({}, WorldTestingServer)
		return self:constructor(...) or self
	end
	function WorldTestingServer:constructor()
		self.worldStateManager = WorldStateManager:getInstance()
		self:setupWorldTesting()
		self:storeOriginalLighting()
		print("🌍 World Testing Server initialized")
	end
	WorldTestingServer.setupWorldTesting = TS.async(function(self)
		local networkService = Core.getNetworkService()
		if networkService:isError() then
			warn(`Failed to get network service: {networkService:getError().message}`)
			return nil
		end
		-- Register world testing event
		local registerResult = networkService:getValue():registerEvent(Core.eventName("WorldTesting"), TS.async(function(player, request)
			return TS.await(self:handleWorldTest(player, request))
		end), {
			validateRequest = function(request)
				local _request = request
				if typeof(_request) ~= "table" then
					return Result:err(createError("Request must be a table"))
				end
				local req = request
				local _testType = req.testType
				if typeof(_testType) ~= "string" then
					return Result:err(createError("testType must be a string"))
				end
				return Result:ok(request)
			end,
			rateLimit = {
				maxCalls = 1,
				windowMs = 1000,
			},
			requiresAuth = false,
		})
		if registerResult:isError() then
			warn(`Failed to register WorldTesting event: {registerResult:getError().message}`)
		else
			print("✅ World testing setup complete")
		end
	end)
	function WorldTestingServer:storeOriginalLighting()
		self.originalLighting = {
			Brightness = Lighting.Brightness,
			Ambient = Lighting.Ambient,
			ColorShift_Top = Lighting.ColorShift_Top,
			ColorShift_Bottom = Lighting.ColorShift_Bottom,
		}
	end
	WorldTestingServer.handleWorldTest = TS.async(function(self, player, request)
		print(`🌍 {player.Name} testing world: {request.testType}`)
		local _exitType, _returns = TS.try(function()
			-- Handle gravity tests
			if (string.find(request.testType, "gravity")) ~= nil then
				self:handleGravityTest(request, player.UserId)
			elseif (string.find(request.testType, "weather")) ~= nil then
				self:handleWeatherTest(request, player.UserId)
			elseif (string.find(request.testType, "time")) ~= nil then
				self:handleTimeTest(request, player.UserId)
			else
				return TS.TRY_RETURN, { Result:err(createError(`Unknown world test: {request.testType}`)) }
			end
			return TS.TRY_RETURN, { Result:ok(true) }
		end, function(error)
			return TS.TRY_RETURN, { Result:err(createError(`World test failed: {error}`)) }
		end)
		if _exitType then
			return unpack(_returns)
		end
	end)
	function WorldTestingServer:handleGravityTest(request, playerId)
		local _exp = request.testType
		repeat
			if _exp == "low_gravity_world" then
				self.worldStateManager:applyWorldState({
					gravity = {
						level = "low",
					},
				}, playerId)
				break
			end
			if _exp == "high_gravity_world" then
				self.worldStateManager:applyWorldState({
					gravity = {
						level = "high",
					},
				}, playerId)
				break
			end
			if _exp == "zero_gravity_world" then
				self.worldStateManager:applyWorldState({
					gravity = {
						level = "zero",
					},
				}, playerId)
				break
			end
			if _exp == "normal_gravity_world" then
				self.worldStateManager:applyWorldState({
					gravity = {
						level = "normal",
					},
				}, playerId)
				break
			end
			print(`❌ Unknown gravity test: {request.testType}`)
		until true
	end
	function WorldTestingServer:handleWeatherTest(request, playerId)
		local _exp = request.testType
		repeat
			if _exp == "clear_weather" then
				self.worldStateManager:applyWorldState({
					weather = {
						type = "clear",
					},
				}, playerId)
				break
			end
			if _exp == "rain_weather" then
				self.worldStateManager:applyWorldState({
					weather = {
						type = "rain",
						intensity = 0.5,
					},
				}, playerId)
				break
			end
			if _exp == "heavy_rain_weather" then
				self.worldStateManager:applyWorldState({
					weather = {
						type = "heavy_rain",
						intensity = 0.8,
					},
				}, playerId)
				break
			end
			if _exp == "snow_weather" then
				self.worldStateManager:applyWorldState({
					weather = {
						type = "snow",
						intensity = 0.5,
					},
				}, playerId)
				break
			end
			if _exp == "blizzard_weather" then
				self.worldStateManager:applyWorldState({
					weather = {
						type = "blizzard",
						intensity = 0.9,
					},
				}, playerId)
				break
			end
			if _exp == "storm_weather" then
				self.worldStateManager:applyWorldState({
					weather = {
						type = "storm",
						intensity = 0.7,
					},
				}, playerId)
				break
			end
			if _exp == "thunderstorm_weather" then
				self.worldStateManager:applyWorldState({
					weather = {
						type = "thunderstorm",
						intensity = 0.8,
					},
				}, playerId)
				break
			end
			if _exp == "fog_weather" then
				self.worldStateManager:applyWorldState({
					weather = {
						type = "fog",
						intensity = 0.6,
					},
				}, playerId)
				break
			end
			if _exp == "sandstorm_weather" then
				self.worldStateManager:applyWorldState({
					weather = {
						type = "sandstorm",
						intensity = 0.7,
					},
				}, playerId)
				break
			end
			print(`❌ Unknown weather test: {request.testType}`)
		until true
	end
	function WorldTestingServer:handleTimeTest(request, playerId)
		local _exp = request.testType
		repeat
			if _exp == "dawn_time" then
				self.worldStateManager:applyWorldState({
					time = {
						timeOfDay = "dawn",
						transitionDuration = 3,
					},
				}, playerId)
				break
			end
			if _exp == "noon_time" then
				self.worldStateManager:applyWorldState({
					time = {
						timeOfDay = "noon",
						transitionDuration = 3,
					},
				}, playerId)
				break
			end
			if _exp == "dusk_time" then
				self.worldStateManager:applyWorldState({
					time = {
						timeOfDay = "dusk",
						transitionDuration = 3,
					},
				}, playerId)
				break
			end
			if _exp == "night_time" then
				self.worldStateManager:applyWorldState({
					time = {
						timeOfDay = "night",
						transitionDuration = 3,
					},
				}, playerId)
				break
			end
			print(`❌ Unknown time test: {request.testType}`)
		until true
	end
	function WorldTestingServer:cleanup()
		self.worldStateManager:cleanup()
		if self.currentGravityEffect then
			self.currentGravityEffect:Destroy()
		end
		print("🌍 World Testing Server cleaned up")
	end
	WorldTestingServer.NORMAL_GRAVITY = 196.2
end
return {
	WorldTestingServer = WorldTestingServer,
}
