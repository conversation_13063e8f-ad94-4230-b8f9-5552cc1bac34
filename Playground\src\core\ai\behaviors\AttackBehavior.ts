import { AIBehavior } from "../interfaces/AIBehavior";
import { AIContext } from "../interfaces/AIContext";
import { AIBehaviorResult } from "../interfaces/AIBehaviorResult";

export class AttackBehavior implements AIBehavior {
	name = "Attack";
	priority = 7;

	canExecute(context: AIContext): boolean {
		if (!context.target || !context.targetPosition) return false;
		
		const distance = context.position.sub(context.targetPosition).Magnitude;
		return distance <= 5;
	}

	execute(context: AIContext): AIBehaviorResult {
		if (!context.target || !context.targetPosition) {
			return { success: false, completed: true };
		}

		const distance = context.position.sub(context.targetPosition).Magnitude;
		
		if (distance > 5) {
			return { success: false, completed: true, nextBehavior: "Follow" };
		}

		this.performAttack(context);
		return { success: true, completed: false };
	}

	onEnter(context: AIContext): void {
		print(`⚔️ ${context.entityId} is attacking target`);
	}

	private performAttack(context: AIContext): void {
		const lastAttackTime = context.blackboard.lastAttackTime as number || 0;
		const currentTime = tick();
		
		if (currentTime - lastAttackTime >= 1) {
			print(`💥 ${context.entityId} attacks!`);
			context.blackboard.lastAttackTime = currentTime;
		}
	}
}
