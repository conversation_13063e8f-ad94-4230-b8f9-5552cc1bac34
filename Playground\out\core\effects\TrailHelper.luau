-- Compiled with roblox-ts v3.0.0
local TrailHelper
do
	TrailHelper = setmetatable({}, {
		__tostring = function()
			return "TrailHelper"
		end,
	})
	TrailHelper.__index = TrailHelper
	function TrailHelper.new(...)
		local self = setmetatable({}, TrailHelper)
		return self:constructor(...) or self
	end
	function TrailHelper:constructor(parent)
		-- Create attachments
		self.attachment0 = Instance.new("Attachment")
		self.attachment0.Parent = parent
		-- Create trail
		self.trail = Instance.new("Trail")
		self.trail.Attachment0 = self.attachment0
		self.trail.Attachment1 = self.attachment0
		self.trail.Parent = parent
	end
	function TrailHelper:create(parent)
		return TrailHelper.new(parent)
	end
	function TrailHelper:color(color)
		self.trail.Color = ColorSequence.new(color)
		return self
	end
	function TrailHelper:transparency(transparency)
		self.trail.Transparency = NumberSequence.new(transparency)
		return self
	end
	function TrailHelper:lifetime(lifetime)
		self.trail.Lifetime = lifetime
		return self
	end
	function TrailHelper:width(width)
		self.trail.WidthScale = NumberSequence.new(width)
		return self
	end
	function TrailHelper:texture(textureId)
		self.trail.Texture = textureId
		return self
	end
	function TrailHelper:minLength(length)
		self.trail.MinLength = length
		return self
	end
	function TrailHelper:faceCamera(enabled)
		if enabled == nil then
			enabled = true
		end
		self.trail.FaceCamera = enabled
		return self
	end
	function TrailHelper:addSecondAttachment(offset)
		if offset == nil then
			offset = Vector3.new(0, 0, 0)
		end
		if self.attachment0.Parent then
			self.attachment1 = Instance.new("Attachment")
			self.attachment1.Position = offset
			self.attachment1.Parent = self.attachment0.Parent
			self.trail.Attachment1 = self.attachment1
		end
		return self
	end
	function TrailHelper:spawn()
		return self.trail
	end
	function TrailHelper:createProjectileTrail(parent, color)
		if color == nil then
			color = Color3.fromRGB(255, 100, 100)
		end
		return TrailHelper:create(parent):color(color):transparency(0.5):lifetime(0.5):width(1):faceCamera(true):spawn()
	end
	function TrailHelper:createMagicTrail(parent, color)
		if color == nil then
			color = Color3.fromRGB(100, 255, 255)
		end
		return TrailHelper:create(parent):color(color):transparency(0.3):lifetime(1):width(2):faceCamera(true):spawn()
	end
	function TrailHelper:createSmokeTrail(parent)
		return TrailHelper:create(parent):color(Color3.fromRGB(128, 128, 128)):transparency(0.7):lifetime(2):width(3):texture("rbxassetid://241650934"):spawn()
	end
end
return {
	TrailHelper = TrailHelper,
}
