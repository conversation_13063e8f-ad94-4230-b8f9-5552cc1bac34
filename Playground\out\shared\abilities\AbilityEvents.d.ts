import { CastAbilityRequest, CastAbilityResponse, AbilityEffectData } from "./AbilityTypes";
export declare function getCastAbilityEvent(): RemoteEvent;
export declare function getAbilityCastConfirmEvent(): RemoteEvent;
export declare function getAbilityEffectEvent(): RemoteEvent;
export declare function getRequestAbilitiesEvent(): RemoteEvent;
export declare function getAbilitiesDataEvent(): RemoteEvent;
export declare namespace AbilityNetworking {
    function requestCastAbility(request: CastAbilityRequest): void;
    function requestAbilitiesData(): void;
    function confirmAbilityCast(player: Player, response: CastAbilityResponse): void;
    function replicateAbilityEffect(effectData: AbilityEffectData): void;
    function sendAbilitiesData(player: Player, abilities: any): void;
}
