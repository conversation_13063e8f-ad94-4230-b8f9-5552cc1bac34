import * as React from "@rbxts/react";
interface ImageProps {
    image: string;
    size?: UDim2;
    position?: UDim2;
    anchorPoint?: Vector2;
    layoutOrder?: number;
    imageColor?: Color3;
    imageTransparency?: number;
    scaleType?: Enum.ScaleType;
    backgroundColor?: Color3;
    backgroundTransparency?: number;
    cornerRadius?: number;
}
export declare function Image(props: ImageProps): React.ReactElement;
export {};
