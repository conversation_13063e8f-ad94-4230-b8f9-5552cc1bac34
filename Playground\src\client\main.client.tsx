import * as React from "@rbxts/react";
import { createRoot } from "@rbxts/react-roblox";
import { Players } from "@rbxts/services";
import { BottomLeftGrid } from "./gui/BottomLeftGrid";
import { ActionBarDemo } from "./gui/ActionBarDemo";
import { MovementExample } from "./movement/MovementExample";
import { initializeDebugSystem, initializeClientCore } from "../core";

const version = "v1.3.5";
const player = Players.LocalPlayer;
const playerGui = player.WaitForChild("PlayerGui") as PlayerGui;

// Create a ScreenGui with properties to make it visible
const screenGui = new Instance("ScreenGui", playerGui);
screenGui.ResetOnSpawn = false;
screenGui.IgnoreGuiInset = true;
screenGui.ZIndexBehavior = Enum.ZIndexBehavior.Sibling;
screenGui.DisplayOrder = 100; // Higher than DebugGUI to ensure interactive panels appear on top
screenGui.Name = "MainReactGUI";

print(`🎮 [${tick()}] Main React ScreenGui created with DisplayOrder: ${screenGui.DisplayOrder}`);

const root = createRoot(screenGui);

// Render the GUI components
root.render(
  <>
    <BottomLeftGrid
      onTestClick={() => print("Test button clicked!")}
      onHelloClick={() => print("Hello button clicked!")}
    />
    <ActionBarDemo />
  </>
);

// Initialize movement example for testing
new MovementExample();

// Initialize systems
async function initializeClient() {
    // Initialize debug system for development
    initializeDebugSystem();

    // Initialize client core
    const clientCoreResult = await initializeClientCore();
    if (clientCoreResult.isError()) {
        warn(`Failed to initialize client core: ${clientCoreResult.getError().message}`);
    } else {
        print("✅ Client Core initialized successfully!");
    }

    print(`🔥 Playground client loaded! [${version}]`);
    print(`🎙️ Voice system available! Use voiceDemo methods in console`);
}

// Start client initialization
initializeClient().catch((err) => {
    error(`Client initialization failed: ${err}`);
});