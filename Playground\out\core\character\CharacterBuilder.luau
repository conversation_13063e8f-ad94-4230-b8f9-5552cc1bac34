-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local Workspace = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services").Workspace
local CharacterBuilder
do
	CharacterBuilder = setmetatable({}, {
		__tostring = function()
			return "CharacterBuilder"
		end,
	})
	CharacterBuilder.__index = CharacterBuilder
	function CharacterBuilder.new(...)
		local self = setmetatable({}, CharacterBuilder)
		return self:constructor(...) or self
	end
	function CharacterBuilder:constructor()
		self.parts = {}
		self.model = Instance.new("Model")
		self.model.Parent = Workspace
	end
	function CharacterBuilder:create()
		return CharacterBuilder.new()
	end
	function CharacterBuilder:name(name)
		self.model.Name = name
		return self
	end
	function CharacterBuilder:position(position)
		if self.model.PrimaryPart then
			self.model.PrimaryPart.Position = position
		end
		return self
	end
	function CharacterBuilder:parent(parent)
		self.model.Parent = parent
		return self
	end
	function CharacterBuilder:addHumanoid(maxHealth)
		if maxHealth == nil then
			maxHealth = 100
		end
		self.humanoid = Instance.new("Humanoid")
		self.humanoid.MaxHealth = maxHealth
		self.humanoid.Health = maxHealth
		self.humanoid.Parent = self.model
		return self
	end
	function CharacterBuilder:addPart(partName, size, color, shape)
		local part = Instance.new("Part")
		part.Name = partName
		part.Size = size
		if color then
			part.Color = color
		end
		if shape then
			part.Shape = shape
		end
		part.Parent = self.model
		local _parts = self.parts
		local _partName = partName
		_parts[_partName] = part
		return self
	end
	function CharacterBuilder:addHead(color)
		if color == nil then
			color = Color3.fromRGB(255, 184, 148)
		end
		return self:addPart("Head", Vector3.new(2, 1, 1), color, Enum.PartType.Ball)
	end
	function CharacterBuilder:addTorso(color)
		if color == nil then
			color = Color3.fromRGB(13, 105, 172)
		end
		local torso = self:addPart("Torso", Vector3.new(2, 2, 1), color)
		self.model.PrimaryPart = self.parts.Torso
		return torso
	end
	function CharacterBuilder:addArm(side, color)
		if color == nil then
			color = Color3.fromRGB(255, 184, 148)
		end
		local armName = `{side} Arm`
		return self:addPart(armName, Vector3.new(1, 2, 1), color)
	end
	function CharacterBuilder:addLeg(side, color)
		if color == nil then
			color = Color3.fromRGB(13, 105, 172)
		end
		local legName = `{side} Leg`
		return self:addPart(legName, Vector3.new(1, 2, 1), color)
	end
	function CharacterBuilder:positionPart(partName, relativePosition)
		local _parts = self.parts
		local _partName = partName
		local part = _parts[_partName]
		local primaryPart = self.model.PrimaryPart
		if part and primaryPart then
			local _position = primaryPart.Position
			local _relativePosition = relativePosition
			part.Position = _position + _relativePosition
		end
		return self
	end
	function CharacterBuilder:addJoint(jointName, part0Name, part1Name, c0, c1)
		local _parts = self.parts
		local _part0Name = part0Name
		local part0 = _parts[_part0Name]
		local _parts_1 = self.parts
		local _part1Name = part1Name
		local part1 = _parts_1[_part1Name]
		if part0 and part1 then
			local joint = Instance.new("Motor6D")
			joint.Name = jointName
			joint.Part0 = part0
			joint.Part1 = part1
			joint.C0 = c0
			joint.C1 = c1
			joint.Parent = part0
		end
		return self
	end
	function CharacterBuilder:addNeckJoint()
		return self:addJoint("Neck", "Torso", "Head", CFrame.new(0, 1, 0), CFrame.new(0, -0.5, 0))
	end
	function CharacterBuilder:addShoulderJoint(side)
		local jointName = `{side} Shoulder`
		local armName = `{side} Arm`
		local xOffset = if side == "Left" then -1.5 else 1.5
		return self:addJoint(jointName, "Torso", armName, CFrame.new(xOffset, 0.5, 0), CFrame.new(0, 1, 0))
	end
	function CharacterBuilder:addHipJoint(side)
		local jointName = `{side} Hip`
		local legName = `{side} Leg`
		local xOffset = if side == "Left" then -0.5 else 0.5
		return self:addJoint(jointName, "Torso", legName, CFrame.new(xOffset, -1, 0), CFrame.new(0, 1, 0))
	end
	function CharacterBuilder:autoPositionParts()
		self:positionPart("Head", Vector3.new(0, 1.5, 0))
		self:positionPart("Left Arm", Vector3.new(-1.5, 0.5, 0))
		self:positionPart("Right Arm", Vector3.new(1.5, 0.5, 0))
		self:positionPart("Left Leg", Vector3.new(-0.5, -2, 0))
		self:positionPart("Right Leg", Vector3.new(0.5, -2, 0))
		return self
	end
	function CharacterBuilder:autoAddJoints()
		if self.parts.Head ~= nil then
			self:addNeckJoint()
		end
		if self.parts["Left Arm"] ~= nil then
			self:addShoulderJoint("Left")
		end
		if self.parts["Right Arm"] ~= nil then
			self:addShoulderJoint("Right")
		end
		if self.parts["Left Leg"] ~= nil then
			self:addHipJoint("Left")
		end
		if self.parts["Right Leg"] ~= nil then
			self:addHipJoint("Right")
		end
		return self
	end
	function CharacterBuilder:spawn()
		return self.model
	end
	function CharacterBuilder:createBasicNPC(name, position)
		if name == nil then
			name = "NPC"
		end
		if position == nil then
			position = Vector3.new(0, 0, 0)
		end
		return CharacterBuilder:create():name(name):addHumanoid(100):addTorso():addHead():position(position):positionPart("Head", Vector3.new(0, 1.5, 0)):addNeckJoint():spawn()
	end
	function CharacterBuilder:createBasicPlayer(name, position)
		if name == nil then
			name = "PlayerEntity"
		end
		if position == nil then
			position = Vector3.new(0, 0, 0)
		end
		return CharacterBuilder:create():name(name):addHumanoid(100):addTorso():position(position):spawn()
	end
	function CharacterBuilder:createFullCharacter(name, position)
		if name == nil then
			name = "Character"
		end
		if position == nil then
			position = Vector3.new(0, 0, 0)
		end
		return CharacterBuilder:create():name(name):addHumanoid(100):addTorso():addHead():addArm("Left"):addArm("Right"):addLeg("Left"):addLeg("Right"):position(position):autoPositionParts():autoAddJoints():spawn()
	end
end
return {
	CharacterBuilder = CharacterBuilder,
}
