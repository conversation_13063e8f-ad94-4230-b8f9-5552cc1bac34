-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local _design = TS.import(script, game:GetService("ReplicatedStorage"), "core", "design")
local COLORS = _design.COLORS
local BORDER_RADIUS = _design.BORDER_RADIUS
local function ListItemButton(props)
	local hovered, setHovered = React.useState(false)
	local backgroundColor = if props.selected then COLORS.bg["surface-hover"] elseif hovered then COLORS.bg.hover else COLORS.bg.surface
	local size = props.size or UDim2.new(1, -10, 0, 40)
	return React.createElement("textbutton", {
		Text = "",
		BackgroundColor3 = Color3.fromHex(backgroundColor),
		Size = size,
		BorderSizePixel = 0,
		LayoutOrder = props.layoutOrder,
		AutoButtonColor = false,
		Event = {
			MouseEnter = function()
				if not props.selected then
					setHovered(true)
				end
			end,
			MouseLeave = function()
				setHovered(false)
			end,
			Activated = function()
				if props.onClick then
					props.onClick()
				end
			end,
		},
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, BORDER_RADIUS.sm),
	}), props.children)
end
return {
	ListItemButton = ListItemButton,
}
