import { AIBehavior } from "../interfaces/AIBehavior";
import { AIContext } from "../interfaces/AIContext";
import { AIBehaviorResult } from "../interfaces/AIBehaviorResult";

export class WanderBehavior implements AIBehavior {
	name = "Wan<PERSON>";
	priority = 2;

	canExecute(context: AIContext): boolean {
		return !context.target;
	}

	execute(context: AIContext): AIBehaviorResult {
		const wanderTarget = context.blackboard.wanderTarget as Vector3;
		const wanderTime = context.blackboard.wanderTime as number || 0;

		if (!wanderTarget || wanderTime <= 0) {
			this.setNewWanderTarget(context);
			return { success: true, completed: false };
		}

		const distance = context.position.sub(wanderTarget).Magnitude;
		
		if (distance <= 3) {
			context.blackboard.wanderTarget = undefined;
			context.blackboard.wanderTime = 0;
			return { success: true, completed: true, nextBehavior: "Idle" };
		}

		this.moveTowards(context, wanderTarget);
		context.blackboard.wanderTime = wanderTime - context.deltaTime;

		return { success: true, completed: false };
	}

	onEnter(context: AIContext): void {
		print(`🚶 ${context.entityId} is wandering`);
	}

	private setNewWanderTarget(context: AIContext): void {
		const wanderRadius = 15;
		const randomAngle = math.random() * math.pi * 2;
		const randomDistance = math.random() * wanderRadius;
		
		const x = context.position.X + math.cos(randomAngle) * randomDistance;
		const z = context.position.Z + math.sin(randomAngle) * randomDistance;
		
		context.blackboard.wanderTarget = new Vector3(x, context.position.Y, z);
		context.blackboard.wanderTime = 5 + math.random() * 5; // 5-10 seconds
	}

	private moveTowards(context: AIContext, targetPosition: Vector3): void {
		if (context.entity.IsA("Model") && context.entity.PrimaryPart) {
			const humanoid = context.entity.FindFirstChild("Humanoid") as Humanoid;
			if (humanoid) {
				humanoid.MoveTo(targetPosition);
			}
		}
	}
}
