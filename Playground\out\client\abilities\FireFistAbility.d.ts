import { AbilityBase } from "./AbilityBase";
export declare class FireFistAbility extends AbilityBase {
    private fireEffects;
    private fireConnection?;
    private cooldownEndTime;
    private isActive;
    constructor();
    isOnCooldown(): boolean;
    private startCooldown;
    activate(): void;
    private createFireFistAnimation;
    private createCharacterAnimation;
    private animateR6Character;
    private animateR15Character;
    private createCharacterFireAura;
    private createFireParticles;
    private createFireFistCharge;
    private launchFireFist;
    private checkFireProjectileCollision;
    private createFireExplosion;
    private createFireRings;
    private createEnvironmentalFire;
    private createFireAtmosphere;
    private createFloatingFireOrbs;
    private createGroundFirePatches;
    private cleanupEffects;
}
