-- Compiled with roblox-ts v3.0.0
local Result
do
	Result = setmetatable({}, {
		__tostring = function()
			return "Result"
		end,
	})
	Result.__index = Result
	function Result.new(...)
		local self = setmetatable({}, Result)
		return self:constructor(...) or self
	end
	function Result:constructor(_value, _error, _isSuccess)
		if _isSuccess == nil then
			_isSuccess = true
		end
		self._value = _value
		self._error = _error
		self._isSuccess = _isSuccess
	end
	function Result:ok(value)
		return Result.new(value, nil, true)
	end
	function Result:err(errorValue)
		return Result.new(nil, errorValue, false)
	end
	function Result:isOk()
		return self._isSuccess
	end
	function Result:isError()
		return not self._isSuccess
	end
	function Result:getValue()
		if not self._isSuccess then
			error("Cannot access value of error result")
		end
		return self._value
	end
	function Result:getError()
		if self._isSuccess then
			error("Cannot access error of success result")
		end
		return self._error
	end
	function Result:map(fn)
		if self._isSuccess then
			return Result:ok(fn(self._value))
		end
		return Result:err(self._error)
	end
	function Result:mapError(fn)
		if self._isSuccess then
			return Result:ok(self._value)
		end
		return Result:err(fn(self._error))
	end
	function Result:flatMap(fn)
		if self._isSuccess then
			return fn(self._value)
		end
		return Result:err(self._error)
	end
	function Result:unwrapOr(defaultValue)
		return if self._isSuccess then self._value else defaultValue
	end
	function Result:unwrapOrElse(fn)
		return if self._isSuccess then self._value else fn(self._error)
	end
	function Result:match(onOk, onError)
		return if self._isSuccess then onOk(self._value) else onError(self._error)
	end
end
return {
	Result = Result,
}
