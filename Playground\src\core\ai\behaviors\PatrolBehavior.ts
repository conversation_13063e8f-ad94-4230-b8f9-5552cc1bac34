import { AIBehavior } from "../interfaces/AIBehavior";
import { AIContext } from "../interfaces/AIContext";
import { AIBehaviorResult } from "../interfaces/AIBehaviorResult";
import { PositionHelper } from "../../helper/PositionHelper";

export class PatrolBehavior implements AIBehavior {
	name = "Patrol";
	priority = 3;

	canExecute(context: AIContext): boolean {
		return !context.target;
	}

	execute(context: AIContext): AIBehaviorResult {
		const patrolPoints = context.blackboard.patrolPoints as Vector3[] || [];
		const currentPatrolIndex = context.blackboard.currentPatrolIndex as number || 0;
		
		if (patrolPoints.size() === 0) {
			this.generatePatrolPoints(context);
			return { success: true, completed: false };
		}

		const targetPoint = patrolPoints[currentPatrolIndex];
		const distance = context.position.sub(targetPoint).Magnitude;

		if (distance <= 5) {
			context.blackboard.currentPatrolIndex = (currentPatrolIndex + 1) % patrolPoints.size();
		} else {
			this.moveTowards(context, targetPoint);
		}

		return { success: true, completed: false };
	}

	onEnter(context: AIContext): void {
		print(`🚶 ${context.entityId} is patrolling`);
	}

	private generatePatrolPoints(context: AIContext): void {
		const patrolPoints: Vector3[] = [];
		const basePosition = context.position;
		const radius = 20;

		for (let i = 0; i < 4; i++) {
			const angle = (i / 4) * math.pi * 2;
			const x = basePosition.X + math.cos(angle) * radius;
			const z = basePosition.Z + math.sin(angle) * radius;
			patrolPoints.push(new Vector3(x, basePosition.Y, z));
		}

		context.blackboard.patrolPoints = patrolPoints;
		context.blackboard.currentPatrolIndex = 0;
	}

	private moveTowards(context: AIContext, targetPosition: Vector3): void {
		if (context.entity.IsA("Model") && context.entity.PrimaryPart) {
			const humanoid = context.entity.FindFirstChild("Humanoid") as Humanoid;
			if (humanoid) {
				humanoid.MoveTo(targetPosition);
			}
		}
	}
}
