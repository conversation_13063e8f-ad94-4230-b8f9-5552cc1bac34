import { BaseService } from "../foundation/BaseService";
import { Result } from "../foundation/types/Result";
import { EventName, PlayerId } from "../foundation/types/BrandedTypes";
import { Error } from "../foundation/types/RobloxError";
import { NetworkError } from "./errors/NetworkError";
export declare class NetworkValidationService extends BaseService {
    private rateLimitMap;
    private authenticatedPlayers;
    constructor();
    protected onInitialize(): Promise<Result<void, Error>>;
    protected onShutdown(): Promise<Result<void, Error>>;
    checkRateLimit(player: Player, eventName: EventName, rateLimit: {
        maxCalls: number;
        windowMs: number;
    }): Result<void, NetworkError>;
    validateAuthentication(player: Player): Result<void, NetworkError>;
    authenticatePlayer(playerId: PlayerId): void;
    deauthenticatePlayer(playerId: PlayerId): void;
    validatePlayerPosition(player: Player, reportedPosition: Vector3, maxDistance?: number): Result<void, NetworkError>;
    validatePlayerExists(playerId: PlayerId): Result<Player, NetworkError>;
    private startCleanupTask;
    private cleanupExpiredEntries;
}
