import * as React from "@rbxts/react";
interface LabelProps {
    text: string;
    textColor?: string;
    fontSize?: number;
    alignment?: Enum.TextXAlignment;
    size?: UDim2;
    position?: UDim2;
    anchorPoint?: Vector2;
    layoutOrder?: number;
    bold?: boolean;
    autoSize?: boolean;
    textWrapped?: boolean;
}
export declare function Label(props: LabelProps): React.ReactElement;
export {};
