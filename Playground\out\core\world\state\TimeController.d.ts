import { TimeOptions, TimeOfDay } from "./interfaces/TimeOptions";
/**
 * TimeController - Manages day/night cycle and time of day
 * Provides simple methods for changing time with smooth transitions
 */
export declare class TimeController {
    private static instance;
    private currentTimeOfDay;
    private activeTweens;
    private constructor();
    static getInstance(): TimeController;
    /**
     * Set time of day with smooth transition
     */
    setTime(options: TimeOptions): void;
    /**
     * Get current time of day
     */
    getCurrentTimeOfDay(): TimeOfDay;
    /**
     * Get current clock time (0-24)
     */
    getCurrentClockTime(): number;
    private getClockTimeForTimeOfDay;
    private tweenToTime;
    private tweenLightingForTimeOfDay;
    /**
     * Start automatic day/night cycle
     */
    startDayNightCycle(cycleDuration?: number): void;
    /**
     * Stop automatic day/night cycle
     */
    stopDayNightCycle(): void;
    private clearActiveTweens;
    /**
     * Cleanup all time effects
     */
    cleanup(): void;
}
