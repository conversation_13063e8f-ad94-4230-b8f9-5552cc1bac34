-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local _design = TS.import(script, game:GetService("ReplicatedStorage"), "core", "design")
local COLORS = _design.COLORS
local SIZES = _design.SIZES
local TYPOGRAPHY = _design.TYPOGRAPHY
local BORDER_RADIUS = _design.BORDER_RADIUS
local function Input(props)
	local _condition = props.value
	if _condition == nil then
		_condition = ""
	end
	local text, setText = React.useState(_condition)
	local textBoxRef = React.useRef()
	-- Update internal state when value prop changes
	React.useEffect(function()
		if props.value ~= nil then
			setText(props.value)
		end
	end, { props.value })
	React.useEffect(function()
		local tb = textBoxRef.current
		if tb then
			local conn = tb:GetPropertyChangedSignal("Text"):Connect(function()
				local newText = tb.Text
				setText(newText)
				props.onChange(newText)
			end)
			-- Return cleanup function to disconnect when component unmounts
			return function()
				return conn:Disconnect()
			end
		end
	end, {})
	-- Note: TextBox selection styling properties are not available in Roblox
	-- SelectionColor3 and SelectionTransparency are not valid properties for TextBox
	-- Apply customizable properties with defaults
	local _condition_1 = props.placeholder
	if _condition_1 == nil then
		_condition_1 = "Enter text..."
	end
	local placeholder = _condition_1
	local size = props.size or UDim2.new(0, SIZES.input.width, 0, SIZES.input.height)
	local _condition_2 = props.textColor
	if _condition_2 == nil then
		_condition_2 = COLORS.text.main
	end
	local textColor = _condition_2
	local _condition_3 = props.backgroundColor
	if _condition_3 == nil then
		_condition_3 = COLORS.bg.base
	end
	local backgroundColor = _condition_3
	local _condition_4 = props.borderColor
	if _condition_4 == nil then
		_condition_4 = COLORS.border.l2
	end
	local borderColor = _condition_4
	local _condition_5 = props.placeholderColor
	if _condition_5 == nil then
		_condition_5 = COLORS.text.secondary
	end
	local placeholderColor = _condition_5
	local _condition_6 = props.clearTextOnFocus
	if _condition_6 == nil then
		_condition_6 = false
	end
	local clearTextOnFocus = _condition_6
	local _condition_7 = props.multiline
	if _condition_7 == nil then
		_condition_7 = false
	end
	local multiline = _condition_7
	return React.createElement("textbox", {
		ref = textBoxRef,
		PlaceholderText = placeholder,
		PlaceholderColor3 = Color3.fromHex(placeholderColor),
		Text = text,
		TextColor3 = Color3.fromHex(textColor),
		BackgroundColor3 = Color3.fromHex(backgroundColor),
		Size = size,
		Position = props.position,
		AnchorPoint = props.anchorPoint,
		LayoutOrder = props.layoutOrder,
		Font = TYPOGRAPHY.font,
		TextSize = SIZES.fontSize,
		ClearTextOnFocus = clearTextOnFocus,
		TextWrapped = multiline,
		MultiLine = multiline,
		BorderSizePixel = 0,
		Active = not props.disabled,
		TextEditable = not props.disabled,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, BORDER_RADIUS.md),
	}), React.createElement("uistroke", {
		Color = Color3.fromHex(borderColor),
		Thickness = 1,
		Transparency = if props.disabled then 0.7 else 0,
	}))
end
return {
	Input = Input,
}
