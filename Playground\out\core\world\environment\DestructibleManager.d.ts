import { DestructionOptions, DestructibleZone } from "./interfaces/DestructionOptions";
export declare class DestructibleManager {
    private static activeZones;
    private static zoneCounter;
    /**
     * Create a destructible zone that affects the environment
     * Perfect for Whitebeard earthquake effects, explosions, etc.
     */
    static createDestructibleZone(options: DestructionOptions): string;
    /**
     * Apply destruction effects to parts in the zone
     */
    private static applyDestruction;
    /**
     * Apply specific destruction effects to a single part
     */
    private static applyDestructionToPart;
    /**
     * Apply earthquake-style destruction (perfect for Whitebeard)
     */
    private static applyEarthquakeEffect;
    /**
     * Apply explosion-style destruction
     */
    private static applyExplosionEffect;
    /**
     * Apply freeze effect (for Ice Age ability)
     */
    private static applyFreezeEffect;
    /**
     * Create visual cracks on a part
     */
    private static createCracksOnPart;
    /**
     * Create debris from destroyed parts
     */
    private static createDebris;
    /**
     * Create frost visual effect
     */
    private static createFrostEffect;
    /**
     * Create visual effects at a position
     */
    private static createVisualEffect;
    /**
     * Find all parts within a radius
     */
    private static findPartsInRadius;
    /**
     * Capture the current state of a part
     */
    private static capturePartState;
    /**
     * Restore a destructible zone to its original state
     */
    static restoreZone(zoneId: string): void;
    /**
     * Get all active destructible zones
     */
    static getActiveZones(): Map<string, DestructibleZone>;
    /**
     * Clean up expired zones
     */
    static cleanupExpiredZones(): void;
    private static applySlashEffect;
    private static applyImpactEffect;
    private static applyBurnEffect;
}
