import { WeatherController } from "./WeatherController";
import { TimeController } from "./TimeController";
import { GravityController } from "./GravityController";
import { WorldStateOptions } from "./interfaces/WorldStateUpdate";
import { WorldStateEvent } from "./interfaces/WorldStateEvent";
/**
 * WorldStateManager - Central manager for all world environmental states
 * Provides a unified interface for weather, time, gravity, and atmosphere management
 */
export declare class WorldStateManager {
    private static instance;
    private weatherController;
    private timeController;
    private gravityController;
    private eventListeners;
    private constructor();
    static getInstance(): WorldStateManager;
    /**
     * Apply multiple world state changes at once
     */
    applyWorldState(options: WorldStateOptions, playerId?: number): void;
    /**
     * Get current state of all world systems
     */
    getCurrentState(): WorldStateOptions;
    getWeatherController(): WeatherController;
    getTimeController(): TimeController;
    getGravityController(): GravityController;
    /**
     * Quick preset methods for common world states
     */
    setStormyNight(): void;
    setSunnyDay(): void;
    setWinterStorm(): void;
    setLowGravityDawn(): void;
    setSpaceMode(): void;
    /**
     * Event system for listening to world state changes
     */
    addEventListener(listener: (event: WorldStateEvent) => void): void;
    removeEventListener(listener: (event: WorldStateEvent) => void): void;
    private emitEvent;
    /**
     * Reset all world systems to default state
     */
    resetToDefaults(): void;
    /**
     * Cleanup all world state systems
     */
    cleanup(): void;
    /**
     * Initialize the world state manager
     */
    static initialize(): WorldStateManager;
}
